"""
Ella命令执行器
负责执行各种类型的命令（文本、语音等）
"""
import time
from core.logger import log


class EllaCommandExecutor:
    """Ella命令执行器"""
    
    def __init__(self, driver=None, page_elements=None):
        """
        初始化命令执行器
        
        Args:
            driver: UIAutomator2驱动实例
            page_elements: 页面元素字典
        """
        self.driver = driver
        self.page_elements = page_elements or {}
    
    def execute_text_command(self, command: str) -> bool:
        """
        执行文本命令（输入+发送）
        
        Args:
            command: 要执行的命令
            
        Returns:
            bool: 执行是否成功
        """
        try:
            log.info(f"执行文本命令: {command}")
            
            if not self.driver:
                log.error("驱动实例未初始化")
                return False
            
            # 1. 确保输入框可用
            if not self._ensure_input_box_ready():
                log.error("无法确保输入框就绪")
                return False
            
            # 2. 输入命令
            if not self._input_text_command(command):
                return False
            
            # 3. 发送命令
            if not self._send_command():
                return False
            
            log.info("✅ 文本命令执行完成")
            return True
            
        except Exception as e:
            log.error(f"执行文本命令失败: {e}")
            return False
    
    def execute_voice_command(self, command: str, duration: float = 3.0, language: str = 'zh-CN') -> bool:
        """
        执行语音命令
        
        Args:
            command: 要执行的命令
            duration: 语音持续时间
            language: 语言代码
            
        Returns:
            bool: 执行是否成功
        """
        try:
            log.info(f"🎤 执行语音命令: '{command}' (语言: {language}, 持续时间: {duration}秒)")
            
            if not self.driver:
                log.error("驱动实例未初始化")
                return False
            
            # 1. 确保输入框可用
            if not self._ensure_input_box_ready():
                log.error("无法确保输入框就绪")
                return False
            
            # 2. 启动语音输入
            if not self._start_voice_input():
                log.warning("无法启动语音输入，回退到文本输入")
                return self.execute_text_command(command)
            
            # 3. 等待语音录制状态稳定
            log.info("等待语音录制状态稳定...")
            time.sleep(1.0)
            
            # 4. 播放语音命令文件
            log.info(f"🔊 播放语音命令: '{command}'")
            voice_success = self._play_voice_command_file(command, language)
            
            if not voice_success:
                log.warning("语音文件播放失败，回退到文本输入")
                # 先停止语音输入
                self._stop_voice_input()
                return self.execute_text_command(command)
            
            # 5. 等待语音识别完成
            time.sleep(duration)
            
            # 6. 停止语音输入
            if not self._stop_voice_input():
                log.warning("停止语音输入失败，但继续执行")
            
            log.info("✅ 语音命令执行完成")
            return True
            
        except Exception as e:
            log.error(f"执行语音命令失败: {e}")
            # 尝试停止语音输入
            try:
                self._stop_voice_input()
            except:
                pass
            return False
    
    def _ensure_input_box_ready(self) -> bool:
        """
        确保输入框就绪
        
        Returns:
            bool: 输入框是否就绪
        """
        try:
            log.info("确保输入框就绪...")
            
            if not self.driver:
                return False
            
            # 检查已知的输入元素
            if self._check_known_input_elements():
                return True
            
            # 检查通用输入元素
            if self._check_generic_input_elements():
                return True
            
            # 尝试通过坐标激活输入
            if self._activate_input_by_coordinates():
                return True
            
            log.error("❌ 无法确保输入框就绪")
            return False
            
        except Exception as e:
            log.error(f"确保输入框就绪失败: {e}")
            return False
    
    def _check_known_input_elements(self) -> bool:
        """检查已知的输入元素"""
        try:
            # 检查主输入框
            input_box = self.page_elements.get('input_box')
            if input_box and input_box.is_exists():
                log.info("✅ 找到主输入框")
                return True
            
            # 检查备选输入框
            text_input_box = self.page_elements.get('text_input_box')
            if text_input_box and text_input_box.is_exists():
                log.info("✅ 找到备选输入框")
                return True
            
            return False
        except Exception as e:
            log.debug(f"检查已知输入元素失败: {e}")
            return False
    
    def _check_generic_input_elements(self) -> bool:
        """检查通用输入元素"""
        try:
            # 查找EditText元素
            edit_texts = self.driver(className="android.widget.EditText")
            if edit_texts.exists():
                log.info("✅ 找到EditText元素")
                return True
            
            return False
        except Exception as e:
            log.debug(f"检查通用输入元素失败: {e}")
            return False
    
    def _activate_input_by_coordinates(self) -> bool:
        """通过坐标激活输入"""
        try:
            # 获取屏幕尺寸
            width, height = self.driver.window_size()
            
            # 尝试点击屏幕下方中央（通常是输入框位置）
            input_x = width // 2
            input_y = int(height * 0.85)  # 屏幕下方85%位置
            
            log.info(f"尝试点击坐标激活输入: ({input_x}, {input_y})")
            self.driver.click(input_x, input_y)
            time.sleep(1)
            
            # 检查是否激活了输入
            return self._verify_input_activated()
            
        except Exception as e:
            log.debug(f"坐标激活输入失败: {e}")
            return False
    
    def _verify_input_activated(self) -> bool:
        """验证输入是否已激活"""
        try:
            # 检查是否有键盘出现或输入框获得焦点
            # 这里可以添加更多的验证逻辑
            return True  # 简化实现
        except Exception as e:
            log.debug(f"验证输入激活失败: {e}")
            return False
    
    def _input_text_command(self, command: str) -> bool:
        """
        输入文本命令
        
        Args:
            command: 要输入的命令文本
            
        Returns:
            bool: 输入是否成功
        """
        try:
            log.info(f"输入文本命令: {command}")
            
            # 清空输入框
            self._clear_input_box()
            
            # 尝试多种输入方法
            input_methods = [
                self._input_via_known_elements,
                self._input_via_generic_elements,
                self._input_via_coordinates
            ]
            
            for method in input_methods:
                try:
                    if method(command):
                        # 验证输入是否成功
                        if self._verify_input_text(command):
                            log.info("✅ 文本输入成功")
                            return True
                except Exception as e:
                    log.debug(f"输入方法失败: {e}")
                    continue
            
            log.error("❌ 所有输入方法都失败")
            return False
            
        except Exception as e:
            log.error(f"输入文本命令失败: {e}")
            return False
    
    def _input_via_known_elements(self, command: str) -> bool:
        """通过已知元素输入"""
        input_box = self.page_elements.get('input_box')
        if input_box and input_box.is_exists():
            input_box.send_keys(command)
            return True
        return False
    
    def _input_via_generic_elements(self, command: str) -> bool:
        """通过通用元素输入"""
        edit_texts = self.driver(className="android.widget.EditText")
        if edit_texts.exists():
            edit_texts.send_keys(command)
            return True
        return False
    
    def _input_via_coordinates(self, command: str) -> bool:
        """通过坐标输入"""
        try:
            # 先点击激活输入框
            width, height = self.driver.window_size()
            input_x = width // 2
            input_y = int(height * 0.85)
            
            self.driver.click(input_x, input_y)
            time.sleep(0.5)
            
            # 使用系统输入法输入
            self.driver.send_keys(command)
            return True
        except Exception:
            return False
    
    def _clear_input_box(self):
        """清空输入框"""
        try:
            # 尝试清空已知的输入框
            input_box = self.page_elements.get('input_box')
            if input_box and input_box.is_exists():
                input_box.clear_text()
                return
            
            # 尝试清空通用输入框
            edit_texts = self.driver(className="android.widget.EditText")
            if edit_texts.exists():
                edit_texts.clear_text()
                
        except Exception as e:
            log.debug(f"清空输入框失败: {e}")
    
    def _verify_input_text(self, expected_text: str) -> bool:
        """验证输入的文本"""
        try:
            # 简化实现，实际可以检查输入框内容
            return True
        except Exception:
            return False
    
    def _send_command(self) -> bool:
        """发送命令"""
        try:
            log.info("发送命令")
            
            # 尝试点击发送按钮
            send_button = self.page_elements.get('send_button')
            locator = send_button.locator
            log.info(locator)
            log.info(send_button)
            log.info(send_button.is_exists())
            if send_button and send_button.is_exists():
                send_button.click()
                log.info("✅ 点击发送按钮成功")
                return True
            
            # 尝试按回车键
            self.driver.press("enter")
            log.info("✅ 按回车键发送成功")
            return True
            
        except Exception as e:
            log.error(f"发送命令失败: {e}")
            return False
    
    def _start_voice_input(self) -> bool:
        """启动语音输入"""
        try:
            log.info("启动语音输入")
            
            # 尝试点击语音按钮
            voice_button = self.page_elements.get('voice_input_button')
            if voice_button and voice_button.is_exists():
                voice_button.click()
                time.sleep(1)
                return True
            
            return False
            
        except Exception as e:
            log.error(f"启动语音输入失败: {e}")
            return False
    
    def _stop_voice_input(self) -> bool:
        """停止语音输入"""
        try:
            log.info("停止语音输入")
            
            # 尝试再次点击语音按钮停止录制
            voice_button = self.page_elements.get('voice_input_button')
            if voice_button and voice_button.is_exists():
                voice_button.click()
                time.sleep(1)
                return True
            
            return False
            
        except Exception as e:
            log.error(f"停止语音输入失败: {e}")
            return False
    
    def _play_voice_command_file(self, command: str, language: str) -> bool:
        """
        播放语音命令文件
        
        Args:
            command: 命令文本
            language: 语言代码
            
        Returns:
            bool: 播放是否成功
        """
        try:
            # 这里可以集成TTS功能播放语音文件
            # 简化实现，直接返回True
            log.info(f"模拟播放语音命令: {command} ({language})")
            time.sleep(1)  # 模拟播放时间
            return True
            
        except Exception as e:
            log.error(f"播放语音命令文件失败: {e}")
            return False
