"""
相册清理混入类
为测试用例提供相册清理功能
"""
import pytest
import allure
from typing import List, Dict, Optional
from core.logger import log
from tools.gallery_cleaner import gallery_cleaner
from tools.force_gallery_cleaner import force_gallery_cleaner


class GalleryCleanupMixin:
    """相册清理混入类，为测试用例提供相册清理功能"""

    @pytest.fixture(scope="function", autouse=False)
    def clean_gallery_before_test(self):
        """测试前清理相册的fixture"""
        log.info("🧹 测试前清理相册...")
        
        with allure.step("清理相册文件夹"):
            # 清理主要的相册文件夹
            results = gallery_cleaner.clear_all_gallery_folders(['images', 'videos'])
            
            # 记录清理结果
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            log.info(f"相册清理完成: {success_count}/{total_count} 个文件夹成功")
            
            # 附加清理结果到测试报告
            cleanup_summary = self._create_cleanup_summary(results)
            allure.attach(cleanup_summary, name="相册清理结果", attachment_type=allure.attachment_type.TEXT)
        
        yield
        
        # 测试后可以选择是否再次清理
        # log.info("🧹 测试后清理相册...")
        # gallery_cleaner.clear_all_gallery_folders(['images', 'videos'])

    @pytest.fixture(scope="function", autouse=False)
    def clean_camera_only(self):
        """仅清理相机文件夹的fixture"""
        log.info("📷 清理相机文件夹...")
        
        with allure.step("清理相机文件夹"):
            success = gallery_cleaner.clear_gallery_folder('camera', ['images', 'videos'])
            
            if success:
                log.info("✅ 相机文件夹清理成功")
            else:
                log.warning("⚠️ 相机文件夹清理失败")
            
            allure.attach(
                f"相机文件夹清理结果: {'成功' if success else '失败'}", 
                name="相机清理结果", 
                attachment_type=allure.attachment_type.TEXT
            )
        
        yield

    @pytest.fixture(scope="function", autouse=False)
    def clean_download_only(self):
        """仅清理下载文件夹的fixture"""
        log.info("📥 清理下载文件夹...")
        
        with allure.step("清理下载文件夹"):
            success = gallery_cleaner.clear_gallery_folder('download')
            
            if success:
                log.info("✅ 下载文件夹清理成功")
            else:
                log.warning("⚠️ 下载文件夹清理失败")
            
            allure.attach(
                f"下载文件夹清理结果: {'成功' if success else '失败'}", 
                name="下载清理结果", 
                attachment_type=allure.attachment_type.TEXT
            )
        
        yield

    def clean_gallery_folders(self, folder_types: List[str] = None, file_types: List[str] = None) -> Dict[str, bool]:
        """
        清理指定的相册文件夹

        Args:
            folder_types: 要清理的文件夹类型列表，如 ['camera', 'pictures']，为None时清理所有
            file_types: 要清理的文件类型列表，如 ['images', 'videos']，为None时清理所有媒体文件

        Returns:
            Dict[str, bool]: 每个文件夹的清理结果
        """
        try:
            log.info(f"🧹 开始清理相册文件夹: {folder_types or '全部'}")
            
            with allure.step(f"清理相册文件夹: {folder_types or '全部'}"):
                if folder_types is None:
                    # 清理所有文件夹
                    results = gallery_cleaner.clear_all_gallery_folders(file_types)
                else:
                    # 清理指定文件夹
                    results = {}
                    for folder_type in folder_types:
                        results[folder_type] = gallery_cleaner.clear_gallery_folder(folder_type, file_types)
                
                # 统计结果
                success_count = sum(1 for success in results.values() if success)
                total_count = len(results)
                
                log.info(f"相册清理完成: {success_count}/{total_count} 个文件夹成功")
                
                # 附加清理结果到测试报告
                cleanup_summary = self._create_cleanup_summary(results)
                allure.attach(cleanup_summary, name="相册清理结果", attachment_type=allure.attachment_type.TEXT)
                
                return results

        except Exception as e:
            log.error(f"清理相册文件夹失败: {e}")
            allure.attach(f"清理失败: {str(e)}", name="清理错误", attachment_type=allure.attachment_type.TEXT)
            return {}

    def clean_custom_gallery_path(self, custom_path: str, file_types: List[str] = None) -> bool:
        """
        清理自定义相册路径

        Args:
            custom_path: 自定义路径
            file_types: 要清理的文件类型列表

        Returns:
            bool: 清理是否成功
        """
        try:
            log.info(f"🧹 清理自定义路径: {custom_path}")
            
            with allure.step(f"清理自定义路径: {custom_path}"):
                success = gallery_cleaner.clear_custom_path(custom_path, file_types)
                
                result_text = f"自定义路径清理结果: {'成功' if success else '失败'}\n路径: {custom_path}"
                allure.attach(result_text, name="自定义路径清理结果", attachment_type=allure.attachment_type.TEXT)
                
                return success

        except Exception as e:
            log.error(f"清理自定义路径失败: {e}")
            allure.attach(f"清理失败: {str(e)}", name="清理错误", attachment_type=allure.attachment_type.TEXT)
            return False

    def get_gallery_status(self) -> Dict[str, Dict]:
        """
        获取相册状态

        Returns:
            Dict[str, Dict]: 相册状态信息
        """
        try:
            log.info("📊 获取相册状态...")
            
            with allure.step("获取相册状态"):
                status = gallery_cleaner.get_gallery_status()
                
                # 创建状态报告
                status_summary = self._create_status_summary(status)
                allure.attach(status_summary, name="相册状态", attachment_type=allure.attachment_type.TEXT)
                
                return status

        except Exception as e:
            log.error(f"获取相册状态失败: {e}")
            allure.attach(f"获取状态失败: {str(e)}", name="状态错误", attachment_type=allure.attachment_type.TEXT)
            return {}

    def ensure_clean_gallery(self, folder_types: List[str] = None) -> bool:
        """
        确保相册是干净的（测试前准备）

        Args:
            folder_types: 要确保清理的文件夹类型列表

        Returns:
            bool: 是否成功确保相册干净
        """
        try:
            log.info("🎯 确保相册环境干净...")
            
            with allure.step("确保相册环境干净"):
                # 获取当前状态
                status_before = self.get_gallery_status()
                
                # 检查是否需要清理
                needs_cleanup = False
                for folder_type, info in status_before.items():
                    if folder_types is None or folder_type in folder_types:
                        if info.get('file_count', 0) > 0:
                            needs_cleanup = True
                            break
                
                if not needs_cleanup:
                    log.info("✅ 相册已经是干净的，无需清理")
                    return True
                
                # 执行清理
                results = self.clean_gallery_folders(folder_types, ['images', 'videos'])
                
                # 验证清理结果
                all_success = all(results.values())
                
                if all_success:
                    log.info("✅ 相册环境准备完成")
                else:
                    log.warning("⚠️ 部分相册清理失败")
                
                return all_success

        except Exception as e:
            log.error(f"确保相册干净失败: {e}")
            return False

    def _create_cleanup_summary(self, results: Dict[str, bool], cleanup_type: str = "清理") -> str:
        """创建清理结果摘要"""
        summary_lines = [f"相册{cleanup_type}结果摘要", "=" * 30]

        for folder_type, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            summary_lines.append(f"{folder_type}: {status}")

        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        summary_lines.append(f"\n总计: {success_count}/{total_count} 个文件夹成功")

        return "\n".join(summary_lines)

    def _create_status_summary(self, status: Dict[str, Dict]) -> str:
        """创建状态摘要"""
        summary_lines = ["相册状态摘要", "=" * 30]
        
        total_files = 0
        for folder_type, info in status.items():
            exists = "存在" if info.get('exists', False) else "不存在"
            file_count = info.get('file_count', 0)
            total_files += file_count
            
            summary_lines.append(f"{folder_type}: {exists}, {file_count} 个文件")
        
        summary_lines.append(f"\n总文件数: {total_files}")
        
        return "\n".join(summary_lines)

    # 便捷方法
    def clean_camera_folder(self) -> bool:
        """清理相机文件夹"""
        return gallery_cleaner.clear_gallery_folder('camera', ['images', 'videos'])

    def clean_pictures_folder(self) -> bool:
        """清理图片文件夹"""
        return gallery_cleaner.clear_gallery_folder('pictures', ['images', 'videos'])

    def clean_download_folder(self) -> bool:
        """清理下载文件夹"""
        return gallery_cleaner.clear_gallery_folder('download')

    def clean_screenshots_folder(self) -> bool:
        """清理截图文件夹"""
        return gallery_cleaner.clear_gallery_folder('screenshots', ['images'])

    def clean_all_media_folders(self) -> Dict[str, bool]:
        """清理所有媒体文件夹"""
        return gallery_cleaner.clear_all_gallery_folders(['images', 'videos', 'audio'])

    def force_clean_gallery_folders(self, folder_types: List[str] = None) -> Dict[str, bool]:
        """
        强力清理相册文件夹（当普通清理失效时使用）

        Args:
            folder_types: 要清理的文件夹类型列表，为None时清理所有

        Returns:
            Dict[str, bool]: 每个文件夹的清理结果
        """
        try:
            log.info(f"🔥 开始强力清理相册文件夹: {folder_types or '全部'}")

            with allure.step(f"强力清理相册文件夹: {folder_types or '全部'}"):
                if folder_types is None:
                    # 强力清理所有文件夹
                    results = force_gallery_cleaner.force_clear_all_gallery_folders()
                else:
                    # 强力清理指定文件夹
                    results = {}
                    for folder_type in folder_types:
                        results[folder_type] = force_gallery_cleaner.force_clear_gallery_folder(folder_type)

                # 统计结果
                success_count = sum(1 for success in results.values() if success)
                total_count = len(results)

                log.info(f"强力清理完成: {success_count}/{total_count} 个文件夹成功")

                # 附加清理结果到测试报告
                cleanup_summary = self._create_cleanup_summary(results, "强力清理")
                allure.attach(cleanup_summary, name="强力清理结果", attachment_type=allure.attachment_type.TEXT)

                return results

        except Exception as e:
            log.error(f"强力清理相册文件夹失败: {e}")
            allure.attach(f"强力清理失败: {str(e)}", name="强力清理错误", attachment_type=allure.attachment_type.TEXT)
            return {}

    def force_clean_camera_folder(self) -> bool:
        """强力清理相机文件夹"""
        return force_gallery_cleaner.force_clear_gallery_folder('camera')

    def force_clean_pictures_folder(self) -> bool:
        """强力清理图片文件夹"""
        return force_gallery_cleaner.force_clear_gallery_folder('pictures')

    def force_clean_download_folder(self) -> bool:
        """强力清理下载文件夹"""
        return force_gallery_cleaner.force_clear_gallery_folder('download')


# 使用示例的测试基类
class BaseGalleryTest(GalleryCleanupMixin):
    """包含相册清理功能的测试基类"""
    
    def setup_method(self):
        """测试方法设置"""
        log.info("🚀 开始测试，准备相册环境...")
        # 可以在这里调用相册清理
        # self.ensure_clean_gallery()
    
    def teardown_method(self):
        """测试方法清理"""
        log.info("🏁 测试完成，清理相册环境...")
        # 可以在这里调用相册清理
        # self.clean_all_media_folders()
