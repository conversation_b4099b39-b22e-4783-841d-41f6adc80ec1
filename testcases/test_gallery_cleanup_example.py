"""
相册清理功能使用示例
展示如何在测试用例中使用相册清理功能
"""
import pytest
import allure
from testcases.mixins.gallery_cleanup_mixin import BaseGalleryTest
from core.logger import log


class TestGalleryCleanupExample(BaseGalleryTest):
    """相册清理功能使用示例测试类"""

    @allure.title("测试相册清理功能 - 使用fixture自动清理")
    @allure.description("演示如何使用fixture在测试前自动清理相册")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.gallery
    def test_with_auto_cleanup_fixture(self, clean_gallery_before_test):
        """使用fixture自动清理相册的测试示例"""
        
        with allure.step("验证相册已被清理"):
            # 获取相册状态
            status = self.get_gallery_status()
            
            # 验证主要文件夹是否为空
            camera_files = status.get('camera', {}).get('file_count', 0)
            pictures_files = status.get('pictures', {}).get('file_count', 0)
            
            log.info(f"相机文件夹文件数: {camera_files}")
            log.info(f"图片文件夹文件数: {pictures_files}")
            
            # 这里可以添加具体的验证逻辑
            # assert camera_files == 0, f"相机文件夹应该为空，但有 {camera_files} 个文件"
            # assert pictures_files == 0, f"图片文件夹应该为空，但有 {pictures_files} 个文件"
        
        with allure.step("执行测试逻辑"):
            # 这里是你的实际测试逻辑
            log.info("✅ 在干净的相册环境中执行测试")

    @allure.title("测试相册清理功能 - 手动清理特定文件夹")
    @allure.description("演示如何手动清理特定的相册文件夹")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.gallery
    def test_manual_cleanup_specific_folders(self):
        """手动清理特定文件夹的测试示例"""
        
        with allure.step("获取清理前的相册状态"):
            status_before = self.get_gallery_status()
            log.info("清理前相册状态已获取")
        
        with allure.step("清理相机和图片文件夹"):
            # 只清理相机和图片文件夹的图片和视频
            results = self.clean_gallery_folders(
                folder_types=['camera', 'pictures'], 
                file_types=['images', 'videos']
            )
            
            # 验证清理结果
            assert results.get('camera', False), "相机文件夹清理失败"
            assert results.get('pictures', False), "图片文件夹清理失败"
            
            log.info("✅ 指定文件夹清理成功")
        
        with allure.step("验证清理效果"):
            status_after = self.get_gallery_status()
            
            camera_files_after = status_after.get('camera', {}).get('file_count', 0)
            pictures_files_after = status_after.get('pictures', {}).get('file_count', 0)
            
            log.info(f"清理后相机文件夹文件数: {camera_files_after}")
            log.info(f"清理后图片文件夹文件数: {pictures_files_after}")

    @allure.title("测试相册清理功能 - 清理自定义路径")
    @allure.description("演示如何清理自定义路径")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.gallery
    def test_cleanup_custom_path(self):
        """清理自定义路径的测试示例"""
        
        custom_path = "/sdcard/MyCustomFolder"
        
        with allure.step(f"清理自定义路径: {custom_path}"):
            # 清理自定义路径
            success = self.clean_custom_gallery_path(
                custom_path=custom_path,
                file_types=['images', 'videos', 'documents']
            )
            
            # 注意：如果路径不存在，清理也会返回成功
            log.info(f"自定义路径清理结果: {'成功' if success else '失败'}")

    @allure.title("测试相册清理功能 - 使用便捷方法")
    @allure.description("演示如何使用便捷的清理方法")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.gallery
    def test_convenience_methods(self):
        """使用便捷方法的测试示例"""
        
        with allure.step("使用便捷方法清理各个文件夹"):
            # 使用便捷方法清理不同文件夹
            camera_result = self.clean_camera_folder()
            pictures_result = self.clean_pictures_folder()
            download_result = self.clean_download_folder()
            screenshots_result = self.clean_screenshots_folder()
            
            log.info(f"相机文件夹清理: {'成功' if camera_result else '失败'}")
            log.info(f"图片文件夹清理: {'成功' if pictures_result else '失败'}")
            log.info(f"下载文件夹清理: {'成功' if download_result else '失败'}")
            log.info(f"截图文件夹清理: {'成功' if screenshots_result else '失败'}")

    @allure.title("测试相册清理功能 - 确保环境干净")
    @allure.description("演示如何确保测试环境的相册是干净的")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.gallery
    def test_ensure_clean_environment(self):
        """确保环境干净的测试示例"""
        
        with allure.step("确保相册环境干净"):
            # 确保指定文件夹是干净的
            success = self.ensure_clean_gallery(['camera', 'pictures', 'download'])
            
            assert success, "无法确保相册环境干净"
            log.info("✅ 相册环境已确保干净")
        
        with allure.step("在干净环境中执行测试"):
            # 在确保干净的环境中执行测试逻辑
            log.info("在干净的相册环境中执行测试逻辑")
            
            # 这里可以添加需要干净相册环境的测试逻辑
            # 例如：拍照测试、文件下载测试等

    @allure.title("测试相册清理功能 - 完整的清理流程")
    @allure.description("演示完整的相册清理流程")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.gallery
    def test_complete_cleanup_workflow(self):
        """完整清理流程的测试示例"""
        
        with allure.step("步骤1: 获取初始状态"):
            initial_status = self.get_gallery_status()
            
            total_files_before = sum(
                info.get('file_count', 0) 
                for info in initial_status.values()
            )
            log.info(f"清理前总文件数: {total_files_before}")
        
        with allure.step("步骤2: 执行全面清理"):
            # 清理所有媒体文件夹
            results = self.clean_all_media_folders()
            
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            log.info(f"清理结果: {success_count}/{total_count} 个文件夹成功")
        
        with allure.step("步骤3: 验证清理效果"):
            final_status = self.get_gallery_status()
            
            total_files_after = sum(
                info.get('file_count', 0) 
                for info in final_status.values()
            )
            log.info(f"清理后总文件数: {total_files_after}")
            
            # 验证清理效果
            files_removed = total_files_before - total_files_after
            log.info(f"共删除 {files_removed} 个文件")
        
        with allure.step("步骤4: 生成清理报告"):
            # 创建详细的清理报告
            report_lines = [
                "相册清理报告",
                "=" * 40,
                f"清理前总文件数: {total_files_before}",
                f"清理后总文件数: {total_files_after}",
                f"删除文件数: {files_removed}",
                f"成功清理文件夹: {success_count}/{total_count}",
                "",
                "详细结果:"
            ]
            
            for folder_type, success in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                report_lines.append(f"  {folder_type}: {status}")
            
            report = "\n".join(report_lines)
            allure.attach(report, name="清理报告", attachment_type=allure.attachment_type.TEXT)
            
            log.info("✅ 完整清理流程执行完成")


# 使用fixture的测试类示例
class TestWithFixtures(BaseGalleryTest):
    """使用不同fixture的测试示例"""

    @pytest.mark.gallery
    def test_with_camera_cleanup(self, clean_camera_only):
        """仅清理相机文件夹的测试"""
        log.info("执行需要干净相机文件夹的测试")

    @pytest.mark.gallery  
    def test_with_download_cleanup(self, clean_download_only):
        """仅清理下载文件夹的测试"""
        log.info("执行需要干净下载文件夹的测试")


if __name__ == "__main__":
    # 运行测试示例
    pytest.main([
        __file__,
        "-v",
        "--allure-results-dir=reports/allure-results",
        "-m", "gallery"
    ])
