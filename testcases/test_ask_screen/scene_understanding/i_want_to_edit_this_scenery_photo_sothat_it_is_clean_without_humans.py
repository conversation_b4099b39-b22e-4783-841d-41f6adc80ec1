import pytest
import allure
from testcases.test_ask_screen.base_ask_screen_test import SimpleAskScreenTest
from pages.apps.ai_gallery.photos_page import AiGalleryPhotosPage
from core.logger import log

@allure.epic("Ella浮窗测试")
@allure.feature("Ask Screen功能")
@allure.story("场景理解")
class TestAskScreenIWantEditSceneryPhotoSothatItIsCleanWithoutHumans(SimpleAskScreenTest):
    """
    Ask Screen测试类 - 场景理解
    命令: i want to edit this scenery photo sothat it is clean without humans
    图片: i_want_to_edit_this_scenery_photo_sothat_it_is_clean_without_humans
    """

    @allure.title("测试i want to edit this scenery photo sothat it is clean without humans")
    @allure.description("测试Ask Screen功能: i want to edit this scenery photo sothat it is clean without humans")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_i_want_to_edit_this_scenery_photo_sothat_it_is_clean_without_humans(self, ella_floating_page):
        """测试i want to edit this scenery photo sothat it is clean without humans命令"""
        command = "i want to edit this scenery photo sothat it is clean without humans"
        expected_keywords = ['Done', 'The image is too small. Removal is currently not supported']

        # 数据准备
        with allure.step("准备测试数据"):
            from tools.file_pusher import file_pusher
            # 推送测试图片到设备
            push_result = file_pusher.push_ask_screen_image("i_want_to_edit_this_scenery_photo_sothat_it_is_clean_without_humans")
            assert push_result, f"推送图片失败: i_want_to_edit_this_scenery_photo_sothat_it_is_clean_without_humans"

            # 打开图库并选择图片
            photos_page = AiGalleryPhotosPage()
            result = photos_page.start_app()
            if result:
                photos_page.wait_for_page_load()
                photos_page.click_photo()

        # 执行命令并验证
        with allure.step(f"执行Ask Screen命令: {command}"):
            success, response_texts, verification_result = self.simple_floating_command_test(
                ella_floating_page, command, expected_keywords, verify_response=True, response_timeout=30
            )

        # 断言结果
        with allure.step("验证测试结果"):
            assert success, f"命令执行失败: {command}"
            assert response_texts, "未获取到响应文本"

            # 验证响应包含期望关键词（至少匹配一个）
            self.assert_floating_response_contains(response_texts, expected_keywords, match_all=False)

        with allure.step("记录测试完成"):
            log.info(f"✅ Ask Screen测试完成: {command}")
