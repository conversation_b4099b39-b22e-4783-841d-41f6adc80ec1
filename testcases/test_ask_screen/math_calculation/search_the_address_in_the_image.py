import pytest
import allure
from testcases.test_ask_screen.base_ask_screen_test import SimpleAskScreenTest
from pages.apps.ai_gallery.photos_page import AiGalleryPhotosPage
from core.logger import log

@allure.epic("Ella浮窗测试")
@allure.feature("Ask Screen功能")
@allure.story("数学计算")
class TestAskScreenSearchAddressImage(SimpleAskScreenTest):
    """
    Ask Screen测试类 - 数学计算
    命令: search the address in the image
    图片: search_the_address_in_the_image
    """

    @allure.title("测试search the address in the image")
    @allure.description("测试Ask Screen功能: search the address in the image")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_search_the_address_in_the_image(self, ella_floating_page, ella_app):
        """测试search the address in the image命令"""
        command = "search the address in the image"
        # expected_keywords = ['Done']

        # 数据准备
        with allure.step("准备测试数据"):
            from tools.file_pusher import file_pusher
            # 推送测试图片到设备
            push_result = file_pusher.push_ask_screen_image("search_the_address_in_the_image")
            assert push_result, f"推送图片失败: search_the_address_in_the_image"

            # 打开图库并选择图片
            photos_page = AiGalleryPhotosPage()
            result = photos_page.start_app()
            if result:
                photos_page.wait_for_page_load()
                photos_page.click_photo()

        # 执行命令并验证
        with allure.step(f"执行Ask Screen命令: {command}"):
            success, response_texts, verification_result, initial_status, final_status = self.simple_floating_command_test_with_status(
                ella_app, ella_floating_page, command, expected_keywords=None, verify_response=True
            )

        # 断言结果
        # with allure.step("验证测试结果"):
        #     assert success, f"命令执行失败: {command}"
        #     assert response_texts, "未获取到响应文本"
        #
        #     # 验证响应包含期望关键词（至少匹配一个）
        #     self.assert_floating_response_contains(response_texts, expected_keywords, match_all=False)

        with allure.step("记录测试完成"):
            log.info(f"status: {initial_status} -> {final_status}")
            log.info(f"✅ Ask Screen测试完成: {command}")
