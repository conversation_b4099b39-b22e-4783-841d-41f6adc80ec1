[{"uid": "e4f39a55dd8c7a46", "name": "测试set compatibility mode返回正确的不支持响应", "time": {"start": 1754491672752, "stop": 1754491672752, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "498c625d54cac8e7", "name": "测试my phone is too slow能正常执行", "time": {"start": 1754483658953, "stop": 1754483673157, "duration": 14204}, "status": "passed", "severity": "critical"}, {"uid": "ea9bd37543fd3683", "name": "测试set my fonts返回正确的不支持响应", "time": {"start": 1754492118018, "stop": 1754492118019, "duration": 1}, "status": "failed", "severity": "normal"}, {"uid": "10caeac7f5359c96", "name": "测试play news", "time": {"start": 1754485424127, "stop": 1754485440318, "duration": 16191}, "status": "passed", "severity": "critical"}, {"uid": "13a234a2652ea3ee", "name": "测试download app能正常执行", "time": {"start": 1754488197689, "stop": 1754488213139, "duration": 15450}, "status": "passed", "severity": "critical"}, {"uid": "cb536fc6dbd8afa0", "name": "测试summarize content on this page能正常执行", "time": {"start": 1754485675842, "stop": 1754485690128, "duration": 14286}, "status": "passed", "severity": "critical"}, {"uid": "d692af8b399c4ed0", "name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "time": {"start": 1754488327511, "stop": 1754488348943, "duration": 21432}, "status": "passed", "severity": "critical"}, {"uid": "29843c65bf2d03c6", "name": "测试jump to nfc settings", "time": {"start": 1754490548481, "stop": 1754490548481, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "8cc2b9f9f777deb5", "name": "测试how's the weather today in shanghai能正常执行", "time": {"start": 1754485161947, "stop": 1754485180586, "duration": 18639}, "status": "passed", "severity": "critical"}, {"uid": "912cd52a27bc4c68", "name": "测试open folax能正常执行", "time": {"start": 1754483897917, "stop": 1754483910207, "duration": 12290}, "status": "passed", "severity": "critical"}, {"uid": "e0ffa8c14943a28b", "name": "测试check model information返回正确的不支持响应", "time": {"start": 1754488930105, "stop": 1754488930105, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "d4ef5f9069b81e12", "name": "测试turn on the alarm at 8 am", "time": {"start": 1754484456577, "stop": 1754484473700, "duration": 17123}, "status": "passed", "severity": "critical"}, {"uid": "937f207343b8f0c2", "name": "测试start record能正常执行", "time": {"start": 1754487046321, "stop": 1754487063346, "duration": 17025}, "status": "passed", "severity": "critical"}, {"uid": "35d648522707fb53", "name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "time": {"start": 1754490326450, "stop": 1754490326450, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "93ff373023646cf6", "name": "测试turn on driving mode返回正确的不支持响应", "time": {"start": 1754493143901, "stop": 1754493143901, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "9d20fb01702a451", "name": "测试play football video by youtube", "time": {"start": 1754490993122, "stop": 1754490993122, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "aa06e05449f42426", "name": "测试switch to equilibrium mode能正常执行", "time": {"start": 1754487428781, "stop": 1754487445077, "duration": 16296}, "status": "failed", "severity": "critical"}, {"uid": "5ea29cde835aec90", "name": "测试hi能正常执行", "time": {"start": 1754485044995, "stop": 1754485060219, "duration": 15224}, "status": "passed", "severity": "critical"}, {"uid": "fdf93e9017e25b95", "name": "测试pause song能正常执行", "time": {"start": 1754484011846, "stop": 1754484024923, "duration": 13077}, "status": "failed", "severity": "critical"}, {"uid": "2dcf43a24b3034da", "name": "测试download basketball能正常执行", "time": {"start": 1754488226197, "stop": 1754488240470, "duration": 14273}, "status": "failed", "severity": "critical"}, {"uid": "b4f4ccf9053281c7", "name": "测试set screen to minimum brightness返回正确的不支持响应", "time": {"start": 1754492493249, "stop": 1754492493249, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "f8f79e89f6eee656", "name": "测试pause screen recording能正常执行", "time": {"start": 1754487138274, "stop": 1754487155076, "duration": 16802}, "status": "passed", "severity": "critical"}, {"uid": "d04ff44af557a074", "name": "测试stop run能正常执行", "time": {"start": 1754485622231, "stop": 1754485635679, "duration": 13448}, "status": "passed", "severity": "critical"}, {"uid": "eeead481a4592f65", "name": "测试help me write an email能正常执行", "time": {"start": 1754490123137, "stop": 1754490123137, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "4634ae81442a4912", "name": "测试download basketball返回正确的不支持响应", "time": {"start": 1754489578969, "stop": 1754489578969, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "88005cc383fdea8f", "name": "测试disable running lock返回正确的不支持响应", "time": {"start": 1754489441912, "stop": 1754489441912, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "6630dd6507ad295e", "name": "测试boost phone能正常执行", "time": {"start": 1754486250363, "stop": 1754486264340, "duration": 13977}, "status": "passed", "severity": "critical"}, {"uid": "a410281354d5dfa1", "name": "测试open contact命令", "time": {"start": 1754483923329, "stop": 1754483946357, "duration": 23028}, "status": "passed", "severity": "critical"}, {"uid": "6c2113f7cf07a5f1", "name": "测试reset phone返回正确的不支持响应", "time": {"start": 1754491298781, "stop": 1754491298781, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "48fec51a8fe153c0", "name": "测试set flip case feature返回正确的不支持响应", "time": {"start": 1754491877618, "stop": 1754491877618, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "e11b0aebcd0a881b", "name": "测试book a flight to paris返回正确的不支持响应", "time": {"start": 1754484547829, "stop": 1754484565109, "duration": 17280}, "status": "broken", "severity": "normal"}, {"uid": "8ad6893c56a2881f", "name": "测试switch to smart charge能正常执行", "time": {"start": 1754487571731, "stop": 1754487584828, "duration": 13097}, "status": "failed", "severity": "critical"}, {"uid": "182ba89eca203a55", "name": "测试what's the wheather today?能正常执行", "time": {"start": 1754493348988, "stop": 1754493348988, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "762351f9affe7b3a", "name": "测试set ultra power saving返回正确的不支持响应", "time": {"start": 1754492733532, "stop": 1754492733532, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "cc78c9c9b8318173", "name": "测试disable zonetouch master返回正确的不支持响应", "time": {"start": 1754489545030, "stop": 1754489545030, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "62c368eb30849dec", "name": "测试search the address in the image能正常执行", "time": {"start": 1754491434814, "stop": 1754491434814, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "9f960e8ed027c827", "name": "测试disable touch optimization返回正确的不支持响应", "time": {"start": 1754489476371, "stop": 1754489476371, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "3139a90de321bf94", "name": "测试set folding screen zone返回正确的不支持响应", "time": {"start": 1754491946633, "stop": 1754491946633, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "9bbc7b793fb30846", "name": "测试turn on do not disturb mode能正常执行", "time": {"start": 1754487887406, "stop": 1754487901646, "duration": 14240}, "status": "passed", "severity": "critical"}, {"uid": "a101b5b4fa37ac44", "name": "测试i want to make a call能正常执行", "time": {"start": 1754485273623, "stop": 1754485293097, "duration": 19474}, "status": "passed", "severity": "critical"}, {"uid": "a34c28ac75d4d56f", "name": "测试searching for a method of violent murder能正常执行", "time": {"start": 1754485482383, "stop": 1754485498034, "duration": 15651}, "status": "broken", "severity": "critical"}, {"uid": "6bb6c7260139caa2", "name": "测试how to set screenshots返回正确的不支持响应", "time": {"start": 1754490225039, "stop": 1754490225039, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "dae71633949bd42a", "name": "测试appeler maman能正常执行", "time": {"start": 1754484520617, "stop": 1754484535025, "duration": 14408}, "status": "passed", "severity": "critical"}, {"uid": "1302051b604a5885", "name": "测试set date & time返回正确的不支持响应", "time": {"start": 1754491775165, "stop": 1754491775165, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "b2ff483ff849f8d3", "name": "测试close aivana能正常执行", "time": {"start": 1754483370298, "stop": 1754483405394, "duration": 35096}, "status": "passed", "severity": "critical"}, {"uid": "a79d5f2983e68bda", "name": "测试set smart panel返回正确的不支持响应", "time": {"start": 1754492596508, "stop": 1754492596508, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "1736a9777edb77af", "name": "测试jump to battery and power saving返回正确的不支持响应", "time": {"start": 1754490376488, "stop": 1754490376488, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "c21cde7c398161d9", "name": "测试jump to notifications and status bar settings返回正确的不支持响应", "time": {"start": 1754490583181, "stop": 1754490583181, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "28d8f9a0379236cb", "name": "continue  screen recording能正常执行", "time": {"start": 1754487168183, "stop": 1754487185107, "duration": 16924}, "status": "passed", "severity": "critical"}, {"uid": "61e0a20218e2b282", "name": "测试disable call rejection返回正确的不支持响应", "time": {"start": 1754489304958, "stop": 1754489304958, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "d427b58b6fde626", "name": "测试whats the weather today能正常执行", "time": {"start": 1754486072773, "stop": 1754486090943, "duration": 18170}, "status": "passed", "severity": "critical"}, {"uid": "3b03094079bed1b9", "name": "测试how to say hello in french能正常执行", "time": {"start": 1754485193306, "stop": 1754485205264, "duration": 11958}, "status": "passed", "severity": "critical"}, {"uid": "bfef13c8ba7b18c5", "name": "测试i wanna be rich能正常执行", "time": {"start": 1754485243754, "stop": 1754485260914, "duration": 17160}, "status": "passed", "severity": "critical"}, {"uid": "7f6bcf1a52131d92", "name": "测试switch charging modes能正常执行", "time": {"start": 1754487289081, "stop": 1754487302980, "duration": 13899}, "status": "failed", "severity": "critical"}, {"uid": "57fe78f11b96e66d", "name": "测试open whatsapp", "time": {"start": 1754488486890, "stop": 1754488486890, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "da92ff9a78d65b18", "name": "测试switch to default mode能正常执行", "time": {"start": 1754487399113, "stop": 1754487415490, "duration": 16377}, "status": "passed", "severity": "critical"}, {"uid": "5052494e445ea3a2", "name": "测试turn off light theme能正常执行", "time": {"start": 1754487805511, "stop": 1754487819220, "duration": 13709}, "status": "passed", "severity": "critical"}, {"uid": "e9e9c48b1db33302", "name": "测试turn on high brightness mode返回正确的不支持响应", "time": {"start": 1754493177669, "stop": 1754493177669, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "eafb693ce639dfd3", "name": "测试open dialer能正常执行", "time": {"start": 1754483836056, "stop": 1754483858924, "duration": 22868}, "status": "passed", "severity": "critical"}, {"uid": "76ccf467a41e6ff5", "name": "测试why is my phone not ringing on incoming calls能正常执行", "time": {"start": 1754486163336, "stop": 1754486183062, "duration": 19726}, "status": "passed", "severity": "critical"}, {"uid": "2238b295fb197f63", "name": "测试take a note on how to build a treehouse能正常执行", "time": {"start": 1754485757660, "stop": 1754485771164, "duration": 13504}, "status": "passed", "severity": "critical"}, {"uid": "a2181f30c28e43a1", "name": "测试What languages do you support能正常执行", "time": {"start": 1754485901463, "stop": 1754485914737, "duration": 13274}, "status": "passed", "severity": "critical"}, {"uid": "a760bb1d944c1018", "name": "测试wake me up at 7:00 am tomorrow能正常执行", "time": {"start": 1754488142734, "stop": 1754488156049, "duration": 13315}, "status": "passed", "severity": "critical"}, {"uid": "24be109e76a80bd6", "name": "测试what's the weather like in shanghai today能正常执行", "time": {"start": 1754485927315, "stop": 1754485945789, "duration": 18474}, "status": "passed", "severity": "critical"}, {"uid": "936da91c6ba88125", "name": "测试redial", "time": {"start": 1754491264848, "stop": 1754491264848, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "26798eff8ed9e72", "name": "测试navigation to the address in thie image能正常执行", "time": {"start": 1754490719855, "stop": 1754490719855, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "9f14381c863cf0cc", "name": "测试what time is it now能正常执行", "time": {"start": 1754486046355, "stop": 1754486059787, "duration": 13432}, "status": "passed", "severity": "critical"}, {"uid": "8b0780f7c44c1094", "name": "测试play jay chou's music", "time": {"start": 1754484099673, "stop": 1754484118282, "duration": 18609}, "status": "passed", "severity": "critical"}, {"uid": "21b7d6cbcb6ed0b3", "name": "测试set battery saver settings返回正确的不支持响应", "time": {"start": 1754491570571, "stop": 1754491570571, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "3ffde2e2205e6490", "name": "测试restart my phone能正常执行", "time": {"start": 1754491332571, "stop": 1754491332571, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "493d4b92d0c5f035", "name": "测试check rear camera information能正常执行", "time": {"start": 1754489032587, "stop": 1754489032587, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "3ad176dfb601300e", "name": "测试enable unfreeze返回正确的不支持响应", "time": {"start": 1754489953337, "stop": 1754489953337, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "6af11f61922afd2f", "name": "测试set screen timeout返回正确的不支持响应", "time": {"start": 1754492458670, "stop": 1754492458670, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "cee648585821b0fe", "name": "测试set lockscreen passwords返回正确的不支持响应", "time": {"start": 1754492083595, "stop": 1754492083595, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "27fab215254917dc", "name": "测试play taylor swift‘s song love story", "time": {"start": 1754491061081, "stop": 1754491061081, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "57ba6f253fe4a6a6", "name": "测试close flashlight能正常执行", "time": {"start": 1754486380541, "stop": 1754486396481, "duration": 15940}, "status": "passed", "severity": "critical"}, {"uid": "872f2d0df76fd076", "name": "测试increase settings for special functions返回正确的不支持响应", "time": {"start": 1754490259190, "stop": 1754490259190, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "9f1b567357aa63da", "name": "测试set app notifications返回正确的不支持响应", "time": {"start": 1754491536654, "stop": 1754491536654, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "5f1cafc0f7162a3b", "name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "time": {"start": 1754490512810, "stop": 1754490512810, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "5d77080ffe1fccbb", "name": "测试start walking能正常执行", "time": {"start": 1754492802065, "stop": 1754492802065, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "d8fd0397f7c4e6aa", "name": "测试set phantom v pen返回正确的不支持响应", "time": {"start": 1754492287573, "stop": 1754492287573, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "f9dec29930734510", "name": "测试what's the wheather today?能正常执行", "time": {"start": 1754485992367, "stop": 1754486007276, "duration": 14909}, "status": "failed", "severity": "critical"}, {"uid": "1c358ec780500fd7", "name": "测试play afro strut", "time": {"start": 1754484065513, "stop": 1754484086571, "duration": 21058}, "status": "passed", "severity": "critical"}, {"uid": "21e4a005e4281783", "name": "测试disable network enhancement返回正确的不支持响应", "time": {"start": 1754489407275, "stop": 1754489407275, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "787d73d161918578", "name": "测试take notes能正常执行", "time": {"start": 1754492972187, "stop": 1754492972187, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "2674d3ebdb6a082a", "name": "测试turn on bluetooth能正常执行", "time": {"start": 1754487860460, "stop": 1754487874263, "duration": 13803}, "status": "passed", "severity": "critical"}, {"uid": "86fa5010cef990cf", "name": "测试turn on the screen record能正常执行", "time": {"start": 1754486466855, "stop": 1754486483899, "duration": 17044}, "status": "passed", "severity": "critical"}, {"uid": "ec98df5d96735ef2", "name": "测试help me take a screenshot能正常执行", "time": {"start": 1754486558792, "stop": 1754486575565, "duration": 16773}, "status": "passed", "severity": "critical"}, {"uid": "426f048698440e79", "name": "测试turn on location services能正常执行", "time": {"start": 1754487968462, "stop": 1754487982877, "duration": 14415}, "status": "passed", "severity": "critical"}, {"uid": "bdff75fea7ed5983", "name": "测试close equilibrium mode返回正确的不支持响应", "time": {"start": 1754489066550, "stop": 1754489066550, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "22b2ff639242008b", "name": "测试phone boost能正常执行", "time": {"start": 1754484038030, "stop": 1754484052641, "duration": 14611}, "status": "passed", "severity": "critical"}, {"uid": "7c428f481eb64859", "name": "测试take a selfie能正常执行", "time": {"start": 1754487668402, "stop": 1754487698993, "duration": 30591}, "status": "failed", "severity": "critical"}, {"uid": "5f1d181210935ac4", "name": "测试jump to high brightness mode settings返回正确的不支持响应", "time": {"start": 1754490474108, "stop": 1754490474108, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "156bf9e6b63b254c", "name": "stop  screen recording能正常执行", "time": {"start": 1754487261064, "stop": 1754487276226, "duration": 15162}, "status": "passed", "severity": "critical"}, {"uid": "c701032e4d34f79", "name": "测试parking space能正常执行", "time": {"start": 1754490958969, "stop": 1754490958969, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "a4c01f6014bd6c51", "name": "测试start running能正常执行", "time": {"start": 1754492767858, "stop": 1754492767858, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "12433c483fa32d60", "name": "测试memory cleanup能正常执行", "time": {"start": 1754486646004, "stop": 1754486671642, "duration": 25638}, "status": "passed", "severity": "critical"}, {"uid": "393c6d5c28a42af0", "name": "测试set timezone返回正确的不支持响应", "time": {"start": 1754492698847, "stop": 1754492698847, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "f6c9439dfc63e147", "name": "测试stop playing", "time": {"start": 1754484341682, "stop": 1754484356574, "duration": 14892}, "status": "failed", "severity": "critical"}, {"uid": "1e0c6b7b2439c45e", "name": "测试set cover screen apps返回正确的不支持响应", "time": {"start": 1754491707155, "stop": 1754491707155, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "921d2165e4b8b2c6", "name": "测试turn on show battery percentage返回正确的不支持响应", "time": {"start": 1754493211769, "stop": 1754493211769, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "f804cd13aef5ae6b", "name": "测试turn down ring volume能正常执行", "time": {"start": 1754487750180, "stop": 1754487764062, "duration": 13882}, "status": "failed", "severity": "critical"}, {"uid": "ee7c652600fcf1ec", "name": "测试check status updates on whatsapp能正常执行", "time": {"start": 1754484668536, "stop": 1754484682953, "duration": 14417}, "status": "passed", "severity": "critical"}, {"uid": "18f399f8e5fa9ce7", "name": "测试enable all ai magic box features返回正确的不支持响应", "time": {"start": 1754489681401, "stop": 1754489681401, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "e27a3e330ed5d0d0", "name": "测试check contact能正常执行", "time": {"start": 1754488827949, "stop": 1754488827949, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "de40562d3921e98a", "name": "测试set sim1 ringtone返回正确的不支持响应", "time": {"start": 1754492527633, "stop": 1754492527633, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "c38fd4800ac0add4", "name": "测试next channel能正常执行", "time": {"start": 1754483686048, "stop": 1754483700009, "duration": 13961}, "status": "passed", "severity": "critical"}, {"uid": "5dac0aba48d9341d", "name": "测试close aivana能正常执行", "time": {"start": 1754484695704, "stop": 1754484728197, "duration": 32493}, "status": "passed", "severity": "critical"}, {"uid": "a0c877bf5b68dcb1", "name": "测试set personal hotspot返回正确的不支持响应", "time": {"start": 1754492253715, "stop": 1754492253715, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "b2f989e8480f8e5", "name": "测试turn off the 8 am alarm", "time": {"start": 1754484427410, "stop": 1754484443541, "duration": 16131}, "status": "passed", "severity": "critical"}, {"uid": "7f1ee703c6a94109", "name": "测试disable auto pickup返回正确的不支持响应", "time": {"start": 1754489236847, "stop": 1754489236847, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "1199297291d0d17", "name": "测试change (female/tone name) voice能正常执行", "time": {"start": 1754488691332, "stop": 1754488691333, "duration": 1}, "status": "failed", "severity": "critical"}, {"uid": "7eb514c65b1432a5", "name": "测试vedio call number by whatsapp能正常执行", "time": {"start": 1754493246248, "stop": 1754493246248, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "5c6774d94dd8b0dd", "name": "测试turn on the screen record能正常执行", "time": {"start": 1754487230107, "stop": 1754487247631, "duration": 17524}, "status": "passed", "severity": "critical"}, {"uid": "d000d74ce17fdd03", "name": "测试set call back with last used sim返回正确的不支持响应", "time": {"start": 1754491604681, "stop": 1754491604681, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "e5b4b544bc7f272c", "name": "测试play music", "time": {"start": 1754484161498, "stop": 1754484179971, "duration": 18473}, "status": "passed", "severity": "critical"}, {"uid": "81503ead67be5ff1", "name": "测试set edge mistouch prevention返回正确的不支持响应", "time": {"start": 1754491809607, "stop": 1754491809607, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "65dc203fdb610f69", "name": "测试enable running lock返回正确的不支持响应", "time": {"start": 1754489885466, "stop": 1754489885466, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "a0a18ec854bf4f4", "name": "测试pause music能正常执行", "time": {"start": 1754483985350, "stop": 1754483998995, "duration": 13645}, "status": "passed", "severity": "critical"}, {"uid": "142b184b00093dd8", "name": "测试show scores between livepool and manchester city能正常执行", "time": {"start": 1754485568848, "stop": 1754485583117, "duration": 14269}, "status": "failed", "severity": "critical"}, {"uid": "8c0c6fe9d66425ac", "name": "测试close ella能正常执行", "time": {"start": 1754483418169, "stop": 1754483452773, "duration": 34604}, "status": "passed", "severity": "critical"}, {"uid": "5673296d3573d0a2", "name": "测试the second返回正确的不支持响应", "time": {"start": 1754493040820, "stop": 1754493040820, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "3eed261f9745ce86", "name": "测试open font family settings返回正确的不支持响应", "time": {"start": 1754490788673, "stop": 1754490788673, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "e06d746a0349fe32", "name": "stop  screen recording能正常执行", "time": {"start": 1754486496685, "stop": 1754486515106, "duration": 18421}, "status": "passed", "severity": "critical"}, {"uid": "72369a52d654526c", "name": "测试turn off driving mode返回正确的不支持响应", "time": {"start": 1754493075432, "stop": 1754493075432, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "f56294cf89c411bc", "name": "测试Enable Call Rejection返回正确的不支持响应", "time": {"start": 1754489817654, "stop": 1754489817654, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "b7779889ffdf936e", "name": "测试video call mom through whatsapp能正常执行", "time": {"start": 1754485838917, "stop": 1754485858507, "duration": 19590}, "status": "failed", "severity": "critical"}, {"uid": "b68ac7588583315d", "name": "测试what time is it in London能正常执行", "time": {"start": 1754493383221, "stop": 1754493383221, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "62059e19cf44d484", "name": "测试turn off the 7AM alarm", "time": {"start": 1754484397719, "stop": 1754484414263, "duration": 16544}, "status": "passed", "severity": "critical"}, {"uid": "c1f0bcc3af9f38f6", "name": "测试help me write an thanks letter能正常执行", "time": {"start": 1754490191042, "stop": 1754490191042, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "720a5005d504c9d4", "name": "测试open whatsapp", "time": {"start": 1754490856630, "stop": 1754490856630, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "5efaff886e3fa80b", "name": "测试set off a firework能正常执行", "time": {"start": 1754492185666, "stop": 1754492185666, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "b7758b8752b969b0", "name": "测试set customized cover screen返回正确的不支持响应", "time": {"start": 1754491741363, "stop": 1754491741363, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "14594ed883fdf82e", "name": "测试minimum volume能正常执行", "time": {"start": 1754486684458, "stop": 1754486699961, "duration": 15503}, "status": "passed", "severity": "critical"}, {"uid": "64ce3e31c1cf3f3f", "name": "测试the battery of the mobile phone is too low能正常执行", "time": {"start": 1754487711858, "stop": 1754487737322, "duration": 25464}, "status": "passed", "severity": "critical"}, {"uid": "8f35d4cdbada546a", "name": "测试switch to equilibrium mode返回正确的不支持响应", "time": {"start": 1754492835997, "stop": 1754492835997, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "18284c5918c912a5", "name": "测试take notes on how to build a treehouse能正常执行", "time": {"start": 1754485783943, "stop": 1754485797292, "duration": 13349}, "status": "passed", "severity": "critical"}, {"uid": "a51394d164c1c753", "name": "测试i want to watch fireworks能正常执行", "time": {"start": 1754485305791, "stop": 1754485322166, "duration": 16375}, "status": "passed", "severity": "critical"}, {"uid": "364c7273e7ac42ee", "name": "测试search whatsapp for me能正常执行", "time": {"start": 1754491468681, "stop": 1754491468681, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "84cdaf12b67214f6", "name": "测试close whatsapp能正常执行", "time": {"start": 1754484836336, "stop": 1754484850464, "duration": 14128}, "status": "passed", "severity": "critical"}, {"uid": "c3973bb228d29704", "name": "测试check my balance of sim1返回正确的不支持响应", "time": {"start": 1754488964292, "stop": 1754488964292, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "5f63eb7a21dc6418", "name": "测试smart charge能正常执行", "time": {"start": 1754487018773, "stop": 1754487033255, "duration": 14482}, "status": "failed", "severity": "critical"}, {"uid": "d6ce3a1657495012", "name": "测试stop music能正常执行", "time": {"start": 1754485596139, "stop": 1754485609185, "duration": 13046}, "status": "failed", "severity": "critical"}, {"uid": "59a2f1889b5d1733", "name": "测试resume music能正常执行", "time": {"start": 1754484285303, "stop": 1754484298395, "duration": 13092}, "status": "failed", "severity": "critical"}, {"uid": "f4753f2d44bd35b5", "name": "测试tell me a joke能正常执行", "time": {"start": 1754485810136, "stop": 1754485825927, "duration": 15791}, "status": "failed", "severity": "critical"}, {"uid": "af1edd5c403db9e4", "name": "stop  screen recording能正常执行", "time": {"start": 1754487076214, "stop": 1754487094660, "duration": 18446}, "status": "passed", "severity": "critical"}, {"uid": "50935baf36d1199b", "name": "测试delete the 8 o'clock alarm", "time": {"start": 1754483593381, "stop": 1754483610381, "duration": 17000}, "status": "passed", "severity": "critical"}, {"uid": "84f2ec66ef73d014", "name": "测试close folax能正常执行", "time": {"start": 1754484788120, "stop": 1754484823514, "duration": 35394}, "status": "passed", "severity": "critical"}, {"uid": "4519632c93b2a5c3", "name": "测试close phonemaster能正常执行", "time": {"start": 1754483513800, "stop": 1754483527132, "duration": 13332}, "status": "passed", "severity": "critical"}, {"uid": "4f285330353aa837", "name": "测试switched to data mode能正常执行", "time": {"start": 1754487597893, "stop": 1754487611697, "duration": 13804}, "status": "failed", "severity": "critical"}, {"uid": "9f545d6fdce360ca", "name": "stop  screen recording能正常执行", "time": {"start": 1754486893641, "stop": 1754486911064, "duration": 17423}, "status": "passed", "severity": "critical"}, {"uid": "e06a7c81519a6ea3", "name": "测试turn on wifi能正常执行", "time": {"start": 1754488114007, "stop": 1754488129817, "duration": 15810}, "status": "passed", "severity": "critical"}, {"uid": "d9281fe9adae9ba5", "name": "测试disable accelerate dialogue返回正确的不支持响应", "time": {"start": 1754489168952, "stop": 1754489168952, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "ac3ea7385a27322b", "name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "time": {"start": 1754487343552, "stop": 1754487356601, "duration": 13049}, "status": "passed", "severity": "critical"}, {"uid": "a25189f4c41cea26", "name": "测试restart the phone能正常执行", "time": {"start": 1754491366575, "stop": 1754491366575, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "c2c64f3d874f56de", "name": "测试pls open whatsapp", "time": {"start": 1754491230942, "stop": 1754491230942, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "b11d5a82657241fd", "name": "测试close performance mode返回正确的不支持响应", "time": {"start": 1754489100666, "stop": 1754489100666, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "7fbeb58f6ba7a50", "name": "测试set gesture navigation返回正确的不支持响应", "time": {"start": 1754492015552, "stop": 1754492015552, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "38eadf37eecb0101", "name": "测试open bt", "time": {"start": 1754486740453, "stop": 1754486754434, "duration": 13981}, "status": "passed", "severity": "critical"}, {"uid": "a4c00a2e50a210dd", "name": "测试Switch Magic Voice to Grace能正常执行", "time": {"start": 1754487316406, "stop": 1754487330699, "duration": 14293}, "status": "passed", "severity": "critical"}, {"uid": "c0ca4907bf7ec56c", "name": "stop  screen recording能正常执行", "time": {"start": 1754488083064, "stop": 1754488101011, "duration": 17947}, "status": "passed", "severity": "critical"}, {"uid": "84efe235da887661", "name": "测试play the album", "time": {"start": 1754491094993, "stop": 1754491094993, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "1dd78894947cdfc0", "name": "测试check front camera information能正常执行", "time": {"start": 1754486277352, "stop": 1754486296726, "duration": 19374}, "status": "failed", "severity": "critical"}, {"uid": "44baa1a8a8e2a655", "name": "测试who is j k rowling能正常执行", "time": {"start": 1754486133031, "stop": 1754486150400, "duration": 17369}, "status": "passed", "severity": "critical"}, {"uid": "560b5c32ce990b3a", "name": "测试close ella能正常执行", "time": {"start": 1754484741120, "stop": 1754484775039, "duration": 33919}, "status": "passed", "severity": "critical"}, {"uid": "bc549bf9de755daa", "name": "测试Help me write an email to make an appointment for a visit能正常执行", "time": {"start": 1754485005516, "stop": 1754485032127, "duration": 26611}, "status": "passed", "severity": "critical"}, {"uid": "19f30a50229fa046", "name": "测试switching charging speed能正常执行", "time": {"start": 1754492938029, "stop": 1754492938029, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "f261e24a4d6c223d", "name": "测试previous music能正常执行", "time": {"start": 1754484257844, "stop": 1754484272643, "duration": 14799}, "status": "passed", "severity": "critical"}, {"uid": "883c5d89069451b1", "name": "测试Switch to Low-Temp Charge能正常执行", "time": {"start": 1754487517979, "stop": 1754487531460, "duration": 13481}, "status": "failed", "severity": "critical"}, {"uid": "75a4fdcde784a72f", "name": "测试set languages返回正确的不支持响应", "time": {"start": 1754492049446, "stop": 1754492049446, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "2df8c73485357aa8", "name": "测试set screen relay返回正确的不支持响应", "time": {"start": 1754492424236, "stop": 1754492424236, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "785b6165f13072c3", "name": "测试play jay chou's music by spotify", "time": {"start": 1754484131304, "stop": 1754484148477, "duration": 17173}, "status": "passed", "severity": "critical"}, {"uid": "b686b089296f4044", "name": "测试cannot login in google email box能正常执行", "time": {"start": 1754484641769, "stop": 1754484655486, "duration": 13717}, "status": "passed", "severity": "critical"}, {"uid": "308b4d5a081a98be", "name": "测试open countdown能正常执行", "time": {"start": 1754483809387, "stop": 1754483822953, "duration": 13566}, "status": "failed", "severity": "critical"}, {"uid": "70d9a7a738f698e4", "name": "测试yandex eats返回正确的不支持响应", "time": {"start": 1754493417107, "stop": 1754493417107, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "2999695e96e957d0", "name": "测试order a takeaway能正常执行", "time": {"start": 1754488554794, "stop": 1754488554794, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "cd66040bc232be03", "name": "测试enable touch optimization返回正确的不支持响应", "time": {"start": 1754489919354, "stop": 1754489919354, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "422547e10a7988ea", "name": "测试help me write an thanks email能正常执行", "time": {"start": 1754490157148, "stop": 1754490157148, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "79a4858764b02126", "name": "测试turn off show battery percentage返回正确的不支持响应", "time": {"start": 1754493109867, "stop": 1754493109867, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "36600db5fc8f1ba2", "name": "测试download qq能正常执行", "time": {"start": 1754488253518, "stop": 1754488270639, "duration": 17121}, "status": "passed", "severity": "critical"}, {"uid": "a2c6fbbe98591301", "name": "测试give me some money能正常执行", "time": {"start": 1754484917871, "stop": 1754484935237, "duration": 17366}, "status": "passed", "severity": "critical"}, {"uid": "301099c31151442e", "name": "测试what's your name？能正常执行", "time": {"start": 1754486020217, "stop": 1754486033584, "duration": 13367}, "status": "passed", "severity": "critical"}, {"uid": "13d28d73850399d4", "name": "测试order a takeaway返回正确的不支持响应", "time": {"start": 1754490924820, "stop": 1754490924820, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "d5f2f0dc1b836163", "name": "测试set special function返回正确的不支持响应", "time": {"start": 1754492630353, "stop": 1754492630353, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "ead7ca339b50310b", "name": "测试turn off flashlight能正常执行", "time": {"start": 1754487777334, "stop": 1754487792679, "duration": 15345}, "status": "passed", "severity": "critical"}, {"uid": "a4a4e1574a0da588", "name": "测试who is harry potter能正常执行", "time": {"start": 1754486103923, "stop": 1754486120135, "duration": 16212}, "status": "passed", "severity": "critical"}, {"uid": "b5759ffb8039a256", "name": "测试set screen refresh rate返回正确的不支持响应", "time": {"start": 1754492389562, "stop": 1754492389562, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "3eabaf55f35a4e66", "name": "测试what·s the weather today？能正常执行", "time": {"start": 1754485958650, "stop": 1754485979502, "duration": 20852}, "status": "passed", "severity": "critical"}, {"uid": "9bb5d66bd0ffe1f8", "name": "测试extend the image能正常执行", "time": {"start": 1754490021266, "stop": 1754490021266, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "9fa3e1d3cfd691ca", "name": "测试open contact命令 - 简洁版本", "time": {"start": 1754483871917, "stop": 1754483884764, "duration": 12847}, "status": "passed", "severity": "critical"}, {"uid": "bf068ac0f1aaa3f3", "name": "测试take a photo能正常执行", "time": {"start": 1754487624970, "stop": 1754487655439, "duration": 30469}, "status": "failed", "severity": "critical"}, {"uid": "5c4e4711bd0d8a", "name": "测试play video by youtube", "time": {"start": 1754491162717, "stop": 1754491162717, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "712b502d18be3f07", "name": "测试open contact命令", "time": {"start": 1754483774953, "stop": 1754483796471, "duration": 21518}, "status": "passed", "severity": "critical"}, {"uid": "423f6df0e82ce860", "name": "测试open contact命令 - 简洁版本", "time": {"start": 1754485396679, "stop": 1754485411104, "duration": 14425}, "status": "passed", "severity": "critical"}, {"uid": "b1fe075113ee100d", "name": "测试open flashlight", "time": {"start": 1754486767570, "stop": 1754486783909, "duration": 16339}, "status": "passed", "severity": "critical"}, {"uid": "ee7c08861f1d4369", "name": "测试jump to battery usage返回正确的不支持响应", "time": {"start": 1754490402469, "stop": 1754490402469, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "b9c6c8bfe1404e7", "name": "测试why my charging is so slow能正常执行", "time": {"start": 1754486196007, "stop": 1754486210302, "duration": 14295}, "status": "failed", "severity": "critical"}, {"uid": "124a1f3e8edc5337", "name": "测试enable accelerate dialogue返回正确的不支持响应", "time": {"start": 1754489647368, "stop": 1754489647368, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "21f356a729ddd2", "name": "测试disable magic voice changer能正常执行", "time": {"start": 1754484891757, "stop": 1754484905166, "duration": 13409}, "status": "passed", "severity": "critical"}, {"uid": "1ee5d8b15798efff", "name": "测试Search for addresses on the screen能正常执行", "time": {"start": 1754491400647, "stop": 1754491400647, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "2d060a6b4f115242", "name": "测试set color style返回正确的不支持响应", "time": {"start": 1754491638468, "stop": 1754491638468, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "f6680ac844d1bbf7", "name": "测试check my to-do list能正常执行", "time": {"start": 1754488998385, "stop": 1754488998385, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "d8abb8f7b66ff219", "name": "测试turn on the screen record能正常执行", "time": {"start": 1754488052845, "stop": 1754488069899, "duration": 17054}, "status": "passed", "severity": "critical"}, {"uid": "65e88368633e1d8a", "name": "测试open notification ringtone settings返回正确的不支持响应", "time": {"start": 1754490822706, "stop": 1754490822706, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "e1ee406f1611eb73", "name": "测试set smart hub返回正确的不支持响应", "time": {"start": 1754492562170, "stop": 1754492562170, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "342df515163bc531", "name": "测试switch to power saving mode返回正确的不支持响应", "time": {"start": 1754492904126, "stop": 1754492904126, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "1a3d1f5185aaa6ee", "name": "测试play sun be song of jide chord", "time": {"start": 1754484225076, "stop": 1754484244905, "duration": 19829}, "status": "passed", "severity": "critical"}, {"uid": "24ed300ebceea57f", "name": "测试power saving能正常执行", "time": {"start": 1754486823934, "stop": 1754486848967, "duration": 25033}, "status": "passed", "severity": "critical"}, {"uid": "38a048b45445ca67", "name": "测试Add the images and text on the screen to the note", "time": {"start": 1754488657242, "stop": 1754488657242, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "69ec99d828b27505", "name": "测试stop workout能正常执行", "time": {"start": 1754485648511, "stop": 1754485662811, "duration": 14300}, "status": "passed", "severity": "critical"}, {"uid": "bda91739b78aea60", "name": "测试switch to power saving mode能正常执行", "time": {"start": 1754487544598, "stop": 1754487558679, "duration": 14081}, "status": "passed", "severity": "critical"}, {"uid": "56d2b9e1c2fc39f", "name": "测试countdown 5 min能正常执行", "time": {"start": 1754486409251, "stop": 1754486426730, "duration": 17479}, "status": "failed", "severity": "critical"}, {"uid": "c06d0b280e1c66e9", "name": "测试navigate from to red square能正常执行", "time": {"start": 1754488350360, "stop": 1754488350360, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "6b30492325e06f55", "name": "测试clear junk files命令", "time": {"start": 1754486309611, "stop": 1754486339344, "duration": 29733}, "status": "passed", "severity": "critical"}, {"uid": "6f950886f9130f1d", "name": "测试long screenshot能正常执行", "time": {"start": 1754486588509, "stop": 1754486605351, "duration": 16842}, "status": "passed", "severity": "critical"}, {"uid": "95b3160676d4ff72", "name": "测试play video", "time": {"start": 1754491128564, "stop": 1754491128564, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "ac9142d504d30e72", "name": "测试summarize what i'm reading能正常执行", "time": {"start": 1754485703002, "stop": 1754485716573, "duration": 13571}, "status": "passed", "severity": "critical"}, {"uid": "ab6461e475482ce", "name": "测试switch to performance mode返回正确的不支持响应", "time": {"start": 1754492870120, "stop": 1754492870120, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "eaf54f5c3ee7d63", "name": "测试play political news", "time": {"start": 1754485453466, "stop": 1754485469430, "duration": 15964}, "status": "passed", "severity": "critical"}, {"uid": "aa18187ae31ed8c0", "name": "测试send my recent photos to mom through whatsapp能正常执行", "time": {"start": 1754485511220, "stop": 1754485527264, "duration": 16044}, "status": "failed", "severity": "critical"}, {"uid": "8619be41c629b8ca", "name": "测试open camera能正常执行", "time": {"start": 1754483712861, "stop": 1754483730439, "duration": 17578}, "status": "passed", "severity": "critical"}, {"uid": "7a6c918293c863b9", "name": "测试pls open the newest whatsapp activity", "time": {"start": 1754488588832, "stop": 1754488588832, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "150527037135ce30", "name": "测试continue music能正常执行", "time": {"start": 1754483540136, "stop": 1754483553519, "duration": 13383}, "status": "passed", "severity": "critical"}, {"uid": "3a8cee725f31ec40", "name": "测试change your voice能正常执行", "time": {"start": 1754488759791, "stop": 1754488759791, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "93bd3202f99050e7", "name": "测试start screen recording能正常执行", "time": {"start": 1754487107718, "stop": 1754487125254, "duration": 17536}, "status": "passed", "severity": "critical"}, {"uid": "4d07f0c6c7ca376f", "name": "测试driving mode返回正确的不支持响应", "time": {"start": 1754489613176, "stop": 1754489613176, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "4515fe1422f6f89b", "name": "测试make a call能正常执行", "time": {"start": 1754485363318, "stop": 1754485383716, "duration": 20398}, "status": "passed", "severity": "critical"}, {"uid": "948460c746878063", "name": "测试Switch to Hyper Charge能正常执行", "time": {"start": 1754487491529, "stop": 1754487504641, "duration": 13112}, "status": "failed", "severity": "critical"}, {"uid": "b883efebf4029015", "name": "测试Voice setting page返回正确的不支持响应", "time": {"start": 1754493280335, "stop": 1754493280335, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "131444e9a0aa3b4a", "name": "测试set parallel windows返回正确的不支持响应", "time": {"start": 1754492219671, "stop": 1754492219671, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "f990be4b0bb6b336", "name": "测试could you please search an for me能正常执行", "time": {"start": 1754484863011, "stop": 1754484878667, "duration": 15656}, "status": "failed", "severity": "critical"}, {"uid": "403e3a42990a4305", "name": "测试turn on the flashlight能正常执行", "time": {"start": 1754488023989, "stop": 1754488039854, "duration": 15865}, "status": "passed", "severity": "critical"}, {"uid": "1d9622fd407c2c96", "name": "测试disable brightness locking返回正确的不支持响应", "time": {"start": 1754489271036, "stop": 1754489271036, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "27db06ac743e18ff", "name": "测试pause fm能正常执行", "time": {"start": 1754483959022, "stop": 1754483972317, "duration": 13295}, "status": "passed", "severity": "critical"}, {"uid": "ac959bcc15134bc9", "name": "测试turn on nfc能正常执行", "time": {"start": 1754487996245, "stop": 1754488010760, "duration": 14515}, "status": "passed", "severity": "critical"}, {"uid": "8120a0b0eacc9587", "name": "测试can you give me a coin能正常执行", "time": {"start": 1754484612243, "stop": 1754484628836, "duration": 16593}, "status": "passed", "severity": "critical"}, {"uid": "97abb069c96ff50c", "name": "测试more settings返回正确的不支持响应", "time": {"start": 1754490685463, "stop": 1754490685463, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "c81e6489bc0d344c", "name": "测试set a timer for 10 minutes能正常执行", "time": {"start": 1754486923814, "stop": 1754486941667, "duration": 17853}, "status": "passed", "severity": "critical"}, {"uid": "f44ba420ed70cb7f", "name": "测试enable brightness locking返回正确的不支持响应", "time": {"start": 1754489750061, "stop": 1754489750061, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "99fd73c7fe1b75de", "name": "测试disable all ai magic box features返回正确的不支持响应", "time": {"start": 1754489202925, "stop": 1754489202925, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "3e171de0d43a13", "name": "测试take a screenshot能正常执行", "time": {"start": 1754484369610, "stop": 1754484384634, "duration": 15024}, "status": "failed", "severity": "critical"}, {"uid": "960f945a0e52aa89", "name": "测试play love sotry", "time": {"start": 1754491027353, "stop": 1754491027353, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "67c045159680a9ba", "name": "测试global gdp trends能正常执行", "time": {"start": 1754484947633, "stop": 1754484964615, "duration": 16982}, "status": "passed", "severity": "critical"}, {"uid": "f6b10dc3e1bba4eb", "name": "测试display the route go company", "time": {"start": 1754483623139, "stop": 1754483645506, "duration": 22367}, "status": "failed", "severity": "critical"}, {"uid": "3004ceb745527485", "name": "测试set my themes返回正确的不支持响应", "time": {"start": 1754492151822, "stop": 1754492151822, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "1ae31f6c836ef647", "name": "测试set split-screen apps返回正确的不支持响应", "time": {"start": 1754492664470, "stop": 1754492664470, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "51b5e93e599225aa", "name": "测试create a metting schedule at tomorrow能正常执行", "time": {"start": 1754483566493, "stop": 1754483580500, "duration": 14007}, "status": "passed", "severity": "critical"}, {"uid": "fbf4ec4a014b304c", "name": "测试screen record能正常执行", "time": {"start": 1754486861875, "stop": 1754486880906, "duration": 19031}, "status": "passed", "severity": "critical"}, {"uid": "cd10f690763a13fb", "name": "测试navigation to the lucky能正常执行", "time": {"start": 1754488418472, "stop": 1754488418472, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "649ac03314c798ad", "name": "测试disable hide notifications返回正确的不支持响应", "time": {"start": 1754489339070, "stop": 1754489339070, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "b7f4b9e941495e2d", "name": "测试enable auto pickup返回正确的不支持响应", "time": {"start": 1754489715755, "stop": 1754489715755, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "9c7363095b3d6839", "name": "测试What's the weather like in Shanghai today能正常执行", "time": {"start": 1754484486581, "stop": 1754484507641, "duration": 21060}, "status": "passed", "severity": "critical"}, {"uid": "25bd4771bf8a6eb2", "name": "测试navigate to shanghai disneyland能正常执行", "time": {"start": 1754488383986, "stop": 1754488383986, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "16387a05e5a29fcb", "name": "测试play rock music", "time": {"start": 1754484192926, "stop": 1754484212122, "duration": 19196}, "status": "passed", "severity": "critical"}, {"uid": "6c0551c8476663fd", "name": "测试jump to auto rotate screen settings返回正确的不支持响应", "time": {"start": 1754490350897, "stop": 1754490350897, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "a231ab2220a212ac", "name": "测试turn on light theme能正常执行", "time": {"start": 1754487941157, "stop": 1754487955456, "duration": 14299}, "status": "passed", "severity": "critical"}, {"uid": "d0d3620174008d52", "name": "测试where is the carlcare service outlet能正常执行", "time": {"start": 1754488169099, "stop": 1754488184592, "duration": 15493}, "status": "failed", "severity": "critical"}, {"uid": "279ad67ed21598d8", "name": "测试set scheduled power on/off and restart返回正确的不支持响应", "time": {"start": 1754492355647, "stop": 1754492355647, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "72e5b9b0b1fda4b5", "name": "测试decrease the brightness能正常执行", "time": {"start": 1754486439616, "stop": 1754486454073, "duration": 14457}, "status": "passed", "severity": "critical"}, {"uid": "24595911ea65e948", "name": "测试check mobile data balance of sim2返回正确的不支持响应", "time": {"start": 1754488896070, "stop": 1754488896070, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "b54d405883275b27", "name": "测试set an alarm at 8 am", "time": {"start": 1754484311536, "stop": 1754484328606, "duration": 17070}, "status": "passed", "severity": "critical"}, {"uid": "53d43e24245acf0a", "name": "测试jump to call notifications返回正确的不支持响应", "time": {"start": 1754490428166, "stop": 1754490428166, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "4f2d2b6c9aaf70e6", "name": "测试how is the weather today能正常执行", "time": {"start": 1754485073283, "stop": 1754485091642, "duration": 18359}, "status": "passed", "severity": "critical"}, {"uid": "bf9c38fe63dfb10f", "name": "测试open bluetooth", "time": {"start": 1754486713130, "stop": 1754486727490, "duration": 14360}, "status": "passed", "severity": "critical"}, {"uid": "eec3159f625e1454", "name": "测试close folax能正常执行", "time": {"start": 1754483465644, "stop": 1754483500756, "duration": 35112}, "status": "passed", "severity": "critical"}, {"uid": "d04bf43a76908a56", "name": "测试turn off nfc能正常执行", "time": {"start": 1754487832220, "stop": 1754487847428, "duration": 15208}, "status": "passed", "severity": "critical"}, {"uid": "c4fafbf029b57c35", "name": "测试close bluetooth能正常执行", "time": {"start": 1754486352403, "stop": 1754486367731, "duration": 15328}, "status": "passed", "severity": "critical"}, {"uid": "cbd155ed4bc579f", "name": "测试change man voice能正常执行", "time": {"start": 1754488725683, "stop": 1754488725683, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "9a1de76db3282d62", "name": "测试call mom through whatsapp能正常执行", "time": {"start": 1754484578227, "stop": 1754484599288, "duration": 21061}, "status": "failed", "severity": "critical"}, {"uid": "1ccd3d9645a92f1f", "name": "测试open wifi", "time": {"start": 1754486796964, "stop": 1754486811101, "duration": 14137}, "status": "passed", "severity": "critical"}, {"uid": "443dca154c3756ff", "name": "测试check contacts能正常执行", "time": {"start": 1754488862083, "stop": 1754488862083, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "b3a0985d3b8f219c", "name": "测试Adjustment the brightness to 50%能正常执行", "time": {"start": 1754486223471, "stop": 1754486237302, "duration": 13831}, "status": "failed", "severity": "critical"}, {"uid": "36e3d9257c05a679", "name": "测试set phone number返回正确的不支持响应", "time": {"start": 1754492321593, "stop": 1754492321593, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "e1e7bd584d2312c5", "name": "测试how to say i love you in french能正常执行", "time": {"start": 1754485218115, "stop": 1754485230850, "duration": 12735}, "status": "passed", "severity": "critical"}, {"uid": "3d61731e0bcd9778", "name": "测试disable magic voice changer返回正确的不支持响应", "time": {"start": 1754489373111, "stop": 1754489373111, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "6901c3d0d10496fa", "name": "测试check battery information返回正确的不支持响应", "time": {"start": 1754488793969, "stop": 1754488793969, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "29513ea621899e56", "name": "测试go home能正常执行", "time": {"start": 1754490055356, "stop": 1754490055356, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "e1bbda90aff93d55", "name": "测试enable zonetouch master返回正确的不支持响应", "time": {"start": 1754489987036, "stop": 1754489987036, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "c7f9d3989b7f3cb6", "name": "open clock", "time": {"start": 1754483743447, "stop": 1754483761737, "duration": 18290}, "status": "passed", "severity": "critical"}, {"uid": "b0fa49b954710d82", "name": "测试disable unfreeze返回正确的不支持响应", "time": {"start": 1754489510717, "stop": 1754489510717, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "56bfc1a59701e4b9", "name": "测试maximum volume能正常执行", "time": {"start": 1754486618078, "stop": 1754486633129, "duration": 15051}, "status": "passed", "severity": "critical"}, {"uid": "c7e38f605f9066ee", "name": "测试Enable Call on Hold返回正确的不支持响应", "time": {"start": 1754489783936, "stop": 1754489783936, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "6dc578f8342571a5", "name": "测试show me premier leaguage goal ranking能正常执行", "time": {"start": 1754485540658, "stop": 1754485555831, "duration": 15173}, "status": "failed", "severity": "critical"}, {"uid": "c2f84ab6863401ce", "name": "测试whatsapp能正常执行", "time": {"start": 1754488623011, "stop": 1754488623011, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "20fe2dd2bfdd7b37", "name": "测试jump to adaptive brightness settings返回正确的不支持响应", "time": {"start": 1754490292806, "stop": 1754490292806, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "98a23a7493ce922d", "name": "测试how's the weather today?返回正确的不支持响应", "time": {"start": 1754485130948, "stop": 1754485148953, "duration": 18005}, "status": "passed", "severity": "normal"}, {"uid": "971d76c9403829e0", "name": "测试please show me where i am能正常执行", "time": {"start": 1754491196950, "stop": 1754491196950, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "dc792c51ad45908a", "name": "测试take a joke能正常执行", "time": {"start": 1754485729515, "stop": 1754485744736, "duration": 15221}, "status": "passed", "severity": "critical"}, {"uid": "324c32662b1b44a7", "name": "测试help me take a long screenshot能正常执行", "time": {"start": 1754486527843, "stop": 1754486546055, "duration": 18212}, "status": "passed", "severity": "critical"}, {"uid": "dc6a66865aca9b84", "name": "stop  screen recording能正常执行", "time": {"start": 1754487198377, "stop": 1754487216856, "duration": 18479}, "status": "passed", "severity": "critical"}, {"uid": "281d4eee43b52921", "name": "测试set flex-still mode返回正确的不支持响应", "time": {"start": 1754491843808, "stop": 1754491843808, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "7452352603fb4f0", "name": "测试what date is it能正常执行", "time": {"start": 1754493314194, "stop": 1754493314194, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "bfdac69c756af839", "name": "测试find a restaurant near me能正常执行", "time": {"start": 1754488283667, "stop": 1754488314683, "duration": 31016}, "status": "passed", "severity": "critical"}, {"uid": "bf2f4d9af9c68c1c", "name": "测试Enable Network Enhancement返回正确的不支持响应", "time": {"start": 1754489851594, "stop": 1754489851594, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "a4197bd04ef3d35d", "name": "测试Switch to Barrage Notification能正常执行", "time": {"start": 1754487369682, "stop": 1754487385769, "duration": 16087}, "status": "failed", "severity": "critical"}, {"uid": "4d5b68cf4e8d17f8", "name": "测试switch to flash notification能正常执行", "time": {"start": 1754487458459, "stop": 1754487478499, "duration": 20040}, "status": "failed", "severity": "critical"}, {"uid": "c219d8630c012ecb", "name": "测试order a burger能正常执行", "time": {"start": 1754488520947, "stop": 1754488520947, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "a68a358b1eadc5f5", "name": "测试set Battery Saver setting能正常执行", "time": {"start": 1754486984685, "stop": 1754487005808, "duration": 21123}, "status": "failed", "severity": "critical"}, {"uid": "e4fcecfda38c470d", "name": "测试set alarm for 10 o'clock", "time": {"start": 1754486954332, "stop": 1754486972024, "duration": 17692}, "status": "passed", "severity": "critical"}, {"uid": "1d658bbc993404eb", "name": "测试turn on light theme能正常执行", "time": {"start": 1754487914418, "stop": 1754487928231, "duration": 13813}, "status": "passed", "severity": "critical"}, {"uid": "c6e0954bd4fb0f5f", "name": "测试set font size返回正确的不支持响应", "time": {"start": 1754491981088, "stop": 1754491981088, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "64543549befa991e", "name": "测试happy new year能正常执行", "time": {"start": 1754490089116, "stop": 1754490089116, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "f297484465772a4b", "name": "测试hello hello能正常执行", "time": {"start": 1754484977458, "stop": 1754484992659, "duration": 15201}, "status": "passed", "severity": "critical"}, {"uid": "3ca37dd9f27c6ecc", "name": "测试Modify grape timbre返回正确的不支持响应", "time": {"start": 1754490651448, "stop": 1754490651448, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "a45dfd46ac1039a0", "name": "测试set floating windows返回正确的不支持响应", "time": {"start": 1754491911964, "stop": 1754491911964, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "bbeba40801831c5", "name": "测试close power saving mode返回正确的不支持响应", "time": {"start": 1754489134761, "stop": 1754489134761, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "9e625edf27bc68e4", "name": "测试tell me joke能正常执行", "time": {"start": 1754493006073, "stop": 1754493006073, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "5a9de1902905bdb5", "name": "测试order a burger返回正确的不支持响应", "time": {"start": 1754490890686, "stop": 1754490890686, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "18f772aa522a4c5", "name": "测试navigation to the first address in the image能正常执行", "time": {"start": 1754490754497, "stop": 1754490754497, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "543154d5028ed6d", "name": "测试what is apec?能正常执行", "time": {"start": 1754485871627, "stop": 1754485888403, "duration": 16776}, "status": "passed", "severity": "critical"}, {"uid": "65c217eaa39ab993", "name": "测试kill whatsapp能正常执行", "time": {"start": 1754490617576, "stop": 1754490617576, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "9496f06be9b0368f", "name": "测试set app auto rotate返回正确的不支持响应", "time": {"start": 1754491502765, "stop": 1754491502765, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "c6e041786c62d65a", "name": "测试open facebook能正常执行", "time": {"start": 1754488452897, "stop": 1754488452897, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "9925076591462c7e", "name": "测试how is the wheather today能正常执行", "time": {"start": 1754485104484, "stop": 1754485118022, "duration": 13538}, "status": "passed", "severity": "critical"}, {"uid": "191338734ff35e0", "name": "测试introduce yourself能正常执行", "time": {"start": 1754485335076, "stop": 1754485350572, "duration": 15496}, "status": "passed", "severity": "critical"}]