{"uid": "98d3104e051c652961429bf95fa0b5d6", "name": "suites", "children": [{"name": "testcases.test_ella.component_coupling", "children": [{"name": "test_close_aivana", "children": [{"name": "TestEllaCloseAivana", "children": [{"name": "测试close aivana能正常执行", "uid": "b2ff483ff849f8d3", "parentUid": "dc766e0abf993d1226d15b68c77f4f69", "status": "passed", "time": {"start": 1754483370298, "stop": 1754483405394, "duration": 35096}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dc766e0abf993d1226d15b68c77f4f69"}], "uid": "2326186ebd37334731865ec8b5679891"}, {"name": "test_close_ella", "children": [{"name": "TestEllaClose<PERSON>lla", "children": [{"name": "测试close ella能正常执行", "uid": "8c0c6fe9d66425ac", "parentUid": "cec0309765c155f4f8740ed45c580c80", "status": "passed", "time": {"start": 1754483418169, "stop": 1754483452773, "duration": 34604}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cec0309765c155f4f8740ed45c580c80"}], "uid": "989d936277ca7c8b51dc874f7afac7c1"}, {"name": "test_close_folax", "children": [{"name": "TestEllaCloseFolax", "children": [{"name": "测试close folax能正常执行", "uid": "eec3159f625e1454", "parentUid": "7ae1e31d59d51e63408ae29d2c8c926e", "status": "passed", "time": {"start": 1754483465644, "stop": 1754483500756, "duration": 35112}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7ae1e31d59d51e63408ae29d2c8c926e"}], "uid": "d7b55da797c782b6c2cc729406440a10"}, {"name": "test_close_phonemaster", "children": [{"name": "TestEllaClosePhonemaster", "children": [{"name": "测试close phonemaster能正常执行", "uid": "4519632c93b2a5c3", "parentUid": "976102e81bb88d3807a1e25c3ddeca37", "status": "passed", "time": {"start": 1754483513800, "stop": 1754483527132, "duration": 13332}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "976102e81bb88d3807a1e25c3ddeca37"}], "uid": "e1a42dca75ede54847ef42fc018b38d7"}, {"name": "test_continue_music", "children": [{"name": "TestEllaContinueMusic", "children": [{"name": "测试continue music能正常执行", "uid": "150527037135ce30", "parentUid": "1006aa648a1d87005804dfc1cb937d40", "status": "passed", "time": {"start": 1754483540136, "stop": 1754483553519, "duration": 13383}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1006aa648a1d87005804dfc1cb937d40"}], "uid": "091d4456268247868ef8ba9cc951a6e9"}, {"name": "test_create_a_metting_schedule_at_tomorrow", "children": [{"name": "TestEllaCreateMettingScheduleTomorrow", "children": [{"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "51b5e93e599225aa", "parentUid": "2723183c861afd907653f50d6bfc622c", "status": "passed", "time": {"start": 1754483566493, "stop": 1754483580500, "duration": 14007}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2723183c861afd907653f50d6bfc622c"}], "uid": "e4b3f0da2dd18af340a32e173acf4400"}, {"name": "test_delete_the_8_o_clock_alarm", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试delete the 8 o'clock alarm", "uid": "50935baf36d1199b", "parentUid": "75d733f64143d69107fc406cc5a4bb5d", "status": "passed", "time": {"start": 1754483593381, "stop": 1754483610381, "duration": 17000}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "75d733f64143d69107fc406cc5a4bb5d"}], "uid": "5ac49ae4fb77ab653eda0b303f9fd551"}, {"name": "test_display_the_route_go_company", "children": [{"name": "TestEllaOpenMaps", "children": [{"name": "测试display the route go company", "uid": "f6b10dc3e1bba4eb", "parentUid": "fae47b3031d4ccce961b95f3d652edcf", "status": "failed", "time": {"start": 1754483623139, "stop": 1754483645506, "duration": 22367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fae47b3031d4ccce961b95f3d652edcf"}], "uid": "a2b2fbfd344f33b51bd68e5977e57dae"}, {"name": "test_my_phone_is_too_slow", "children": [{"name": "TestEllaMyPhoneIsTooSlow", "children": [{"name": "测试my phone is too slow能正常执行", "uid": "498c625d54cac8e7", "parentUid": "a0a6853a187713f8a5ac0e869d619853", "status": "passed", "time": {"start": 1754483658953, "stop": 1754483673157, "duration": 14204}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a0a6853a187713f8a5ac0e869d619853"}], "uid": "1a3ac4d4348e23076891e9f62ee93107"}, {"name": "test_next_channel", "children": [{"name": "TestEllaNextChannel", "children": [{"name": "测试next channel能正常执行", "uid": "c38fd4800ac0add4", "parentUid": "79fe0adb48b7a7e540c509bbc071945d", "status": "passed", "time": {"start": 1754483686048, "stop": 1754483700009, "duration": 13961}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79fe0adb48b7a7e540c509bbc071945d"}], "uid": "bf525e696763b8e3f2fb093dc5260aa2"}, {"name": "test_open_camera", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open camera能正常执行", "uid": "8619be41c629b8ca", "parentUid": "e03396d1fe1c4deef0c8cf91f821a468", "status": "passed", "time": {"start": 1754483712861, "stop": 1754483730439, "duration": 17578}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e03396d1fe1c4deef0c8cf91f821a468"}], "uid": "92e88ab08159dfaf49a78312925760b7"}, {"name": "test_open_clock", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "open clock", "uid": "c7f9d3989b7f3cb6", "parentUid": "b2210d96f4ac8230585a75115cd1d8c2", "status": "passed", "time": {"start": 1754483743447, "stop": 1754483761737, "duration": 18290}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2210d96f4ac8230585a75115cd1d8c2"}], "uid": "d636d9848e1c7419e6f84e7e49c07171"}, {"name": "test_open_contact", "children": [{"name": "TestEllaContactCommandConcise", "children": [{"name": "测试open contact命令", "uid": "712b502d18be3f07", "parentUid": "39d05c6acdceec56c3c12fd23896f56f", "status": "passed", "time": {"start": 1754483774953, "stop": 1754483796471, "duration": 21518}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "39d05c6acdceec56c3c12fd23896f56f"}], "uid": "cd82497cee692a7c9a65a6184e27c221"}, {"name": "test_open_countdown", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open countdown能正常执行", "uid": "308b4d5a081a98be", "parentUid": "e10c6770504fedd71bbf3e383f94dc48", "status": "failed", "time": {"start": 1754483809387, "stop": 1754483822953, "duration": 13566}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e10c6770504fedd71bbf3e383f94dc48"}], "uid": "f0d78da692838ddb314b12cc4febc20b"}, {"name": "test_open_dialer", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open dialer能正常执行", "uid": "eafb693ce639dfd3", "parentUid": "068eb91b79bee6accc0bcab4a6367e76", "status": "passed", "time": {"start": 1754483836056, "stop": 1754483858924, "duration": 22868}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "068eb91b79bee6accc0bcab4a6367e76"}], "uid": "3c3dd50d3fd335b243796d4c0c9bfe64"}, {"name": "test_open_ella", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "9fa3e1d3cfd691ca", "parentUid": "907f87c6914ea345d20478681f484e8d", "status": "passed", "time": {"start": 1754483871917, "stop": 1754483884764, "duration": 12847}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "907f87c6914ea345d20478681f484e8d"}], "uid": "6b30479a0405c27782687611167a8e3d"}, {"name": "test_open_folax", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open folax能正常执行", "uid": "912cd52a27bc4c68", "parentUid": "031c2cd337cd7647fd7b60d0d75ba16e", "status": "passed", "time": {"start": 1754483897917, "stop": 1754483910207, "duration": 12290}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "031c2cd337cd7647fd7b60d0d75ba16e"}], "uid": "fb1e65e2e60ee7c63b5831633cb5005c"}, {"name": "test_open_phone", "children": [{"name": "TestEllaContactCommandConcise", "children": [{"name": "测试open contact命令", "uid": "a410281354d5dfa1", "parentUid": "147ffaef9b3cdea572ba5e4caea69dbe", "status": "passed", "time": {"start": 1754483923329, "stop": 1754483946357, "duration": 23028}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "147ffaef9b3cdea572ba5e4caea69dbe"}], "uid": "75ded0b626adc3c7c8dbefcba1c0b5f0"}, {"name": "test_pause_fm", "children": [{"name": "TestEllaPauseFm", "children": [{"name": "测试pause fm能正常执行", "uid": "27db06ac743e18ff", "parentUid": "88ca4f005094f55c66457c720f6cbb47", "status": "passed", "time": {"start": 1754483959022, "stop": 1754483972317, "duration": 13295}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "88ca4f005094f55c66457c720f6cbb47"}], "uid": "d9c43a396815c7e853000c20e47b8fa8"}, {"name": "test_pause_music", "children": [{"name": "TestEllaPauseMusic", "children": [{"name": "测试pause music能正常执行", "uid": "a0a18ec854bf4f4", "parentUid": "1f79feb59e2eb0bf0fb7e1387903bfee", "status": "passed", "time": {"start": 1754483985350, "stop": 1754483998995, "duration": 13645}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1f79feb59e2eb0bf0fb7e1387903bfee"}], "uid": "8a3b16d13b6ccc8492638853c9ba04aa"}, {"name": "test_pause_song", "children": [{"name": "TestEllaPauseSong", "children": [{"name": "测试pause song能正常执行", "uid": "fdf93e9017e25b95", "parentUid": "a3c6e1d0398474d5bb90c22ff0ad7930", "status": "failed", "time": {"start": 1754484011846, "stop": 1754484024923, "duration": 13077}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3c6e1d0398474d5bb90c22ff0ad7930"}], "uid": "6c009013e1160a80039c4a7e2e07d104"}, {"name": "test_phone_boost", "children": [{"name": "TestEllaPhoneBoost", "children": [{"name": "测试phone boost能正常执行", "uid": "22b2ff639242008b", "parentUid": "64e4d2ec8ce2a83bbdd5f37990e1b292", "status": "passed", "time": {"start": 1754484038030, "stop": 1754484052641, "duration": 14611}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "64e4d2ec8ce2a83bbdd5f37990e1b292"}], "uid": "b327c70b1579c29f98ae28c9e3ffe844"}, {"name": "test_play_afro_strut", "children": [{"name": "TestEllaOpenPlayAfroStrut", "children": [{"name": "测试play afro strut", "uid": "1c358ec780500fd7", "parentUid": "9988b4042f5d7026e75fc8fcb98f5519", "status": "passed", "time": {"start": 1754484065513, "stop": 1754484086571, "duration": 21058}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9988b4042f5d7026e75fc8fcb98f5519"}], "uid": "3aef4f1ab476b7c11ee7ec75e072c264"}, {"name": "test_play_jay_chou_s_music", "children": [{"name": "TestEllaOpenMusic", "children": [{"name": "测试play jay chou's music", "uid": "8b0780f7c44c1094", "parentUid": "a2f6d87f760b2cc31c5c6aad0f136dd1", "status": "passed", "time": {"start": 1754484099673, "stop": 1754484118282, "duration": 18609}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a2f6d87f760b2cc31c5c6aad0f136dd1"}], "uid": "5ac2a1940b596719fc1fe3160a450962"}, {"name": "test_play_jay_chou_s_music_by_spotify", "children": [{"name": "TestEllaOpenMusic", "children": [{"name": "测试play jay chou's music by spotify", "uid": "785b6165f13072c3", "parentUid": "c8aebf1f28528d18214bd1a9e19bfca7", "status": "passed", "time": {"start": 1754484131304, "stop": 1754484148477, "duration": 17173}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c8aebf1f28528d18214bd1a9e19bfca7"}], "uid": "4c3a9736ba488ccfdfd13dbb9608ebde"}, {"name": "test_play_music", "children": [{"name": "TestEllaOpen<PERSON>a", "children": [{"name": "测试play music", "uid": "e5b4b544bc7f272c", "parentUid": "de473794f9027034dfc0e767cb0a1fc8", "status": "passed", "time": {"start": 1754484161498, "stop": 1754484179971, "duration": 18473}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "de473794f9027034dfc0e767cb0a1fc8"}], "uid": "755e0b08ce2f0d843df6c102fcc9d49b"}, {"name": "test_play_rock_music", "children": [{"name": "TestEllaOpen<PERSON>a", "children": [{"name": "测试play rock music", "uid": "16387a05e5a29fcb", "parentUid": "b0f90c45c8c8e64fb5c2a98f923bc6fa", "status": "passed", "time": {"start": 1754484192926, "stop": 1754484212122, "duration": 19196}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0f90c45c8c8e64fb5c2a98f923bc6fa"}], "uid": "735fc1adab6496548b5a97d70b865131"}, {"name": "test_play_sun_be_song_of_jide_chord", "children": [{"name": "TestEllaOpenPlaySunBeSongOfJideChord", "children": [{"name": "测试play sun be song of jide chord", "uid": "1a3d1f5185aaa6ee", "parentUid": "5c987d2d0d756e70bc56eda09e92e854", "status": "passed", "time": {"start": 1754484225076, "stop": 1754484244905, "duration": 19829}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5c987d2d0d756e70bc56eda09e92e854"}], "uid": "020b88bee0045afa929d84639829645d"}, {"name": "test_previous_music", "children": [{"name": "TestEllaPreviousMusic", "children": [{"name": "测试previous music能正常执行", "uid": "f261e24a4d6c223d", "parentUid": "da7caa7ac31745b37f41cb283803912e", "status": "passed", "time": {"start": 1754484257844, "stop": 1754484272643, "duration": 14799}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "da7caa7ac31745b37f41cb283803912e"}], "uid": "bc6fa07388e696170fffb17281da4c7f"}, {"name": "test_resume_music", "children": [{"name": "TestEllaResumeMusic", "children": [{"name": "测试resume music能正常执行", "uid": "59a2f1889b5d1733", "parentUid": "cd0e4afc91d2a02372d847be235bede4", "status": "failed", "time": {"start": 1754484285303, "stop": 1754484298395, "duration": 13092}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cd0e4afc91d2a02372d847be235bede4"}], "uid": "b281dddb4da708a18c1bdad0e1402ec1"}, {"name": "test_set_an_alarm_at_8_am", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试set an alarm at 8 am", "uid": "b54d405883275b27", "parentUid": "33bfbdda545b50afa47fc22456e93557", "status": "passed", "time": {"start": 1754484311536, "stop": 1754484328606, "duration": 17070}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "33bfbdda545b50afa47fc22456e93557"}], "uid": "a3607188923688aad3a2ecec54f9a7c1"}, {"name": "test_stop_playing", "children": [{"name": "TestEllaOpenYoutube", "children": [{"name": "测试stop playing", "uid": "f6c9439dfc63e147", "parentUid": "04a0cd56790b683054ed72365f59d2e7", "status": "failed", "time": {"start": 1754484341682, "stop": 1754484356574, "duration": 14892}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "04a0cd56790b683054ed72365f59d2e7"}], "uid": "8960b75623e553fca190959e7f507f14"}, {"name": "test_take_a_screenshot", "children": [{"name": "TestEllaTakeScreenshot", "children": [{"name": "测试take a screenshot能正常执行", "uid": "3e171de0d43a13", "parentUid": "ac5545075b0e4292eed508b41e08ab8c", "status": "failed", "time": {"start": 1754484369610, "stop": 1754484384634, "duration": 15024}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ac5545075b0e4292eed508b41e08ab8c"}], "uid": "0daa64e90efed9ce03db463cca6bb5b1"}, {"name": "test_turn_off_the_7_am_alarm", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn off the 7AM alarm", "uid": "62059e19cf44d484", "parentUid": "7badc55fb60a0a664ac693ed5106cf16", "status": "passed", "time": {"start": 1754484397719, "stop": 1754484414263, "duration": 16544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7badc55fb60a0a664ac693ed5106cf16"}], "uid": "ca54947974933cdcc156785ae159284e"}, {"name": "test_turn_off_the_8_am_alarm", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn off the 8 am alarm", "uid": "b2f989e8480f8e5", "parentUid": "b239508423aac2a95c8e0b7b6e63f430", "status": "passed", "time": {"start": 1754484427410, "stop": 1754484443541, "duration": 16131}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b239508423aac2a95c8e0b7b6e63f430"}], "uid": "a4642e1a3f32fdb6af62d6449783bb94"}, {"name": "test_turn_on_the_alarm_at_8_am", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn on the alarm at 8 am", "uid": "d4ef5f9069b81e12", "parentUid": "d228b7163521a3d21a5179f5d0599453", "status": "passed", "time": {"start": 1754484456577, "stop": 1754484473700, "duration": 17123}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d228b7163521a3d21a5179f5d0599453"}], "uid": "836d9647d7fb73692aca73e413ef3024"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试What's the weather like in Shanghai today能正常执行", "uid": "9c7363095b3d6839", "parentUid": "de146ab7aca490ae6653437938175de4", "status": "passed", "time": {"start": 1754484486581, "stop": 1754484507641, "duration": 21060}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "de146ab7aca490ae6653437938175de4"}], "uid": "4a586ea49df80d8628d524f64c639079"}], "uid": "5948c7c27387d214d4b5e1b876d4cb27"}, {"name": "testcases.test_ella.dialogue", "children": [{"name": "test_appeler_maman", "children": [{"name": "Test<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试appeler maman能正常执行", "uid": "dae71633949bd42a", "parentUid": "f8fcd4a1c0a83d1c3db334295a60b72e", "status": "passed", "time": {"start": 1754484520617, "stop": 1754484535025, "duration": 14408}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f8fcd4a1c0a83d1c3db334295a60b72e"}], "uid": "97ecf501f8b24237ae07b2178fff9b3f"}, {"name": "test_book_a_flight_to_paris", "children": [{"name": "TestEllaBookFlightParis", "children": [{"name": "测试book a flight to paris返回正确的不支持响应", "uid": "e11b0aebcd0a881b", "parentUid": "d9246a10b1546e33724912523509ec95", "status": "broken", "time": {"start": 1754484547829, "stop": 1754484565109, "duration": 17280}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9246a10b1546e33724912523509ec95"}], "uid": "09531ecb34a029327b70c5fe7633b7f3"}, {"name": "test_call_mom_through_whatsapp", "children": [{"name": "TestEllaCallMomThroughWhatsapp", "children": [{"name": "测试call mom through whatsapp能正常执行", "uid": "9a1de76db3282d62", "parentUid": "b2bb86980dd84d36c5c8df2b8200871f", "status": "failed", "time": {"start": 1754484578227, "stop": 1754484599288, "duration": 21061}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2bb86980dd84d36c5c8df2b8200871f"}], "uid": "b093744c9f16724e1a2928dcb99b362b"}, {"name": "test_can_you_give_me_a_coin", "children": [{"name": "TestEllaCanYouGiveMeCoin", "children": [{"name": "测试can you give me a coin能正常执行", "uid": "8120a0b0eacc9587", "parentUid": "4c5d38b6a82090208170044db256ae60", "status": "passed", "time": {"start": 1754484612243, "stop": 1754484628836, "duration": 16593}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c5d38b6a82090208170044db256ae60"}], "uid": "804d60782c903cc7f780a62383e53a73"}, {"name": "test_cannot_login_in_google_email_box", "children": [{"name": "TestEllaCannotLoginGoogleEmailBox", "children": [{"name": "测试cannot login in google email box能正常执行", "uid": "b686b089296f4044", "parentUid": "9d85ac6932670633773061ce5b33ae53", "status": "passed", "time": {"start": 1754484641769, "stop": 1754484655486, "duration": 13717}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d85ac6932670633773061ce5b33ae53"}], "uid": "4a7c1215b1a8ca8a7a3c6f82e99131c2"}, {"name": "test_check_status_updates_on_whatsapp", "children": [{"name": "TestEllaCheckStatusUpdatesWhatsapp", "children": [{"name": "测试check status updates on whatsapp能正常执行", "uid": "ee7c652600fcf1ec", "parentUid": "8e354564384b35c7b587be597ea49fad", "status": "passed", "time": {"start": 1754484668536, "stop": 1754484682953, "duration": 14417}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8e354564384b35c7b587be597ea49fad"}], "uid": "5ecd3e5cb8ea680501b5eea40dc4f984"}, {"name": "test_close_aivana", "children": [{"name": "TestEllaCloseAivana", "children": [{"name": "测试close aivana能正常执行", "uid": "5dac0aba48d9341d", "parentUid": "0041f31b4fd2292bc63ae4ed9ee85477", "status": "passed", "time": {"start": 1754484695704, "stop": 1754484728197, "duration": 32493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0041f31b4fd2292bc63ae4ed9ee85477"}], "uid": "d8da0aa0f12c4e19960543b2f3ed3cc4"}, {"name": "test_close_ella", "children": [{"name": "TestEllaClose<PERSON>lla", "children": [{"name": "测试close ella能正常执行", "uid": "560b5c32ce990b3a", "parentUid": "0edab14bed6210991fe464eab7a62542", "status": "passed", "time": {"start": 1754484741120, "stop": 1754484775039, "duration": 33919}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0edab14bed6210991fe464eab7a62542"}], "uid": "c8e50bbc71d6d52fef34625f1c3d2ae1"}, {"name": "test_close_folax", "children": [{"name": "TestEllaCloseFolax", "children": [{"name": "测试close folax能正常执行", "uid": "84f2ec66ef73d014", "parentUid": "6c9fefcfb7ec486052a8fb2cd53cb5b8", "status": "passed", "time": {"start": 1754484788120, "stop": 1754484823514, "duration": 35394}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6c9fefcfb7ec486052a8fb2cd53cb5b8"}], "uid": "fa4f92b362ceab429d7d4c627c6adc3b"}, {"name": "test_close_whatsapp", "children": [{"name": "TestEllaCloseWhatsapp", "children": [{"name": "测试close whatsapp能正常执行", "uid": "84cdaf12b67214f6", "parentUid": "c32b137a79c22ae5d9f6dcb3c7a455e4", "status": "passed", "time": {"start": 1754484836336, "stop": 1754484850464, "duration": 14128}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c32b137a79c22ae5d9f6dcb3c7a455e4"}], "uid": "9ab207a1a50592cfc97f93c48ff061e9"}, {"name": "test_could_you_please_search_an_for_me", "children": [{"name": "TestEllaCouldYouPleaseSearchAnMe", "children": [{"name": "测试could you please search an for me能正常执行", "uid": "f990be4b0bb6b336", "parentUid": "05954a2ceae7fd66d1757bbd59b47ed6", "status": "failed", "time": {"start": 1754484863011, "stop": 1754484878667, "duration": 15656}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "05954a2ceae7fd66d1757bbd59b47ed6"}], "uid": "18eeff7a50c6893d6191e5b7834fb912"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "TestEllaDisableMagicVoiceChanger", "children": [{"name": "测试disable magic voice changer能正常执行", "uid": "21f356a729ddd2", "parentUid": "130a6b251dee0788fb7cea69ff620c68", "status": "passed", "time": {"start": 1754484891757, "stop": 1754484905166, "duration": 13409}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "130a6b251dee0788fb7cea69ff620c68"}], "uid": "d6678d1bee0dacb44840d09dee8fdbd2"}, {"name": "test_give_me_some_money", "children": [{"name": "TestEllaGiveMeSomeMoney", "children": [{"name": "测试give me some money能正常执行", "uid": "a2c6fbbe98591301", "parentUid": "112c6b7b50b46672fc16704c6fc8c8ea", "status": "passed", "time": {"start": 1754484917871, "stop": 1754484935237, "duration": 17366}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "112c6b7b50b46672fc16704c6fc8c8ea"}], "uid": "cef13fbaa76291ab7ffe428fd9b3ff0a"}, {"name": "test_global_gdp_trends", "children": [{"name": "TestEllaGlobalGdpTrends", "children": [{"name": "测试global gdp trends能正常执行", "uid": "67c045159680a9ba", "parentUid": "831198a93942962c147b23029d2ab9c8", "status": "passed", "time": {"start": 1754484947633, "stop": 1754484964615, "duration": 16982}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "831198a93942962c147b23029d2ab9c8"}], "uid": "fff3da9e7aaa932288c845a77dbb22ae"}, {"name": "test_hello_hello", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试hello hello能正常执行", "uid": "f297484465772a4b", "parentUid": "ba652584bc7b97ea54da02ced4645ba5", "status": "passed", "time": {"start": 1754484977458, "stop": 1754484992659, "duration": 15201}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ba652584bc7b97ea54da02ced4645ba5"}], "uid": "ce7aabb9954dd4026695310de1d225b1"}, {"name": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "children": [{"name": "TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit", "children": [{"name": "测试Help me write an email to make an appointment for a visit能正常执行", "uid": "bc549bf9de755daa", "parentUid": "f60519e8aeef426bc3a93755c572d6fa", "status": "passed", "time": {"start": 1754485005516, "stop": 1754485032127, "duration": 26611}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f60519e8aeef426bc3a93755c572d6fa"}], "uid": "e821be54d27a42f20fe55cd5d6203630"}, {"name": "test_hi", "children": [{"name": "TestEllaHi", "children": [{"name": "测试hi能正常执行", "uid": "5ea29cde835aec90", "parentUid": "1223273ba8f6a08b5ed65920246c9e1b", "status": "passed", "time": {"start": 1754485044995, "stop": 1754485060219, "duration": 15224}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1223273ba8f6a08b5ed65920246c9e1b"}], "uid": "29e62e7f369af137801d22a9b259e4ec"}, {"name": "test_how_is_the_weather_today", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试how is the weather today能正常执行", "uid": "4f2d2b6c9aaf70e6", "parentUid": "34ffa36d6317bc520ea4d2709c2ddf11", "status": "passed", "time": {"start": 1754485073283, "stop": 1754485091642, "duration": 18359}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34ffa36d6317bc520ea4d2709c2ddf11"}], "uid": "9cf284f12ed28b2464e982b240e1a734"}, {"name": "test_how_is_the_wheather_today", "children": [{"name": "TestEllaHowIsWheatherToday", "children": [{"name": "测试how is the wheather today能正常执行", "uid": "9925076591462c7e", "parentUid": "d29828ee15804f262c19aa832269e66a", "status": "passed", "time": {"start": 1754485104484, "stop": 1754485118022, "duration": 13538}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d29828ee15804f262c19aa832269e66a"}], "uid": "23a13ba389e295288e7459ad4f05d4d2"}, {"name": "test_how_s_the_weather_today", "children": [{"name": "TestEllaHowSWeatherToday", "children": [{"name": "测试how's the weather today?返回正确的不支持响应", "uid": "98a23a7493ce922d", "parentUid": "862e57348213f2baefa0f1dca30e6cac", "status": "passed", "time": {"start": 1754485130948, "stop": 1754485148953, "duration": 18005}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "862e57348213f2baefa0f1dca30e6cac"}], "uid": "e9f1cfec1579f5ee85e18450ed232a94"}, {"name": "test_how_s_the_weather_today_in_shanghai", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试how's the weather today in shanghai能正常执行", "uid": "8cc2b9f9f777deb5", "parentUid": "6278997b6a5cd9b65afdbc6961c1f923", "status": "passed", "time": {"start": 1754485161947, "stop": 1754485180586, "duration": 18639}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6278997b6a5cd9b65afdbc6961c1f923"}], "uid": "0de8d8ddf42930e480591e30731da5d4"}, {"name": "test_how_to_say_hello_in_french", "children": [{"name": "TestEllaHowSayHelloFrench", "children": [{"name": "测试how to say hello in french能正常执行", "uid": "3b03094079bed1b9", "parentUid": "5667c881ac1997f398af2c0cc0dc170c", "status": "passed", "time": {"start": 1754485193306, "stop": 1754485205264, "duration": 11958}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5667c881ac1997f398af2c0cc0dc170c"}], "uid": "594bdcb49fa434818b399bc139ed191f"}, {"name": "test_how_to_say_i_love_you_in_french", "children": [{"name": "TestEllaHowSayILoveYouFrench", "children": [{"name": "测试how to say i love you in french能正常执行", "uid": "e1e7bd584d2312c5", "parentUid": "9966bad905bebb015e3efc2cbe564d9a", "status": "passed", "time": {"start": 1754485218115, "stop": 1754485230850, "duration": 12735}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9966bad905bebb015e3efc2cbe564d9a"}], "uid": "403c67e2055acbe3d575b2a0bb5e3e20"}, {"name": "test_i_wanna_be_rich", "children": [{"name": "TestEllaIWannaBeRich", "children": [{"name": "测试i wanna be rich能正常执行", "uid": "bfef13c8ba7b18c5", "parentUid": "c7de20f7cd05ebdbc63478eed16102dc", "status": "passed", "time": {"start": 1754485243754, "stop": 1754485260914, "duration": 17160}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c7de20f7cd05ebdbc63478eed16102dc"}], "uid": "ec547ced2ed20bbbff5a2043b9e6319c"}, {"name": "test_i_want_to_make_a_call", "children": [{"name": "TestEllaIWantMakeCall", "children": [{"name": "测试i want to make a call能正常执行", "uid": "a101b5b4fa37ac44", "parentUid": "503f251f10534ae24ea758298ae39419", "status": "passed", "time": {"start": 1754485273623, "stop": 1754485293097, "duration": 19474}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "503f251f10534ae24ea758298ae39419"}], "uid": "9643231b759e5225f7987ba2484e3f41"}, {"name": "test_i_want_to_watch_fireworks", "children": [{"name": "TestEllaIWantWatchFireworks", "children": [{"name": "测试i want to watch fireworks能正常执行", "uid": "a51394d164c1c753", "parentUid": "a62423648b24979e9d41076cc7fc4719", "status": "passed", "time": {"start": 1754485305791, "stop": 1754485322166, "duration": 16375}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a62423648b24979e9d41076cc7fc4719"}], "uid": "0c36eef7e897f644d53145d2fdea584f"}, {"name": "test_introduce_yourself", "children": [{"name": "TestEllaIntroduceYourself", "children": [{"name": "测试introduce yourself能正常执行", "uid": "191338734ff35e0", "parentUid": "af550fbfe44b78ae5ecace3f2e6c80de", "status": "passed", "time": {"start": 1754485335076, "stop": 1754485350572, "duration": 15496}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "af550fbfe44b78ae5ecace3f2e6c80de"}], "uid": "fd2fbebbb7b47e097121b9ff95e07e74"}, {"name": "test_make_a_call", "children": [{"name": "TestEllaMakeCall", "children": [{"name": "测试make a call能正常执行", "uid": "4515fe1422f6f89b", "parentUid": "41b39c88fd91c3d3a8968837a65fbbfa", "status": "passed", "time": {"start": 1754485363318, "stop": 1754485383716, "duration": 20398}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "41b39c88fd91c3d3a8968837a65fbbfa"}], "uid": "4c53e2e35617ddca6d4c7ed877b594a7"}, {"name": "test_open_app", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "423f6df0e82ce860", "parentUid": "ef90c8fa468d5ca1174d3ecae675d779", "status": "passed", "time": {"start": 1754485396679, "stop": 1754485411104, "duration": 14425}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ef90c8fa468d5ca1174d3ecae675d779"}], "uid": "13e5ce3e437fc3eba1f54c55b67226eb"}, {"name": "test_play_news", "children": [{"name": "TestEllaOpenPlayNews", "children": [{"name": "测试play news", "uid": "10caeac7f5359c96", "parentUid": "bfe747e7270f83431a268436bcbe9627", "status": "passed", "time": {"start": 1754485424127, "stop": 1754485440318, "duration": 16191}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfe747e7270f83431a268436bcbe9627"}], "uid": "f2cee0c061bba4cacfb9848756bf2374"}, {"name": "test_play_political_news", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play political news", "uid": "eaf54f5c3ee7d63", "parentUid": "********************************", "status": "passed", "time": {"start": 1754485453466, "stop": 1754485469430, "duration": 15964}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "********************************"}], "uid": "f61bac7c6bed9fe60b44871a005ea908"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "TestEllaSearchingForAMethodOfViolentMurder", "children": [{"name": "测试searching for a method of violent murder能正常执行", "uid": "a34c28ac75d4d56f", "parentUid": "cbc32a4217884f390f94d0aa2f25e4bf", "status": "broken", "time": {"start": 1754485482383, "stop": 1754485498034, "duration": 15651}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cbc32a4217884f390f94d0aa2f25e4bf"}], "uid": "190f97aef35915433ff6090f9e1e18d1"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "aa18187ae31ed8c0", "parentUid": "0d050c8fb5d5c2955b5389a59f902029", "status": "failed", "time": {"start": 1754485511220, "stop": 1754485527264, "duration": 16044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d050c8fb5d5c2955b5389a59f902029"}], "uid": "42e4c350f9cb5b725ff9a0629fd707eb"}, {"name": "test_show_me_premier_le<PERSON><PERSON>_goal_ranking", "children": [{"name": "TestEllaShowMePremierLeaguageGoalRanking", "children": [{"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "6dc578f8342571a5", "parentUid": "b676a8b132f27f862cb2d5f96e1e6e5b", "status": "failed", "time": {"start": 1754485540658, "stop": 1754485555831, "duration": 15173}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b676a8b132f27f862cb2d5f96e1e6e5b"}], "uid": "0d9eb6607628afa8c2895d0b987c95d9"}, {"name": "test_show_scores_between_livepool_and_manchester_city", "children": [{"name": "TestEllaShowScoresBetweenLivepoolManchesterCity", "children": [{"name": "测试show scores between livepool and manchester city能正常执行", "uid": "142b184b00093dd8", "parentUid": "00794312e310e31963198853503abb78", "status": "failed", "time": {"start": 1754485568848, "stop": 1754485583117, "duration": 14269}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "00794312e310e31963198853503abb78"}], "uid": "5f0fdb9ab1509aa4e4013922ca24fdb8"}, {"name": "test_stop_music", "children": [{"name": "TestEllaStopMusic", "children": [{"name": "测试stop music能正常执行", "uid": "d6ce3a1657495012", "parentUid": "7b0d317bdb65f00d77c3ccf77052f972", "status": "failed", "time": {"start": 1754485596139, "stop": 1754485609185, "duration": 13046}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b0d317bdb65f00d77c3ccf77052f972"}], "uid": "92323b24a83f37088059a15dd2e70612"}, {"name": "test_stop_run", "children": [{"name": "TestEllaStopRun", "children": [{"name": "测试stop run能正常执行", "uid": "d04ff44af557a074", "parentUid": "c3342d9dce68675b0ffe24e49dc3ccbb", "status": "passed", "time": {"start": 1754485622231, "stop": 1754485635679, "duration": 13448}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c3342d9dce68675b0ffe24e49dc3ccbb"}], "uid": "224850ea60208f7ca8a6e6ce15d4c0eb"}, {"name": "test_stop_workout", "children": [{"name": "TestEllaStopWorkout", "children": [{"name": "测试stop workout能正常执行", "uid": "69ec99d828b27505", "parentUid": "d09dcb3a4549f5f7f1d86fc4e386f4bf", "status": "passed", "time": {"start": 1754485648511, "stop": 1754485662811, "duration": 14300}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d09dcb3a4549f5f7f1d86fc4e386f4bf"}], "uid": "d886488db4cad9621ae720cb85d9a863"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "TestEllaSummarizeContentThisPage", "children": [{"name": "测试summarize content on this page能正常执行", "uid": "cb536fc6dbd8afa0", "parentUid": "98b21f71a48feae9b99edfcfa187ba23", "status": "passed", "time": {"start": 1754485675842, "stop": 1754485690128, "duration": 14286}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98b21f71a48feae9b99edfcfa187ba23"}], "uid": "8ca3f16dcd6c82e9d2735b11b5d0cef8"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "TestEllaSummarizeWhatIMReading", "children": [{"name": "测试summarize what i'm reading能正常执行", "uid": "ac9142d504d30e72", "parentUid": "83af552ea5df666561ceadf107b37508", "status": "passed", "time": {"start": 1754485703002, "stop": 1754485716573, "duration": 13571}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83af552ea5df666561ceadf107b37508"}], "uid": "34ae398183d59ded56dd80b7bf08775a"}, {"name": "test_take_a_joke", "children": [{"name": "TestEllaTakeJoke", "children": [{"name": "测试take a joke能正常执行", "uid": "dc792c51ad45908a", "parentUid": "3acb54c6ceb30b54183a40536a4da62b", "status": "passed", "time": {"start": 1754485729515, "stop": 1754485744736, "duration": 15221}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3acb54c6ceb30b54183a40536a4da62b"}], "uid": "c6397f27fa8b05537072fa113b8c3fc8"}, {"name": "test_take_a_note_on_how_to_build_a_treehouse", "children": [{"name": "TestEllaTakeNoteHowBuildTreehouse", "children": [{"name": "测试take a note on how to build a treehouse能正常执行", "uid": "2238b295fb197f63", "parentUid": "36906abdedd73fbaf2f1118b2b081f40", "status": "passed", "time": {"start": 1754485757660, "stop": 1754485771164, "duration": 13504}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "36906abdedd73fbaf2f1118b2b081f40"}], "uid": "dd58ab9d8bbd4cb87632d02b4aaab257"}, {"name": "test_take_notes_on_how_to_build_a_treehouse", "children": [{"name": "TestEllaTakeNotesHowBuildTreehouse", "children": [{"name": "测试take notes on how to build a treehouse能正常执行", "uid": "18284c5918c912a5", "parentUid": "521cf47a0da1175d33dabbff6602fa38", "status": "passed", "time": {"start": 1754485783943, "stop": 1754485797292, "duration": 13349}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "521cf47a0da1175d33dabbff6602fa38"}], "uid": "e2b5971fa35ed9c2de1613d7fffb9817"}, {"name": "test_tell_me_a_joke", "children": [{"name": "TestEllaTellMeJoke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "f4753f2d44bd35b5", "parentUid": "f4f36a0e5ab72d1f1ffe809d3354f0d7", "status": "failed", "time": {"start": 1754485810136, "stop": 1754485825927, "duration": 15791}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f4f36a0e5ab72d1f1ffe809d3354f0d7"}], "uid": "9dabb9d428e867eabdc2d3a69afdd429"}, {"name": "test_video_call_mom_through_whatsapp", "children": [{"name": "TestEllaVideoCallMomThroughWhatsapp", "children": [{"name": "测试video call mom through whatsapp能正常执行", "uid": "b7779889ffdf936e", "parentUid": "e9a671f0e7f7a5d9dd84e36b17c5b9d6", "status": "failed", "time": {"start": 1754485838917, "stop": 1754485858507, "duration": 19590}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9a671f0e7f7a5d9dd84e36b17c5b9d6"}], "uid": "52150962482f63ecf4ad855e891df94e"}, {"name": "test_what_is_apec", "children": [{"name": "TestEllaWhatIsApec", "children": [{"name": "测试what is apec?能正常执行", "uid": "543154d5028ed6d", "parentUid": "1e37b5c5f74c73b4b983a5a9a1939ee9", "status": "passed", "time": {"start": 1754485871627, "stop": 1754485888403, "duration": 16776}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1e37b5c5f74c73b4b983a5a9a1939ee9"}], "uid": "c36e05a3608c90521de87174b028a2f3"}, {"name": "test_what_languages_do_you_support", "children": [{"name": "TestEllaWhatLanguagesDoYouSupport", "children": [{"name": "测试What languages do you support能正常执行", "uid": "a2181f30c28e43a1", "parentUid": "0aa09f1aa9e12279998425a7031b2afb", "status": "passed", "time": {"start": 1754485901463, "stop": 1754485914737, "duration": 13274}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0aa09f1aa9e12279998425a7031b2afb"}], "uid": "53246026784adbe982eaaaa893c66882"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试what's the weather like in shanghai today能正常执行", "uid": "24be109e76a80bd6", "parentUid": "abdf90845d6ece7e59853b1633b70976", "status": "passed", "time": {"start": 1754485927315, "stop": 1754485945789, "duration": 18474}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "abdf90845d6ece7e59853b1633b70976"}], "uid": "8d57bd429c231f391c68fd5f296df48d"}, {"name": "test_what_s_the_weather_today", "children": [{"name": "TestEllaWhatSWeatherToday", "children": [{"name": "测试what·s the weather today？能正常执行", "uid": "3eabaf55f35a4e66", "parentUid": "1aa11a1add75d7b42e4f5f940b55235c", "status": "passed", "time": {"start": 1754485958650, "stop": 1754485979502, "duration": 20852}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1aa11a1add75d7b42e4f5f940b55235c"}], "uid": "e75c3dcc6971391aa4cea847d0c3df26"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "TestEllaWhatSWheatherToday", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "f9dec29930734510", "parentUid": "b0d78f07238a99d14c63a09a132b62ac", "status": "failed", "time": {"start": 1754485992367, "stop": 1754486007276, "duration": 14909}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0d78f07238a99d14c63a09a132b62ac"}], "uid": "df9827022acfcacb6cb91f3eade022ae"}, {"name": "test_what_s_your_name", "children": [{"name": "TestEllaWhatSYourName", "children": [{"name": "测试what's your name？能正常执行", "uid": "301099c31151442e", "parentUid": "3d8e08ff740579156ca205ed22a4756d", "status": "passed", "time": {"start": 1754486020217, "stop": 1754486033584, "duration": 13367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3d8e08ff740579156ca205ed22a4756d"}], "uid": "5920d33b40456d715947fca7a27a7717"}, {"name": "test_what_time_is_it_now", "children": [{"name": "TestEllaWhatTimeIsItNow", "children": [{"name": "测试what time is it now能正常执行", "uid": "9f14381c863cf0cc", "parentUid": "164a179950ff8bd6ebd07029c9858807", "status": "passed", "time": {"start": 1754486046355, "stop": 1754486059787, "duration": 13432}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "164a179950ff8bd6ebd07029c9858807"}], "uid": "809dcfb95d068444fb46fbf13d07f70d"}, {"name": "test_whats_the_weather_today", "children": [{"name": "TestEllaWhatsWeatherToday", "children": [{"name": "测试whats the weather today能正常执行", "uid": "d427b58b6fde626", "parentUid": "6cc45efb201d9209b1a6f8794f17a6ed", "status": "passed", "time": {"start": 1754486072773, "stop": 1754486090943, "duration": 18170}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6cc45efb201d9209b1a6f8794f17a6ed"}], "uid": "82328665eb7ef47d6e8c2fd0919234af"}, {"name": "test_who_is_harry_potter", "children": [{"name": "TestEllaWhoIsHarry<PERSON>otter", "children": [{"name": "测试who is harry potter能正常执行", "uid": "a4a4e1574a0da588", "parentUid": "b5f5061dbe131241d2697c1336ee4d0f", "status": "passed", "time": {"start": 1754486103923, "stop": 1754486120135, "duration": 16212}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b5f5061dbe131241d2697c1336ee4d0f"}], "uid": "acd61d814ff9a72ba57d12465068a2b4"}, {"name": "test_who_is_j_k_rowling", "children": [{"name": "TestEllaWhoIsJKRowling", "children": [{"name": "测试who is j k rowling能正常执行", "uid": "44baa1a8a8e2a655", "parentUid": "bfb9c688e10f1ae6ddcd570a4f050325", "status": "passed", "time": {"start": 1754486133031, "stop": 1754486150400, "duration": 17369}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfb9c688e10f1ae6ddcd570a4f050325"}], "uid": "cb98f3056a0e33f548dde9b60e938b6a"}, {"name": "test_why_is_my_phone_not_ringing_on_incoming_calls", "children": [{"name": "TestEllaWhyIsMyPhoneNotRingingIncomingCalls", "children": [{"name": "测试why is my phone not ringing on incoming calls能正常执行", "uid": "76ccf467a41e6ff5", "parentUid": "cdaa83f7b575bd82cf726da90db761c8", "status": "passed", "time": {"start": 1754486163336, "stop": 1754486183062, "duration": 19726}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cdaa83f7b575bd82cf726da90db761c8"}], "uid": "a302ef9f7c0c93aaaa0b2857fe578fbe"}, {"name": "test_why_my_charging_is_so_slow", "children": [{"name": "TestEllaWhyMyChargingIsSoSlow", "children": [{"name": "测试why my charging is so slow能正常执行", "uid": "b9c6c8bfe1404e7", "parentUid": "3750969afddcdbdadfd545f67aca7706", "status": "failed", "time": {"start": 1754486196007, "stop": 1754486210302, "duration": 14295}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3750969afddcdbdadfd545f67aca7706"}], "uid": "48e6e701074c76202e2d7a86eeb1f71a"}], "uid": "0a5f897bb744ec2f8b960fc5954cddf6"}, {"name": "testcases.test_ella.system_coupling", "children": [{"name": "test_adjustment_the_brightness_to", "children": [{"name": "TestEllaAdjustmentBrightness", "children": [{"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "b3a0985d3b8f219c", "parentUid": "13406d3249e45c3f0440290f62a0a585", "status": "failed", "time": {"start": 1754486223471, "stop": 1754486237302, "duration": 13831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "13406d3249e45c3f0440290f62a0a585"}], "uid": "169492529a068768aa4c236be730e885"}, {"name": "test_boost_phone", "children": [{"name": "TestEllaBoostPhone", "children": [{"name": "测试boost phone能正常执行", "uid": "6630dd6507ad295e", "parentUid": "6b73e18bf77dcabfceaad2f004e2aecf", "status": "passed", "time": {"start": 1754486250363, "stop": 1754486264340, "duration": 13977}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6b73e18bf77dcabfceaad2f004e2aecf"}], "uid": "5b2341e5eca7df187d07a4073eb501b0"}, {"name": "test_check_front_camera_information", "children": [{"name": "TestEllaCheckFrontCameraInformation", "children": [{"name": "测试check front camera information能正常执行", "uid": "1dd78894947cdfc0", "parentUid": "12ceb3f2ca52b0e705dbde45ff23637e", "status": "failed", "time": {"start": 1754486277352, "stop": 1754486296726, "duration": 19374}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12ceb3f2ca52b0e705dbde45ff23637e"}], "uid": "900d85dd19a0727e9711d6cd307efe61"}, {"name": "test_clear_junk_files", "children": [{"name": "TestEllaClearJunkFiles", "children": [{"name": "测试clear junk files命令", "uid": "6b30492325e06f55", "parentUid": "d96f4287167e7edbf6a73f9393984a7d", "status": "passed", "time": {"start": 1754486309611, "stop": 1754486339344, "duration": 29733}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d96f4287167e7edbf6a73f9393984a7d"}], "uid": "4c6f4bc8bf308c30c67ce68d84340b5b"}, {"name": "test_close_bluetooth", "children": [{"name": "TestEllaCloseBluetooth", "children": [{"name": "测试close bluetooth能正常执行", "uid": "c4fafbf029b57c35", "parentUid": "1d722ccfa222e03cef528f38b7d2fac2", "status": "passed", "time": {"start": 1754486352403, "stop": 1754486367731, "duration": 15328}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1d722ccfa222e03cef528f38b7d2fac2"}], "uid": "aa4164fbbe23d76eaa9669ca854678b0"}, {"name": "test_close_flashlight", "children": [{"name": "TestEllaCloseFlashlight", "children": [{"name": "测试close flashlight能正常执行", "uid": "57ba6f253fe4a6a6", "parentUid": "d82496a02dca69564564d1692d50a7e6", "status": "passed", "time": {"start": 1754486380541, "stop": 1754486396481, "duration": 15940}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d82496a02dca69564564d1692d50a7e6"}], "uid": "82ec45c00676757f05c50c06f422bf20"}, {"name": "test_countdown_min", "children": [{"name": "TestEllaCountdownMin", "children": [{"name": "测试countdown 5 min能正常执行", "uid": "56d2b9e1c2fc39f", "parentUid": "45aee82a630fdec483043c1cf3f74bf1", "status": "failed", "time": {"start": 1754486409251, "stop": 1754486426730, "duration": 17479}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "45aee82a630fdec483043c1cf3f74bf1"}], "uid": "f6b105e104089e68c280ff5c4fa1d393"}, {"name": "test_decrease_the_brightness", "children": [{"name": "TestEllaDecreaseBrightness", "children": [{"name": "测试decrease the brightness能正常执行", "uid": "72e5b9b0b1fda4b5", "parentUid": "375849c4ecfe2bbf01bcecaaef84c8db", "status": "passed", "time": {"start": 1754486439616, "stop": 1754486454073, "duration": 14457}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "375849c4ecfe2bbf01bcecaaef84c8db"}], "uid": "c7d1062159fa1a8529d01de2304b5ef9"}, {"name": "test_end_screen_recording", "children": [{"name": "TestEllaTurnScreenRecord", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "86fa5010cef990cf", "parentUid": "cb8ba3420a3f42d8dc07018eb937d89c", "status": "passed", "time": {"start": 1754486466855, "stop": 1754486483899, "duration": 17044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "e06d746a0349fe32", "parentUid": "cb8ba3420a3f42d8dc07018eb937d89c", "status": "passed", "time": {"start": 1754486496685, "stop": 1754486515106, "duration": 18421}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cb8ba3420a3f42d8dc07018eb937d89c"}], "uid": "80f3221929ed1bf33f33827613bd2441"}, {"name": "test_help_me_take_a_long_screenshot", "children": [{"name": "TestEllaHelpMeTakeLongScreenshot", "children": [{"name": "测试help me take a long screenshot能正常执行", "uid": "324c32662b1b44a7", "parentUid": "eddf309b25a6b37f2ead731622b025cd", "status": "passed", "time": {"start": 1754486527843, "stop": 1754486546055, "duration": 18212}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eddf309b25a6b37f2ead731622b025cd"}], "uid": "2f4262c05e172e74fc2c7093e52387d3"}, {"name": "test_help_me_take_a_screenshot", "children": [{"name": "TestEllaHelpMeTakeScreenshot", "children": [{"name": "测试help me take a screenshot能正常执行", "uid": "ec98df5d96735ef2", "parentUid": "1b701ed7fafa2b81e3b507e55819c327", "status": "passed", "time": {"start": 1754486558792, "stop": 1754486575565, "duration": 16773}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b701ed7fafa2b81e3b507e55819c327"}], "uid": "b0951018c5f4d74c567ddd6d37b5afe7"}, {"name": "test_long_screenshot", "children": [{"name": "TestEllaLongScreenshot", "children": [{"name": "测试long screenshot能正常执行", "uid": "6f950886f9130f1d", "parentUid": "10001a71b5e68735481b40decacdb66b", "status": "passed", "time": {"start": 1754486588509, "stop": 1754486605351, "duration": 16842}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "10001a71b5e68735481b40decacdb66b"}], "uid": "fd90b40ba6ffaaec61bd1bf8a6dfb743"}, {"name": "test_maximum_volume", "children": [{"name": "TestEllaMaximumVolume", "children": [{"name": "测试maximum volume能正常执行", "uid": "56bfc1a59701e4b9", "parentUid": "6dba0e91979783ed4ec530b3d80f6368", "status": "passed", "time": {"start": 1754486618078, "stop": 1754486633129, "duration": 15051}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6dba0e91979783ed4ec530b3d80f6368"}], "uid": "42069d56eafe8c2317edc83b4e050135"}, {"name": "test_memory_cleanup", "children": [{"name": "TestEllaMemoryCleanup", "children": [{"name": "测试memory cleanup能正常执行", "uid": "12433c483fa32d60", "parentUid": "402a46f59edfad113bb75993c3b1ee0a", "status": "passed", "time": {"start": 1754486646004, "stop": 1754486671642, "duration": 25638}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "402a46f59edfad113bb75993c3b1ee0a"}], "uid": "b4111ca5d9e6c15c7f70fd341fca204a"}, {"name": "test_minimum_volume", "children": [{"name": "TestEllaMinimumVolume", "children": [{"name": "测试minimum volume能正常执行", "uid": "14594ed883fdf82e", "parentUid": "8ea78a3baf10b4390053ed171e147a99", "status": "passed", "time": {"start": 1754486684458, "stop": 1754486699961, "duration": 15503}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8ea78a3baf10b4390053ed171e147a99"}], "uid": "5f5ae3dc5f88288d2dab05294be5ba53"}, {"name": "test_open_bluetooth", "children": [{"name": "TestEllaOpenBluetooth", "children": [{"name": "测试open bluetooth", "uid": "bf9c38fe63dfb10f", "parentUid": "ce97beca6f71d794ad4aa2488c2a51cc", "status": "passed", "time": {"start": 1754486713130, "stop": 1754486727490, "duration": 14360}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ce97beca6f71d794ad4aa2488c2a51cc"}], "uid": "ff64db128f121f9873fe4471e4dffdb3"}, {"name": "test_open_bt", "children": [{"name": "TestEllaOpenBluetooth", "children": [{"name": "测试open bt", "uid": "38eadf37eecb0101", "parentUid": "cff090d0294d065259343d3c0be3e870", "status": "passed", "time": {"start": 1754486740453, "stop": 1754486754434, "duration": 13981}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cff090d0294d065259343d3c0be3e870"}], "uid": "889f90fb090c14dc6df34ad6387bda95"}, {"name": "test_open_flashlight", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open flashlight", "uid": "b1fe075113ee100d", "parentUid": "bc7827d0911898d37df7fcf78b433e01", "status": "passed", "time": {"start": 1754486767570, "stop": 1754486783909, "duration": 16339}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bc7827d0911898d37df7fcf78b433e01"}], "uid": "dbd0fd96c304d0311b6a78f5c9f6d7a2"}, {"name": "test_open_wifi", "children": [{"name": "TestEllaOpenWifi", "children": [{"name": "测试open wifi", "uid": "1ccd3d9645a92f1f", "parentUid": "4f8addb21351f5f5c147c6f32359e6f8", "status": "passed", "time": {"start": 1754486796964, "stop": 1754486811101, "duration": 14137}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4f8addb21351f5f5c147c6f32359e6f8"}], "uid": "732e1a744ff2476f886f4f9095220be6"}, {"name": "test_power_saving", "children": [{"name": "TestEllaPowerSaving", "children": [{"name": "测试power saving能正常执行", "uid": "24ed300ebceea57f", "parentUid": "cca844da29af41e39df266a7ebd5b861", "status": "passed", "time": {"start": 1754486823934, "stop": 1754486848967, "duration": 25033}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cca844da29af41e39df266a7ebd5b861"}], "uid": "b3608e6af09c4f99081f13cadb0a5d44"}, {"name": "test_screen_record", "children": [{"name": "TestEllaScreenRecord", "children": [{"name": "测试screen record能正常执行", "uid": "fbf4ec4a014b304c", "parentUid": "26f506cb56eae899b201e1de8c38bd6f", "status": "passed", "time": {"start": 1754486861875, "stop": 1754486880906, "duration": 19031}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "9f545d6fdce360ca", "parentUid": "26f506cb56eae899b201e1de8c38bd6f", "status": "passed", "time": {"start": 1754486893641, "stop": 1754486911064, "duration": 17423}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "26f506cb56eae899b201e1de8c38bd6f"}], "uid": "e1332233b220a65d2fdc0ae00a593bdc"}, {"name": "test_set_a_timer_for_minutes", "children": [{"name": "TestEllaSetTimerMinutes", "children": [{"name": "测试set a timer for 10 minutes能正常执行", "uid": "c81e6489bc0d344c", "parentUid": "db45051bfa0b770acce3d9a7a9b34c52", "status": "passed", "time": {"start": 1754486923814, "stop": 1754486941667, "duration": 17853}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "db45051bfa0b770acce3d9a7a9b34c52"}], "uid": "bea19f466921b3036054c922629fe91e"}, {"name": "test_set_alarm_for_10_o_clock", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试set alarm for 10 o'clock", "uid": "e4fcecfda38c470d", "parentUid": "a2954744eacf1d066c2b6177f8028ee2", "status": "passed", "time": {"start": 1754486954332, "stop": 1754486972024, "duration": 17692}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a2954744eacf1d066c2b6177f8028ee2"}], "uid": "5136d3ddad5af9fa04f4391a520c3b0d"}, {"name": "test_set_battery_saver_setting", "children": [{"name": "TestEllaSetBatterySaverSetting", "children": [{"name": "测试set Battery Saver setting能正常执行", "uid": "a68a358b1eadc5f5", "parentUid": "b61ff8d9b726452f92d852b371aa6a90", "status": "failed", "time": {"start": 1754486984685, "stop": 1754487005808, "duration": 21123}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b61ff8d9b726452f92d852b371aa6a90"}], "uid": "28b4581513f8589b378bc346015a9490"}, {"name": "test_smart_charge", "children": [{"name": "TestEllaSmartCharge", "children": [{"name": "测试smart charge能正常执行", "uid": "5f63eb7a21dc6418", "parentUid": "41f573a06c0e20aa83d29445f512857c", "status": "failed", "time": {"start": 1754487018773, "stop": 1754487033255, "duration": 14482}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "41f573a06c0e20aa83d29445f512857c"}], "uid": "0669c1c9bf3fff5019832bc329d03550"}, {"name": "test_start_record", "children": [{"name": "TestEllaStartRecord", "children": [{"name": "测试start record能正常执行", "uid": "937f207343b8f0c2", "parentUid": "fad9085499f3f4042c08d5be02eb6b0a", "status": "passed", "time": {"start": 1754487046321, "stop": 1754487063346, "duration": 17025}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "af1edd5c403db9e4", "parentUid": "fad9085499f3f4042c08d5be02eb6b0a", "status": "passed", "time": {"start": 1754487076214, "stop": 1754487094660, "duration": 18446}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fad9085499f3f4042c08d5be02eb6b0a"}], "uid": "0d2acbf80a2bbd78f39946ab4d920635"}, {"name": "test_start_screen_recording", "children": [{"name": "TestEllaStartScreenRecording", "children": [{"name": "测试start screen recording能正常执行", "uid": "93bd3202f99050e7", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1754487107718, "stop": 1754487125254, "duration": 17536}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause screen recording能正常执行", "uid": "f8f79e89f6eee656", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1754487138274, "stop": 1754487155076, "duration": 16802}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "continue  screen recording能正常执行", "uid": "28d8f9a0379236cb", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1754487168183, "stop": 1754487185107, "duration": 16924}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "dc6a66865aca9b84", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1754487198377, "stop": 1754487216856, "duration": 18479}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "94605bd37b622971e2f6d1306d537ac9"}], "uid": "9210a41e638d65f3b50b4de9468577cb"}, {"name": "test_stop_recording", "children": [{"name": "TestEllaTurnScreenRecord", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "5c6774d94dd8b0dd", "parentUid": "6d9aa92a00ed0065f1758dce8f814a21", "status": "passed", "time": {"start": 1754487230107, "stop": 1754487247631, "duration": 17524}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "156bf9e6b63b254c", "parentUid": "6d9aa92a00ed0065f1758dce8f814a21", "status": "passed", "time": {"start": 1754487261064, "stop": 1754487276226, "duration": 15162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d9aa92a00ed0065f1758dce8f814a21"}], "uid": "12d9ec9f3661e4a6cbb3393c17a142c0"}, {"name": "test_switch_charging_modes", "children": [{"name": "TestEllaSwitchChargingModes", "children": [{"name": "测试switch charging modes能正常执行", "uid": "7f6bcf1a52131d92", "parentUid": "9788e3a9971a6e6142c9edebcd8d6df5", "status": "failed", "time": {"start": 1754487289081, "stop": 1754487302980, "duration": 13899}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9788e3a9971a6e6142c9edebcd8d6df5"}], "uid": "9df29768698803a92c70ca28b324bcd6"}, {"name": "test_switch_magic_voice_to_grace", "children": [{"name": "TestEllaSwitchMagicVoiceGrace", "children": [{"name": "测试Switch Magic Voice to Grace能正常执行", "uid": "a4c00a2e50a210dd", "parentUid": "a68eab3c6aa1e1b791a77b31c94dd2f2", "status": "passed", "time": {"start": 1754487316406, "stop": 1754487330699, "duration": 14293}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a68eab3c6aa1e1b791a77b31c94dd2f2"}], "uid": "ee64f637be288a86beb1fb3a41d0f889"}, {"name": "test_switch_magic_voice_to_mango", "children": [{"name": "TestEllaSwitchMagicVoiceToMango", "children": [{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "ac3ea7385a27322b", "parentUid": "f890978c47c61f26513c6a80d7cd0497", "status": "passed", "time": {"start": 1754487343552, "stop": 1754487356601, "duration": 13049}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f890978c47c61f26513c6a80d7cd0497"}], "uid": "04bb98199ad164d25c90308e2b594563"}, {"name": "test_switch_to_barrage_notification", "children": [{"name": "TestEllaSwitchBarrageNotification", "children": [{"name": "测试Switch to Barrage Notification能正常执行", "uid": "a4197bd04ef3d35d", "parentUid": "4e744fe5421b1f85b2b48648f841c620", "status": "failed", "time": {"start": 1754487369682, "stop": 1754487385769, "duration": 16087}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4e744fe5421b1f85b2b48648f841c620"}], "uid": "81d75a279e51195a6bdcc1b7476061cc"}, {"name": "test_switch_to_default_mode", "children": [{"name": "TestEllaSwitchToDefaultMode", "children": [{"name": "测试switch to default mode能正常执行", "uid": "da92ff9a78d65b18", "parentUid": "7b329a8fe9abef3ed9e84e7ba123812d", "status": "passed", "time": {"start": 1754487399113, "stop": 1754487415490, "duration": 16377}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b329a8fe9abef3ed9e84e7ba123812d"}], "uid": "4c06db8cd2466afbff036b96a7b29084"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "TestEllaSwitchToEquilibriumMode", "children": [{"name": "测试switch to equilibrium mode能正常执行", "uid": "aa06e05449f42426", "parentUid": "76faa1b8f60ce1117990613102818bb9", "status": "failed", "time": {"start": 1754487428781, "stop": 1754487445077, "duration": 16296}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "76faa1b8f60ce1117990613102818bb9"}], "uid": "a631118d3a602f7957b9df1f61e89d8b"}, {"name": "test_switch_to_flash_notification", "children": [{"name": "TestEllaSwitchToFlashNotification", "children": [{"name": "测试switch to flash notification能正常执行", "uid": "4d5b68cf4e8d17f8", "parentUid": "28cdac4aed197c9ad9daa4c4660b871b", "status": "failed", "time": {"start": 1754487458459, "stop": 1754487478499, "duration": 20040}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "28cdac4aed197c9ad9daa4c4660b871b"}], "uid": "199a594d9e7fb8316cc2c05c01918204"}, {"name": "test_switch_to_hyper_charge", "children": [{"name": "TestEllaSwitchToHyperCharge", "children": [{"name": "测试Switch to Hyper Charge能正常执行", "uid": "948460c746878063", "parentUid": "65ad6f571b805fa05f9b55669abf1de1", "status": "failed", "time": {"start": 1754487491529, "stop": 1754487504641, "duration": 13112}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "65ad6f571b805fa05f9b55669abf1de1"}], "uid": "231bfdf13683acaff012f9cc475bdd12"}, {"name": "test_switch_to_low_temp_charge", "children": [{"name": "TestEllaSwitchToLowtempCharge", "children": [{"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "883c5d89069451b1", "parentUid": "c943e664fb1e3d8f2e7feabc9d26cf9b", "status": "failed", "time": {"start": 1754487517979, "stop": 1754487531460, "duration": 13481}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c943e664fb1e3d8f2e7feabc9d26cf9b"}], "uid": "f274d94636e10c97050c0bced04829d1"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "TestEllaSwitchToPowerSavingMode", "children": [{"name": "测试switch to power saving mode能正常执行", "uid": "bda91739b78aea60", "parentUid": "fa491a2fccbb92902b5fea6a0125d0dd", "status": "passed", "time": {"start": 1754487544598, "stop": 1754487558679, "duration": 14081}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fa491a2fccbb92902b5fea6a0125d0dd"}], "uid": "276d2d0724438fcbfe51444d46cecee8"}, {"name": "test_switch_to_smart_charge", "children": [{"name": "TestEllaSwitchToSmartCharge", "children": [{"name": "测试switch to smart charge能正常执行", "uid": "8ad6893c56a2881f", "parentUid": "f0ac9e1d670781c7273288548b5e912c", "status": "failed", "time": {"start": 1754487571731, "stop": 1754487584828, "duration": 13097}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f0ac9e1d670781c7273288548b5e912c"}], "uid": "5dbf89ce49106a865cad1e16016f18aa"}, {"name": "test_switched_to_data_mode", "children": [{"name": "TestEllaSwitchedDataMode", "children": [{"name": "测试switched to data mode能正常执行", "uid": "4f285330353aa837", "parentUid": "ee2a0ba313d9f55972191c0fc58a643b", "status": "failed", "time": {"start": 1754487597893, "stop": 1754487611697, "duration": 13804}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ee2a0ba313d9f55972191c0fc58a643b"}], "uid": "7f43f619728469165153ad2076591cb3"}, {"name": "test_take_a_photo", "children": [{"name": "TestEllaTakePhoto", "children": [{"name": "测试take a photo能正常执行", "uid": "bf068ac0f1aaa3f3", "parentUid": "491b8997a74c0d96f03949ae2b53cb5a", "status": "failed", "time": {"start": 1754487624970, "stop": 1754487655439, "duration": 30469}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "491b8997a74c0d96f03949ae2b53cb5a"}], "uid": "cf46440006651ab12729bd35922e17b6"}, {"name": "test_take_a_selfie", "children": [{"name": "TestEllaTakeSelfie", "children": [{"name": "测试take a selfie能正常执行", "uid": "7c428f481eb64859", "parentUid": "4605cd6537361d8c73216c0b79b6bdcd", "status": "failed", "time": {"start": 1754487668402, "stop": 1754487698993, "duration": 30591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4605cd6537361d8c73216c0b79b6bdcd"}], "uid": "458be1e68e81f7e0a5f24b2b8e711037"}, {"name": "test_the_battery_of_the_mobile_phone_is_too_low", "children": [{"name": "TestEllaBatteryMobilePhoneIsTooLow", "children": [{"name": "测试the battery of the mobile phone is too low能正常执行", "uid": "64ce3e31c1cf3f3f", "parentUid": "e9e63c79cb2e897c6c2ba74546e55ab8", "status": "passed", "time": {"start": 1754487711858, "stop": 1754487737322, "duration": 25464}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9e63c79cb2e897c6c2ba74546e55ab8"}], "uid": "7fec87272bec0f0286d76fc5e499fde9"}, {"name": "test_turn_down_ring_volume", "children": [{"name": "TestEllaTurnDownRingVolume", "children": [{"name": "测试turn down ring volume能正常执行", "uid": "f804cd13aef5ae6b", "parentUid": "c650a0dd4c418a43693046a1806d4660", "status": "failed", "time": {"start": 1754487750180, "stop": 1754487764062, "duration": 13882}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c650a0dd4c418a43693046a1806d4660"}], "uid": "b8d3945e930e3eeebfc2c83a67a45522"}, {"name": "test_turn_off_flashlight", "children": [{"name": "TestEllaTurnOffFlashlight", "children": [{"name": "测试turn off flashlight能正常执行", "uid": "ead7ca339b50310b", "parentUid": "11b19c6407e932f014698c24c4c16b7b", "status": "passed", "time": {"start": 1754487777334, "stop": 1754487792679, "duration": 15345}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11b19c6407e932f014698c24c4c16b7b"}], "uid": "83abfdb472ff182fcf9c9f70441da7db"}, {"name": "test_turn_off_light_theme", "children": [{"name": "TestEllaTurnOffLightTheme", "children": [{"name": "测试turn off light theme能正常执行", "uid": "5052494e445ea3a2", "parentUid": "7d565ca90fb003f1876217d97fc59c20", "status": "passed", "time": {"start": 1754487805511, "stop": 1754487819220, "duration": 13709}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7d565ca90fb003f1876217d97fc59c20"}], "uid": "2907e63ef40baa3266bf04ea46042c06"}, {"name": "test_turn_off_nfc", "children": [{"name": "TestEllaTurnOffNfc", "children": [{"name": "测试turn off nfc能正常执行", "uid": "d04bf43a76908a56", "parentUid": "140cc3d324fc5f756712663d6db76c28", "status": "passed", "time": {"start": 1754487832220, "stop": 1754487847428, "duration": 15208}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "140cc3d324fc5f756712663d6db76c28"}], "uid": "47184378bab69b86d9722f52ee466ee3"}, {"name": "test_turn_on_bluetooth", "children": [{"name": "TestEllaTurnBluetooth", "children": [{"name": "测试turn on bluetooth能正常执行", "uid": "2674d3ebdb6a082a", "parentUid": "50b13d10c6f48bcd3b710698be278545", "status": "passed", "time": {"start": 1754487860460, "stop": 1754487874263, "duration": 13803}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "50b13d10c6f48bcd3b710698be278545"}], "uid": "73521b2a406c05e4ea6775f8ac550b11"}, {"name": "test_turn_on_do_not_disturb_mode", "children": [{"name": "TestEllaTurnDoNotDisturbMode", "children": [{"name": "测试turn on do not disturb mode能正常执行", "uid": "9bbc7b793fb30846", "parentUid": "b24c19041d3913a347b45ddb7794d996", "status": "passed", "time": {"start": 1754487887406, "stop": 1754487901646, "duration": 14240}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b24c19041d3913a347b45ddb7794d996"}], "uid": "25300102bed6a3f35a171590779f5760"}, {"name": "test_turn_on_light_theme", "children": [{"name": "TestEllaTurnLightTheme", "children": [{"name": "测试turn on light theme能正常执行", "uid": "1d658bbc993404eb", "parentUid": "33d9ea5586f4fc177aea2dcfffa56adf", "status": "passed", "time": {"start": 1754487914418, "stop": 1754487928231, "duration": 13813}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "a231ab2220a212ac", "parentUid": "33d9ea5586f4fc177aea2dcfffa56adf", "status": "passed", "time": {"start": 1754487941157, "stop": 1754487955456, "duration": 14299}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "33d9ea5586f4fc177aea2dcfffa56adf"}], "uid": "491955b9c16082a7c444ca2d26b7637c"}, {"name": "test_turn_on_location_services", "children": [{"name": "TestEllaTurnLocationServices", "children": [{"name": "测试turn on location services能正常执行", "uid": "426f048698440e79", "parentUid": "b0fdf160b2ecb52332e6867a9d6becde", "status": "passed", "time": {"start": 1754487968462, "stop": 1754487982877, "duration": 14415}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0fdf160b2ecb52332e6867a9d6becde"}], "uid": "cf19f0ebd19888cf46f82f4e0bd4bfac"}, {"name": "test_turn_on_nfc", "children": [{"name": "TestEllaTurnNfc", "children": [{"name": "测试turn on nfc能正常执行", "uid": "ac959bcc15134bc9", "parentUid": "125b66f5b4fa0498985a1eb2ee60c6e7", "status": "passed", "time": {"start": 1754487996245, "stop": 1754488010760, "duration": 14515}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "125b66f5b4fa0498985a1eb2ee60c6e7"}], "uid": "5208de3eaa24aeb389307a172f7857eb"}, {"name": "test_turn_on_the_flashlight", "children": [{"name": "TestEllaTurnFlashlight", "children": [{"name": "测试turn on the flashlight能正常执行", "uid": "403e3a42990a4305", "parentUid": "a46ae606b16354d85bcc730dcdcbe96e", "status": "passed", "time": {"start": 1754488023989, "stop": 1754488039854, "duration": 15865}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a46ae606b16354d85bcc730dcdcbe96e"}], "uid": "5ff8e41b3404a9b3f6cce0bee037eb96"}, {"name": "test_turn_on_the_screen_record", "children": [{"name": "TestEllaTurnScreenRecord", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "d8abb8f7b66ff219", "parentUid": "678c4f3bd7c3cae1c7216386baae6d8b", "status": "passed", "time": {"start": 1754488052845, "stop": 1754488069899, "duration": 17054}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "c0ca4907bf7ec56c", "parentUid": "678c4f3bd7c3cae1c7216386baae6d8b", "status": "passed", "time": {"start": 1754488083064, "stop": 1754488101011, "duration": 17947}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "678c4f3bd7c3cae1c7216386baae6d8b"}], "uid": "62efa7d4bd969e35ac386aa7097579c5"}, {"name": "test_turn_on_wifi", "children": [{"name": "TestEllaTurnWifi", "children": [{"name": "测试turn on wifi能正常执行", "uid": "e06a7c81519a6ea3", "parentUid": "6628c4e31fae719ad94c679a246d743e", "status": "passed", "time": {"start": 1754488114007, "stop": 1754488129817, "duration": 15810}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6628c4e31fae719ad94c679a246d743e"}], "uid": "8a973098444298c6ff004df8ce42b561"}, {"name": "test_wake_me_up_at_am_tomorrow", "children": [{"name": "TestEllaWakeMeUpAmTomorrow", "children": [{"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "a760bb1d944c1018", "parentUid": "a95ef973249db47161b0248e62a09176", "status": "passed", "time": {"start": 1754488142734, "stop": 1754488156049, "duration": 13315}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a95ef973249db47161b0248e62a09176"}], "uid": "19346142b5a8f43c02ff8d5890db9067"}, {"name": "test_where_is_the_carlcare_service_outlet", "children": [{"name": "TestEllaWhereIsCarlcareServiceOutlet", "children": [{"name": "测试where is the carlcare service outlet能正常执行", "uid": "d0d3620174008d52", "parentUid": "ca0e45e31e627eb4f9d8c39d6a6bf138", "status": "failed", "time": {"start": 1754488169099, "stop": 1754488184592, "duration": 15493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ca0e45e31e627eb4f9d8c39d6a6bf138"}], "uid": "7d4ef22a8f804451209374dfe8bf64ad"}], "uid": "4159dc35ce06d1422bb1b7c5665d834a"}, {"name": "testcases.test_ella.third_coupling", "children": [{"name": "test_download_app", "children": [{"name": "TestEllaDownloadApp", "children": [{"name": "测试download app能正常执行", "uid": "13a234a2652ea3ee", "parentUid": "3437a290da1a678bac85f4666839d881", "status": "passed", "time": {"start": 1754488197689, "stop": 1754488213139, "duration": 15450}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3437a290da1a678bac85f4666839d881"}], "uid": "ddb6cf5770aedc759f08823b3e6d8b65"}, {"name": "test_download_basketball", "children": [{"name": "TestEllaDownloadBasketball", "children": [{"name": "测试download basketball能正常执行", "uid": "2dcf43a24b3034da", "parentUid": "588cc0c6328e2e8a712130edc3aed897", "status": "failed", "time": {"start": 1754488226197, "stop": 1754488240470, "duration": 14273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "588cc0c6328e2e8a712130edc3aed897"}], "uid": "1d4ceb2f76f4a7e0f4095f5c1f138613"}, {"name": "test_download_qq", "children": [{"name": "TestEllaDownloadQq", "children": [{"name": "测试download qq能正常执行", "uid": "36600db5fc8f1ba2", "parentUid": "510be9da11cf469597d4e78dac5a719b", "status": "passed", "time": {"start": 1754488253518, "stop": 1754488270639, "duration": 17121}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "510be9da11cf469597d4e78dac5a719b"}], "uid": "e47ca49712e495f1dd67541fd74682ac"}, {"name": "test_find_a_restaurant_near_me", "children": [{"name": "TestEllaFindRestaurantNearMe", "children": [{"name": "测试find a restaurant near me能正常执行", "uid": "bfdac69c756af839", "parentUid": "2a2a6349a33f526f9659f59faee83c46", "status": "passed", "time": {"start": 1754488283667, "stop": 1754488314683, "duration": 31016}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2a2a6349a33f526f9659f59faee83c46"}], "uid": "6ecd9aa1e47da9fc316c168ef75730c9"}, {"name": "test_navigate_from_beijing_to_shanghai", "children": [{"name": "TestEllaNavigateFromBeijingShanghai", "children": [{"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "uid": "d692af8b399c4ed0", "parentUid": "d7122eb175471ed23416d503cb147f88", "status": "passed", "time": {"start": 1754488327511, "stop": 1754488348943, "duration": 21432}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7122eb175471ed23416d503cb147f88"}], "uid": "7b053180829d8e59af204f55867fbbbe"}, {"name": "test_navigate_from_to_red_square", "children": [{"name": "TestEllaNavigateFromRedSquare", "children": [{"name": "测试navigate from to red square能正常执行", "uid": "c06d0b280e1c66e9", "parentUid": "e6c951f1c462cc23dddd03e98f8d3a27", "status": "failed", "time": {"start": 1754488350360, "stop": 1754488350360, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e6c951f1c462cc23dddd03e98f8d3a27"}], "uid": "f52e70464a63502b53d063b021cf198a"}, {"name": "test_navigate_to_shanghai_disneyland", "children": [{"name": "TestEllaNavigateShanghaiDisneyland", "children": [{"name": "测试navigate to shanghai disneyland能正常执行", "uid": "25bd4771bf8a6eb2", "parentUid": "5ae1d6975695489eb2d69ce256cf164f", "status": "failed", "time": {"start": 1754488383986, "stop": 1754488383986, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5ae1d6975695489eb2d69ce256cf164f"}], "uid": "1dbef048a8c2c8a81b66d201495c6825"}, {"name": "test_navigation_to_the_lucky", "children": [{"name": "TestEllaNavigationToTheLucky", "children": [{"name": "测试navigation to the lucky能正常执行", "uid": "cd10f690763a13fb", "parentUid": "719ca28cb458f784122e2efc4135056f", "status": "failed", "time": {"start": 1754488418472, "stop": 1754488418472, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "719ca28cb458f784122e2efc4135056f"}], "uid": "de0db7b40aca346332d2b0972719957a"}, {"name": "test_open_facebook", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open facebook能正常执行", "uid": "c6e041786c62d65a", "parentUid": "7b0b3aea4bca28c8189ca6a4be130237", "status": "failed", "time": {"start": 1754488452897, "stop": 1754488452897, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b0b3aea4bca28c8189ca6a4be130237"}], "uid": "e4224d3534251a2780344938a85b3c06"}, {"name": "test_open_whatsapp", "children": [{"name": "TestEllaOpenWhatsapp", "children": [{"name": "测试open whatsapp", "uid": "57fe78f11b96e66d", "parentUid": "e65b3469b422d0d9909fee2c8bfa0960", "status": "failed", "time": {"start": 1754488486890, "stop": 1754488486890, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e65b3469b422d0d9909fee2c8bfa0960"}], "uid": "5aeebca39d474445d6f5ba2265081895"}, {"name": "test_order_a_burger", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试order a burger能正常执行", "uid": "c219d8630c012ecb", "parentUid": "43a31fc74a106d981123a4cb94e40290", "status": "failed", "time": {"start": 1754488520947, "stop": 1754488520947, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43a31fc74a106d981123a4cb94e40290"}], "uid": "3e2cc02d0935d06e8fb6f72bae4c8c83"}, {"name": "test_order_a_takeaway", "children": [{"name": "TestEllaOrderATakeaway", "children": [{"name": "测试order a takeaway能正常执行", "uid": "2999695e96e957d0", "parentUid": "d9e81a960347bb8bb7bfbcc70b663c4b", "status": "failed", "time": {"start": 1754488554794, "stop": 1754488554794, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9e81a960347bb8bb7bfbcc70b663c4b"}], "uid": "24271799b32debb7d5f05bce65733a10"}, {"name": "test_pls_open_the_newest_whatsapp_activity", "children": [{"name": "TestEllaOpenPlsNewestWhatsappActivity", "children": [{"name": "测试pls open the newest whatsapp activity", "uid": "7a6c918293c863b9", "parentUid": "5022c6b03e034f068f4ddbc7533f4c29", "status": "failed", "time": {"start": 1754488588832, "stop": 1754488588832, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5022c6b03e034f068f4ddbc7533f4c29"}], "uid": "9b7c89d46b9b721d197765453f1675de"}, {"name": "test_whatsapp", "children": [{"name": "TestEllaW<PERSON>sapp", "children": [{"name": "测试whatsapp能正常执行", "uid": "c2f84ab6863401ce", "parentUid": "edf22212c64c387b5f0b05436a17000b", "status": "failed", "time": {"start": 1754488623011, "stop": 1754488623011, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "edf22212c64c387b5f0b05436a17000b"}], "uid": "0438f804905f59a497b4803048f51319"}], "uid": "2660f6320a566ad526d6ea679fb2528f"}, {"name": "testcases.test_ella.unsupported_commands", "children": [{"name": "test_Add_the_images_and_text_on_the_screen_to_the_note", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Add the images and text on the screen to the note", "uid": "38a048b45445ca67", "parentUid": "433b944b4500addcf62906018e50a6af", "status": "failed", "time": {"start": 1754488657242, "stop": 1754488657242, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "433b944b4500addcf62906018e50a6af"}], "uid": "3d0102c3a98807edcea83109224019cc"}, {"name": "test_change_female_tone_name_voice", "children": [{"name": "TestEllaChangeFemaleToneNameVoice", "children": [{"name": "测试change (female/tone name) voice能正常执行", "uid": "1199297291d0d17", "parentUid": "b565d06b86fa223901766faddeda207a", "status": "failed", "time": {"start": 1754488691332, "stop": 1754488691333, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b565d06b86fa223901766faddeda207a"}], "uid": "66562456c81152cc1fa9d09032ae884d"}, {"name": "test_change_man_voice", "children": [{"name": "TestEllaChangeManVoice", "children": [{"name": "测试change man voice能正常执行", "uid": "cbd155ed4bc579f", "parentUid": "9211e5358ec84e06d14d678e7d922690", "status": "failed", "time": {"start": 1754488725683, "stop": 1754488725683, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9211e5358ec84e06d14d678e7d922690"}], "uid": "2d491290e2846a176599f1eae33a3ad3"}, {"name": "test_change_your_voice", "children": [{"name": "TestEllaChangeYourVoice", "children": [{"name": "测试change your voice能正常执行", "uid": "3a8cee725f31ec40", "parentUid": "0d45916b91412c59944b54b906868410", "status": "failed", "time": {"start": 1754488759791, "stop": 1754488759791, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d45916b91412c59944b54b906868410"}], "uid": "99d1138eba2f3e2cb20c8824270684bb"}, {"name": "test_check_battery_information", "children": [{"name": "TestEllaCheckBatteryInformation", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "6901c3d0d10496fa", "parentUid": "d28e0e4ff0e4838a6027dc47e3b3a670", "status": "failed", "time": {"start": 1754488793969, "stop": 1754488793969, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d28e0e4ff0e4838a6027dc47e3b3a670"}], "uid": "4c906a8c1578240415aebe31f410f234"}, {"name": "test_check_contact", "children": [{"name": "TestEllaCheckContact", "children": [{"name": "测试check contact能正常执行", "uid": "e27a3e330ed5d0d0", "parentUid": "7840d5d29bd3ef68fd5883a60ba4d483", "status": "failed", "time": {"start": 1754488827949, "stop": 1754488827949, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7840d5d29bd3ef68fd5883a60ba4d483"}], "uid": "26722106422710fc718c70034be7a8c2"}, {"name": "test_check_contacts", "children": [{"name": "TestEllaCheckContacts", "children": [{"name": "测试check contacts能正常执行", "uid": "443dca154c3756ff", "parentUid": "7b0c7a58c89c6e54945f60f9f70c7cd2", "status": "failed", "time": {"start": 1754488862083, "stop": 1754488862083, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b0c7a58c89c6e54945f60f9f70c7cd2"}], "uid": "52118859006a30586241bd2a4a26847c"}, {"name": "test_check_mobile_data_balance_of_sim", "children": [{"name": "TestEllaCheckMobileDataBalanceSim", "children": [{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "24595911ea65e948", "parentUid": "2e50c8269684c4a73bb5b020614c2221", "status": "failed", "time": {"start": 1754488896070, "stop": 1754488896070, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2e50c8269684c4a73bb5b020614c2221"}], "uid": "ff56d23044bd5d499190e5ca173df4d3"}, {"name": "test_check_model_information", "children": [{"name": "TestEllaCheckModelInformation", "children": [{"name": "测试check model information返回正确的不支持响应", "uid": "e0ffa8c14943a28b", "parentUid": "f7e3ca8a9343a9ed054471fe3e0356b4", "status": "failed", "time": {"start": 1754488930105, "stop": 1754488930105, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f7e3ca8a9343a9ed054471fe3e0356b4"}], "uid": "bf2cc0fa355876e3be500d0c5ba7a5f1"}, {"name": "test_check_my_balance_of_sim", "children": [{"name": "TestEllaCheckMyBalanceSim", "children": [{"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "c3973bb228d29704", "parentUid": "f9995b28dafd2b1ed8762fedcb92c132", "status": "failed", "time": {"start": 1754488964292, "stop": 1754488964292, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f9995b28dafd2b1ed8762fedcb92c132"}], "uid": "ffd79a31b3c86841ac0cc6d5fa40f471"}, {"name": "test_check_my_to_do_list", "children": [{"name": "TestEllaCheckMyDoList", "children": [{"name": "测试check my to-do list能正常执行", "uid": "f6680ac844d1bbf7", "parentUid": "43310d5383f9f3391eb3ba14577fd4d7", "status": "failed", "time": {"start": 1754488998385, "stop": 1754488998385, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43310d5383f9f3391eb3ba14577fd4d7"}], "uid": "20383a2eb3e29a6c6ccd53f0bce07fe1"}, {"name": "test_check_rear_camera_information", "children": [{"name": "TestEllaCheckRearCameraInformation", "children": [{"name": "测试check rear camera information能正常执行", "uid": "493d4b92d0c5f035", "parentUid": "89f05870118719c157701595fa592ca6", "status": "failed", "time": {"start": 1754489032587, "stop": 1754489032587, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "89f05870118719c157701595fa592ca6"}], "uid": "3e2d1f7b0da0ab237b35604822208008"}, {"name": "test_close_equilibrium_mode", "children": [{"name": "TestEllaCloseEquilibriumMode", "children": [{"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "bdff75fea7ed5983", "parentUid": "2ca6423e5b9b0595a667d74916b87ae8", "status": "failed", "time": {"start": 1754489066550, "stop": 1754489066550, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2ca6423e5b9b0595a667d74916b87ae8"}], "uid": "cf0d3a245849cc3d2fb49b5919a2ca70"}, {"name": "test_close_performance_mode", "children": [{"name": "TestEllaClosePerformanceMode", "children": [{"name": "测试close performance mode返回正确的不支持响应", "uid": "b11d5a82657241fd", "parentUid": "bfc98a3e31cf71b24d2c2664874d8f35", "status": "failed", "time": {"start": 1754489100666, "stop": 1754489100666, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfc98a3e31cf71b24d2c2664874d8f35"}], "uid": "921c03c2af17c0dd8bb300d05ebc1435"}, {"name": "test_close_power_saving_mode", "children": [{"name": "TestEllaClosePowerSavingMode", "children": [{"name": "测试close power saving mode返回正确的不支持响应", "uid": "bbeba40801831c5", "parentUid": "d7c06ff8b4a17dd9ae62ae957d96f702", "status": "failed", "time": {"start": 1754489134761, "stop": 1754489134761, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7c06ff8b4a17dd9ae62ae957d96f702"}], "uid": "de4693d49200ef3897dfa7e2a5aa8dbc"}, {"name": "test_disable_accelerate_dialogue", "children": [{"name": "TestEllaDisableAccelerateDialogue", "children": [{"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "d9281fe9adae9ba5", "parentUid": "a12fb87a3ef6191f6c5649ce65173837", "status": "failed", "time": {"start": 1754489168952, "stop": 1754489168952, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a12fb87a3ef6191f6c5649ce65173837"}], "uid": "afbbdecfafcd893c4f7104365ff8935e"}, {"name": "test_disable_all_ai_magic_box_features", "children": [{"name": "TestEllaDisableAllAiMagicBoxFeatures", "children": [{"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "99fd73c7fe1b75de", "parentUid": "696197cde92ea407d7be5d0ee1a43b1a", "status": "failed", "time": {"start": 1754489202925, "stop": 1754489202925, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "696197cde92ea407d7be5d0ee1a43b1a"}], "uid": "2b54914538eb635e492ddf923151d357"}, {"name": "test_disable_auto_pickup", "children": [{"name": "TestEllaDisableAutoPickup", "children": [{"name": "测试disable auto pickup返回正确的不支持响应", "uid": "7f1ee703c6a94109", "parentUid": "c20ac19fa762a9db14606dd531e82b8f", "status": "failed", "time": {"start": 1754489236847, "stop": 1754489236847, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c20ac19fa762a9db14606dd531e82b8f"}], "uid": "bf014ddab53633d98f8104639ce1bc1b"}, {"name": "test_disable_brightness_locking", "children": [{"name": "TestEllaDisableBrightnessLocking", "children": [{"name": "测试disable brightness locking返回正确的不支持响应", "uid": "1d9622fd407c2c96", "parentUid": "858fef1ffc1503318aa70e9c72d7b438", "status": "failed", "time": {"start": 1754489271036, "stop": 1754489271036, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "858fef1ffc1503318aa70e9c72d7b438"}], "uid": "66c800c4edcde7ca93c0c123e93d1914"}, {"name": "test_disable_call_rejection", "children": [{"name": "TestEllaDisableCallRejection", "children": [{"name": "测试disable call rejection返回正确的不支持响应", "uid": "61e0a20218e2b282", "parentUid": "3f640e07903d6f89a09553a61d6690c3", "status": "failed", "time": {"start": 1754489304958, "stop": 1754489304958, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f640e07903d6f89a09553a61d6690c3"}], "uid": "021f51382efc25258ec8ca7ef3573495"}, {"name": "test_disable_hide_notifications", "children": [{"name": "TestEllaDisableHideNotifications", "children": [{"name": "测试disable hide notifications返回正确的不支持响应", "uid": "649ac03314c798ad", "parentUid": "9f132b3849693b61c2ca479bcafef282", "status": "failed", "time": {"start": 1754489339070, "stop": 1754489339070, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9f132b3849693b61c2ca479bcafef282"}], "uid": "7030498aad7e0e3d901754313db42a5c"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "TestEllaDisableMagicVoiceChanger", "children": [{"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "3d61731e0bcd9778", "parentUid": "04c072a77ad52d90193c2ec6bc53a021", "status": "failed", "time": {"start": 1754489373111, "stop": 1754489373111, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "04c072a77ad52d90193c2ec6bc53a021"}], "uid": "6f0070b209221eadb3d6425a33ecbff1"}, {"name": "test_disable_network_enhancement", "children": [{"name": "TestEllaDisableNetworkEnhancement", "children": [{"name": "测试disable network enhancement返回正确的不支持响应", "uid": "21e4a005e4281783", "parentUid": "fe480ad7296777a27bc8f2d53aa0fccf", "status": "failed", "time": {"start": 1754489407275, "stop": 1754489407275, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fe480ad7296777a27bc8f2d53aa0fccf"}], "uid": "afc5376e9464fb8921d2fbcf9e0d3bbc"}, {"name": "test_disable_running_lock", "children": [{"name": "TestEllaDisableRunningLock", "children": [{"name": "测试disable running lock返回正确的不支持响应", "uid": "88005cc383fdea8f", "parentUid": "8fe90804f66731578ca4d4ba9283a64c", "status": "failed", "time": {"start": 1754489441912, "stop": 1754489441912, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8fe90804f66731578ca4d4ba9283a64c"}], "uid": "ad391be6683f94da1f0d53eaeb12f8a3"}, {"name": "test_disable_touch_optimization", "children": [{"name": "TestEllaDisableTouchOptimization", "children": [{"name": "测试disable touch optimization返回正确的不支持响应", "uid": "9f960e8ed027c827", "parentUid": "a61e1b3af122a2fe511826a7e99c2e1b", "status": "failed", "time": {"start": 1754489476371, "stop": 1754489476371, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a61e1b3af122a2fe511826a7e99c2e1b"}], "uid": "8d875ca58978089e7102b409d8215741"}, {"name": "test_disable_unfreeze", "children": [{"name": "TestEllaDisableUnfreeze", "children": [{"name": "测试disable unfreeze返回正确的不支持响应", "uid": "b0fa49b954710d82", "parentUid": "1e21c84af5bdef90eb5dee6bd5cf2635", "status": "failed", "time": {"start": 1754489510717, "stop": 1754489510717, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1e21c84af5bdef90eb5dee6bd5cf2635"}], "uid": "b1772b071366ab2c77148d9716c8dcd9"}, {"name": "test_disable_zonetouch_master", "children": [{"name": "TestEllaDisableZonetouchMaster", "children": [{"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "cc78c9c9b8318173", "parentUid": "21d3787e184cbd6bdfc49c456af0ad04", "status": "failed", "time": {"start": 1754489545030, "stop": 1754489545030, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "21d3787e184cbd6bdfc49c456af0ad04"}], "uid": "f5c774dd2222acf0e4aa8f7b201f12b3"}, {"name": "test_download_basketball", "children": [{"name": "TestEllaDownloadBasketball", "children": [{"name": "测试download basketball返回正确的不支持响应", "uid": "4634ae81442a4912", "parentUid": "98ae41f5cdd53a2d20bf8172efd26083", "status": "failed", "time": {"start": 1754489578969, "stop": 1754489578969, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98ae41f5cdd53a2d20bf8172efd26083"}], "uid": "314bfe9c667cd809cb0cc507bfcd14fc"}, {"name": "test_driving_mode", "children": [{"name": "TestEllaDrivingMode", "children": [{"name": "测试driving mode返回正确的不支持响应", "uid": "4d07f0c6c7ca376f", "parentUid": "ef0bd4110e3386c3ad0f74515d6f200e", "status": "failed", "time": {"start": 1754489613176, "stop": 1754489613176, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ef0bd4110e3386c3ad0f74515d6f200e"}], "uid": "514aae0515740321638cc56a86d0bdc5"}, {"name": "test_enable_accelerate_dialogue", "children": [{"name": "TestEllaEnableAccelerateDialogue", "children": [{"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "124a1f3e8edc5337", "parentUid": "eb6a89c6cd9a711ed9ac3e3c4a9f82e7", "status": "failed", "time": {"start": 1754489647368, "stop": 1754489647368, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eb6a89c6cd9a711ed9ac3e3c4a9f82e7"}], "uid": "8c56ee95835e673fa28b34154afb4628"}, {"name": "test_enable_all_ai_magic_box_features", "children": [{"name": "TestEllaEnableAllAiMagicBoxFeatures", "children": [{"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "18f399f8e5fa9ce7", "parentUid": "8a587c12d6caed0b409b8971d49025ab", "status": "failed", "time": {"start": 1754489681401, "stop": 1754489681401, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8a587c12d6caed0b409b8971d49025ab"}], "uid": "68d379dd7f74dd5275f913b7eee03079"}, {"name": "test_enable_auto_pickup", "children": [{"name": "TestEllaEnableAutoPickup", "children": [{"name": "测试enable auto pickup返回正确的不支持响应", "uid": "b7f4b9e941495e2d", "parentUid": "97d17abb9dc1ccc8f46a34e06a32340c", "status": "failed", "time": {"start": 1754489715755, "stop": 1754489715755, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "97d17abb9dc1ccc8f46a34e06a32340c"}], "uid": "8c4650098d6069190cfba3c757ba45db"}, {"name": "test_enable_brightness_locking", "children": [{"name": "TestEllaEnableBrightnessLocking", "children": [{"name": "测试enable brightness locking返回正确的不支持响应", "uid": "f44ba420ed70cb7f", "parentUid": "c400a86ee772ae2bb87a4d299fcffde3", "status": "failed", "time": {"start": 1754489750061, "stop": 1754489750061, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c400a86ee772ae2bb87a4d299fcffde3"}], "uid": "0e14ad27fc644f4f62c72d254e5d2d00"}, {"name": "test_enable_call_on_hold", "children": [{"name": "TestEllaEnableCallHold", "children": [{"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "c7e38f605f9066ee", "parentUid": "6d79c2b8de7e0682e35d7e5c727c1ff8", "status": "failed", "time": {"start": 1754489783936, "stop": 1754489783936, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d79c2b8de7e0682e35d7e5c727c1ff8"}], "uid": "de334526c374af0073eb9eb6ddb0997a"}, {"name": "test_enable_call_rejection", "children": [{"name": "TestEllaEnableCallRejection", "children": [{"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "f56294cf89c411bc", "parentUid": "e26b43428a954a94928f7a25e76abac9", "status": "failed", "time": {"start": 1754489817654, "stop": 1754489817654, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e26b43428a954a94928f7a25e76abac9"}], "uid": "366922e4cbf42ddefd029f732cdd77bf"}, {"name": "test_enable_network_enhancement", "children": [{"name": "TestEllaEnableNetworkEnhancement", "children": [{"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "bf2f4d9af9c68c1c", "parentUid": "c12a817d493d41c56a647cb659a7bb8c", "status": "failed", "time": {"start": 1754489851594, "stop": 1754489851594, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c12a817d493d41c56a647cb659a7bb8c"}], "uid": "040b928633962c521baae26a6f7ac7c5"}, {"name": "test_enable_running_lock", "children": [{"name": "TestEllaEnableRunningLock", "children": [{"name": "测试enable running lock返回正确的不支持响应", "uid": "65dc203fdb610f69", "parentUid": "e1c78a96555072edad6f93b7ff33937c", "status": "failed", "time": {"start": 1754489885466, "stop": 1754489885466, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e1c78a96555072edad6f93b7ff33937c"}], "uid": "26ee37ce6707d6284177cfa0f1cae12c"}, {"name": "test_enable_touch_optimization", "children": [{"name": "TestEllaEnableTouchOptimization", "children": [{"name": "测试enable touch optimization返回正确的不支持响应", "uid": "cd66040bc232be03", "parentUid": "d84bfce90dcde3b576c695692fca0fa8", "status": "failed", "time": {"start": 1754489919354, "stop": 1754489919354, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d84bfce90dcde3b576c695692fca0fa8"}], "uid": "76d94ee4c89c6674e70da8c74116bad6"}, {"name": "test_enable_unfreeze", "children": [{"name": "TestEllaEnableUnfreeze", "children": [{"name": "测试enable unfreeze返回正确的不支持响应", "uid": "3ad176dfb601300e", "parentUid": "c801d69cec54af644c884ba704111151", "status": "failed", "time": {"start": 1754489953337, "stop": 1754489953337, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c801d69cec54af644c884ba704111151"}], "uid": "63e313e7547561b2fa987da4d6072437"}, {"name": "test_enable_zonetouch_master", "children": [{"name": "TestEllaEnableZonetouchMaster", "children": [{"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "e1bbda90aff93d55", "parentUid": "99a67c39634063a502862411f9efdb5c", "status": "failed", "time": {"start": 1754489987036, "stop": 1754489987036, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "99a67c39634063a502862411f9efdb5c"}], "uid": "796eef53680203e2ca4133277e86230b"}, {"name": "test_extend_the_image", "children": [{"name": "TestEllaExtendImage", "children": [{"name": "测试extend the image能正常执行", "uid": "9bb5d66bd0ffe1f8", "parentUid": "58d8f715c93e890833f08e611da6c0f8", "status": "failed", "time": {"start": 1754490021266, "stop": 1754490021266, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58d8f715c93e890833f08e611da6c0f8"}], "uid": "600af0308ad3a3f35324296fc9a30562"}, {"name": "test_go_home", "children": [{"name": "TestEllaGoHome", "children": [{"name": "测试go home能正常执行", "uid": "29513ea621899e56", "parentUid": "0be9216ae99d7aa91cd2e638009124bc", "status": "failed", "time": {"start": 1754490055356, "stop": 1754490055356, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0be9216ae99d7aa91cd2e638009124bc"}], "uid": "16160dbe85fde9f730f84128fbb2c0e8"}, {"name": "test_happy_new_year", "children": [{"name": "TestEllaHappyNewYear", "children": [{"name": "测试happy new year能正常执行", "uid": "64543549befa991e", "parentUid": "451251d367048f9c53b98847c2e447f2", "status": "failed", "time": {"start": 1754490089116, "stop": 1754490089116, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "451251d367048f9c53b98847c2e447f2"}], "uid": "20bc3f1296c161aa14a0b88cee103de3"}, {"name": "test_help_me_write_an_email", "children": [{"name": "TestEllaHelpMeWriteAnEmail", "children": [{"name": "测试help me write an email能正常执行", "uid": "eeead481a4592f65", "parentUid": "649615446ede0148c5ff8678d523d0aa", "status": "failed", "time": {"start": 1754490123137, "stop": 1754490123137, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "649615446ede0148c5ff8678d523d0aa"}], "uid": "4c50426c37e2427cf10466a61f135134"}, {"name": "test_help_me_write_an_thanks_email", "children": [{"name": "TestEllaHelpMeWriteAnThanksEmail", "children": [{"name": "测试help me write an thanks email能正常执行", "uid": "422547e10a7988ea", "parentUid": "532261b0e09523c5a2a4e366e160f5fd", "status": "failed", "time": {"start": 1754490157148, "stop": 1754490157148, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "532261b0e09523c5a2a4e366e160f5fd"}], "uid": "e38c35bd53b03a201c55a53b3d55b4b0"}, {"name": "test_help_me_write_an_thanks_letter", "children": [{"name": "TestEllaHelpMeWriteAnThanksLetter", "children": [{"name": "测试help me write an thanks letter能正常执行", "uid": "c1f0bcc3af9f38f6", "parentUid": "3755340db0d802a9e3906271e22e82ca", "status": "failed", "time": {"start": 1754490191042, "stop": 1754490191042, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3755340db0d802a9e3906271e22e82ca"}], "uid": "407f54fb38fda3eea5b61bfdc186311a"}, {"name": "test_how_to_set_screenshots", "children": [{"name": "TestEllaHowSetScreenshots", "children": [{"name": "测试how to set screenshots返回正确的不支持响应", "uid": "6bb6c7260139caa2", "parentUid": "4862cd77fa6584ab1cd401dd18e576cf", "status": "failed", "time": {"start": 1754490225039, "stop": 1754490225039, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4862cd77fa6584ab1cd401dd18e576cf"}], "uid": "3af4e120133a0419124a1c704c7d3199"}, {"name": "test_increase_settings_for_special_functions", "children": [{"name": "TestEllaIncreaseSettingsSpecialFunctions", "children": [{"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "872f2d0df76fd076", "parentUid": "47b335b99ed8a1ee8475ca4a6834e17a", "status": "failed", "time": {"start": 1754490259190, "stop": 1754490259190, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47b335b99ed8a1ee8475ca4a6834e17a"}], "uid": "c3601014aaa999aff69c5872e32743b7"}, {"name": "test_jump_to_adaptive_brightness_settings", "children": [{"name": "TestEllaJumpAdaptiveBrightnessSettings", "children": [{"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "20fe2dd2bfdd7b37", "parentUid": "f92d66f2cfb1510e3614f32014b90cdf", "status": "failed", "time": {"start": 1754490292806, "stop": 1754490292806, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f92d66f2cfb1510e3614f32014b90cdf"}], "uid": "eda1255cba29f83ff1461094841f0f40"}, {"name": "test_jump_to_ai_wallpaper_generator_settings", "children": [{"name": "TestEllaJumpAiWallpaperGeneratorSettings", "children": [{"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "35d648522707fb53", "parentUid": "508a56999eea40b3b6eae200090d9ef7", "status": "failed", "time": {"start": 1754490326450, "stop": 1754490326450, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "508a56999eea40b3b6eae200090d9ef7"}], "uid": "952ea4e1b05a29ce2f17032769862fd2"}, {"name": "test_jump_to_auto_rotate_screen_settings", "children": [{"name": "TestEllaJumpAutoRotateScreenSettings", "children": [{"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "6c0551c8476663fd", "parentUid": "923f4dfcc1ff430310299406df4a76ad", "status": "failed", "time": {"start": 1754490350897, "stop": 1754490350897, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "923f4dfcc1ff430310299406df4a76ad"}], "uid": "2885405d4e301681acf1f9073b75196b"}, {"name": "test_jump_to_battery_and_power_saving", "children": [{"name": "TestEllaJumpBatteryPowerSaving", "children": [{"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "1736a9777edb77af", "parentUid": "7b0af410a8bc7729423b66dbd86394a1", "status": "failed", "time": {"start": 1754490376488, "stop": 1754490376488, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b0af410a8bc7729423b66dbd86394a1"}], "uid": "5e4b9eee9fae8ca49a205b0e08671105"}, {"name": "test_jump_to_battery_usage", "children": [{"name": "TestEllaJumpBatteryUsage", "children": [{"name": "测试jump to battery usage返回正确的不支持响应", "uid": "ee7c08861f1d4369", "parentUid": "e977b1d095b1f986aff6226776ded3c3", "status": "failed", "time": {"start": 1754490402469, "stop": 1754490402469, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e977b1d095b1f986aff6226776ded3c3"}], "uid": "379854b415d26ca0952c7565049546e2"}, {"name": "test_jump_to_call_notifications", "children": [{"name": "TestEllaJumpCallNotifications", "children": [{"name": "测试jump to call notifications返回正确的不支持响应", "uid": "53d43e24245acf0a", "parentUid": "e1d634c784c8a3eaf84c787e520a993c", "status": "failed", "time": {"start": 1754490428166, "stop": 1754490428166, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e1d634c784c8a3eaf84c787e520a993c"}], "uid": "0d6d5e9fa27b112c5e4a5c892fa26625"}, {"name": "test_jump_to_high_brightness_mode_settings", "children": [{"name": "TestEllaJumpHighBrightnessModeSettings", "children": [{"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "5f1d181210935ac4", "parentUid": "968c23cffad77650f73adb57ed35e44e", "status": "failed", "time": {"start": 1754490474108, "stop": 1754490474108, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "968c23cffad77650f73adb57ed35e44e"}], "uid": "f96efa82b36a271078aa933d212ff1e1"}, {"name": "test_jump_to_lock_screen_notification_and_display_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "5f1cafc0f7162a3b", "parentUid": "6e6a65b648407dd3ea18fb96b915dace", "status": "failed", "time": {"start": 1754490512810, "stop": 1754490512810, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e6a65b648407dd3ea18fb96b915dace"}], "uid": "a57e12d4c6edf0c5f1e60f981492cd0f"}, {"name": "test_jump_to_nfc_settings", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试jump to nfc settings", "uid": "29843c65bf2d03c6", "parentUid": "400a6941e3389b1b48d77dfa263077fe", "status": "failed", "time": {"start": 1754490548481, "stop": 1754490548481, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "400a6941e3389b1b48d77dfa263077fe"}], "uid": "364aacfa906bc91f673d917736ae37e5"}, {"name": "test_jump_to_notifications_and_status_bar_settings", "children": [{"name": "TestEllaJumpNotificationsStatusBarSettings", "children": [{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "c21cde7c398161d9", "parentUid": "5891939ff40959b2d8e13bfa76678a88", "status": "failed", "time": {"start": 1754490583181, "stop": 1754490583181, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5891939ff40959b2d8e13bfa76678a88"}], "uid": "843f8e4d40a46af764e0186551ea2eab"}, {"name": "test_kill_whatsapp", "children": [{"name": "TestEllaKillWhatsapp", "children": [{"name": "测试kill whatsapp能正常执行", "uid": "65c217eaa39ab993", "parentUid": "1c8d4c3e4504945ca2c7aa8aa790b4ab", "status": "failed", "time": {"start": 1754490617576, "stop": 1754490617576, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1c8d4c3e4504945ca2c7aa8aa790b4ab"}], "uid": "f5b7dfc982ee4029db9ba755d4b0c849"}, {"name": "test_modify_grape_timbre", "children": [{"name": "TestEllaEnableRunningLock", "children": [{"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "3ca37dd9f27c6ecc", "parentUid": "79b5d194845308e1383e35ab63ba394b", "status": "failed", "time": {"start": 1754490651448, "stop": 1754490651448, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79b5d194845308e1383e35ab63ba394b"}], "uid": "ff7fe79e49919f1d7ccc98346b480d21"}, {"name": "test_more_settings", "children": [{"name": "TestEllaMoreSettings", "children": [{"name": "测试more settings返回正确的不支持响应", "uid": "97abb069c96ff50c", "parentUid": "5158adca762a1833ee225b49be689b4b", "status": "failed", "time": {"start": 1754490685463, "stop": 1754490685463, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5158adca762a1833ee225b49be689b4b"}], "uid": "2678afdbcf3cd1c2f7304aab68dff13e"}, {"name": "test_navigation_to_the_address_in_the_image", "children": [{"name": "TestEllaNavigationAddressTheImage", "children": [{"name": "测试navigation to the address in thie image能正常执行", "uid": "26798eff8ed9e72", "parentUid": "bdbc8f989cddecd7a3ff8b9229b6e0b7", "status": "failed", "time": {"start": 1754490719855, "stop": 1754490719855, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bdbc8f989cddecd7a3ff8b9229b6e0b7"}], "uid": "2ea8810e61f48227b2b12d8788c78cfa"}, {"name": "test_navigation_to_the_first_address_in_the_image", "children": [{"name": "TestEllaNavigationFirstAddressImage", "children": [{"name": "测试navigation to the first address in the image能正常执行", "uid": "18f772aa522a4c5", "parentUid": "fc6a08afad859df9f5da8d1611e05a0e", "status": "failed", "time": {"start": 1754490754497, "stop": 1754490754497, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fc6a08afad859df9f5da8d1611e05a0e"}], "uid": "f052d105eebf03aacc6d59b029ca356c"}, {"name": "test_open_font_family_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试open font family settings返回正确的不支持响应", "uid": "3eed261f9745ce86", "parentUid": "3abee97ea9125b112c9768709b230d6c", "status": "failed", "time": {"start": 1754490788673, "stop": 1754490788673, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3abee97ea9125b112c9768709b230d6c"}], "uid": "c37be023aa1668dd5137d98115ed22ee"}, {"name": "test_open_notification_ringtone_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "65e88368633e1d8a", "parentUid": "1547bf4ebc0f2064b90de7ee9d4b0d9a", "status": "failed", "time": {"start": 1754490822706, "stop": 1754490822706, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1547bf4ebc0f2064b90de7ee9d4b0d9a"}], "uid": "e9bb1812204e529485c6767282c66ee4"}, {"name": "test_open_whatsapp", "children": [{"name": "TestEllaOpenWhatsapp", "children": [{"name": "测试open whatsapp", "uid": "720a5005d504c9d4", "parentUid": "a86f67524fc901b9e239b41a55bcd168", "status": "failed", "time": {"start": 1754490856630, "stop": 1754490856630, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a86f67524fc901b9e239b41a55bcd168"}], "uid": "134b51cd09c0f3f3f5664bfe08db4bfb"}, {"name": "test_order_a_burger", "children": [{"name": "TestEllaOrderBurger", "children": [{"name": "测试order a burger返回正确的不支持响应", "uid": "5a9de1902905bdb5", "parentUid": "caf4b95867e28da5919f2eca0a324167", "status": "failed", "time": {"start": 1754490890686, "stop": 1754490890686, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "caf4b95867e28da5919f2eca0a324167"}], "uid": "5866702b17345a70f73c15e82c35a889"}, {"name": "test_order_a_takeaway", "children": [{"name": "TestEllaOrderTakeaway", "children": [{"name": "测试order a takeaway返回正确的不支持响应", "uid": "13d28d73850399d4", "parentUid": "bee4551900f0d6522500f0c802a47f62", "status": "failed", "time": {"start": 1754490924820, "stop": 1754490924820, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bee4551900f0d6522500f0c802a47f62"}], "uid": "d94ef1da6fdbed2a2a316ba39ec7f816"}, {"name": "test_parking_space", "children": [{"name": "TestEllaParkingSpace", "children": [{"name": "测试parking space能正常执行", "uid": "c701032e4d34f79", "parentUid": "8002e65cdc68fa89adcbace4cab353af", "status": "failed", "time": {"start": 1754490958969, "stop": 1754490958969, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8002e65cdc68fa89adcbace4cab353af"}], "uid": "91df28ab9c563eb71b7c27ebe03bd2a9"}, {"name": "test_play_football_video_by_youtube", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play football video by youtube", "uid": "9d20fb01702a451", "parentUid": "f50015233729c567f6411101e9d7507e", "status": "failed", "time": {"start": 1754490993122, "stop": 1754490993122, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f50015233729c567f6411101e9d7507e"}], "uid": "288230e0b3a90da164068733d1ce2f50"}, {"name": "test_play_love_sotry", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play love sotry", "uid": "960f945a0e52aa89", "parentUid": "2eafce82b79f0069cbd0ea652014bbfb", "status": "failed", "time": {"start": 1754491027353, "stop": 1754491027353, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2eafce82b79f0069cbd0ea652014bbfb"}], "uid": "395956abc96532bb04cf74abc316a3ba"}, {"name": "test_play_taylor_swift_s_song_love_sotry", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play taylor swift‘s song love story", "uid": "27fab215254917dc", "parentUid": "369a78d30a3bb706690dc0f41716f29f", "status": "failed", "time": {"start": 1754491061081, "stop": 1754491061081, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "369a78d30a3bb706690dc0f41716f29f"}], "uid": "fee77f8c40bb5ae6f21bab13e0798786"}, {"name": "test_play_the_album", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play the album", "uid": "84efe235da887661", "parentUid": "1cfc5343c5c88745c65dc82ee560dd78", "status": "failed", "time": {"start": 1754491094993, "stop": 1754491094993, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1cfc5343c5c88745c65dc82ee560dd78"}], "uid": "58b703424f3437f68fd423d41eccfd46"}, {"name": "test_play_video", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play video", "uid": "95b3160676d4ff72", "parentUid": "6e35a63e6264545436bbb6f84204721d", "status": "failed", "time": {"start": 1754491128564, "stop": 1754491128564, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e35a63e6264545436bbb6f84204721d"}], "uid": "0a6d47b585a70d59df4457bfeb32ef4b"}, {"name": "test_play_video_by_youtube", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play video by youtube", "uid": "5c4e4711bd0d8a", "parentUid": "87ac2f2511f41462750c26dcbc17f7d7", "status": "failed", "time": {"start": 1754491162717, "stop": 1754491162717, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "87ac2f2511f41462750c26dcbc17f7d7"}], "uid": "fe310a2b7e3c25a7a2ac071656c5ef12"}, {"name": "test_please_show_me_where_i_am", "children": [{"name": "TestEllaPleaseShowMeWhereIAm", "children": [{"name": "测试please show me where i am能正常执行", "uid": "971d76c9403829e0", "parentUid": "9fd87d8d098fefc38dbf9094b5c04473", "status": "failed", "time": {"start": 1754491196950, "stop": 1754491196950, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9fd87d8d098fefc38dbf9094b5c04473"}], "uid": "476b6def0b2277a73bf0dfbcd5b6047b"}, {"name": "test_pls_open_whatsapp", "children": [{"name": "TestEllaOpenWhatsapp", "children": [{"name": "测试pls open whatsapp", "uid": "c2c64f3d874f56de", "parentUid": "f606de62a17219d1049f95130d391334", "status": "failed", "time": {"start": 1754491230942, "stop": 1754491230942, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f606de62a17219d1049f95130d391334"}], "uid": "71d89bf7be06a8672fe794f8b289d664"}, {"name": "test_redial", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试redial", "uid": "936da91c6ba88125", "parentUid": "73134362df5e82df9dbacec436fd51cc", "status": "failed", "time": {"start": 1754491264848, "stop": 1754491264848, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "73134362df5e82df9dbacec436fd51cc"}], "uid": "ad6624cb14424d8194382f0f58d7848c"}, {"name": "test_reset_phone", "children": [{"name": "TestEllaResetPhone", "children": [{"name": "测试reset phone返回正确的不支持响应", "uid": "6c2113f7cf07a5f1", "parentUid": "cac15668a449dfa5284ac8751a8bc088", "status": "failed", "time": {"start": 1754491298781, "stop": 1754491298781, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cac15668a449dfa5284ac8751a8bc088"}], "uid": "20f77fb14c171a00344aefe112819e8f"}, {"name": "test_restart_my_phone", "children": [{"name": "TestEllaRestartMyPhone", "children": [{"name": "测试restart my phone能正常执行", "uid": "3ffde2e2205e6490", "parentUid": "8e6b8a6f5b7ace7e38fe72173c083641", "status": "failed", "time": {"start": 1754491332571, "stop": 1754491332571, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8e6b8a6f5b7ace7e38fe72173c083641"}], "uid": "37ab7708b333614b029b8e7dbb1544ed"}, {"name": "test_restart_the_phone", "children": [{"name": "TestEllaRestartPhone", "children": [{"name": "测试restart the phone能正常执行", "uid": "a25189f4c41cea26", "parentUid": "3774bd71fb27e3ff5098f87b6107b67c", "status": "failed", "time": {"start": 1754491366575, "stop": 1754491366575, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3774bd71fb27e3ff5098f87b6107b67c"}], "uid": "8520ec26c958caa18d4558c5d16d58cd"}, {"name": "test_search_for_addresses_on_the_screen", "children": [{"name": "TestEllaSearchAddressesScreen", "children": [{"name": "测试Search for addresses on the screen能正常执行", "uid": "1ee5d8b15798efff", "parentUid": "4c52013229be65cab9dbe8706e15af5a", "status": "failed", "time": {"start": 1754491400647, "stop": 1754491400647, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c52013229be65cab9dbe8706e15af5a"}], "uid": "c2898d22ab26a5d42aa6bad2edebff1b"}, {"name": "test_search_the_address_in_the_image", "children": [{"name": "TestEllaSearchAddressImage", "children": [{"name": "测试search the address in the image能正常执行", "uid": "62c368eb30849dec", "parentUid": "c189e03b4adbf1ffd08ccde0c003cbf1", "status": "failed", "time": {"start": 1754491434814, "stop": 1754491434814, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c189e03b4adbf1ffd08ccde0c003cbf1"}], "uid": "18c4911f1477f7e8b52e7c52b7206918"}, {"name": "test_search_whatsapp_for_me", "children": [{"name": "TestEllaSearchWhatsappMe", "children": [{"name": "测试search whatsapp for me能正常执行", "uid": "364c7273e7ac42ee", "parentUid": "cd5851bdca5beda41724955a20608d69", "status": "failed", "time": {"start": 1754491468681, "stop": 1754491468681, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cd5851bdca5beda41724955a20608d69"}], "uid": "000d1f465ed860bdf1ed56e67ee1ae93"}, {"name": "test_set_app_auto_rotate", "children": [{"name": "TestEllaSetAppAutoRotate", "children": [{"name": "测试set app auto rotate返回正确的不支持响应", "uid": "9496f06be9b0368f", "parentUid": "5ec31d73ee5a8cbd16b741e2a0c12725", "status": "failed", "time": {"start": 1754491502765, "stop": 1754491502765, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5ec31d73ee5a8cbd16b741e2a0c12725"}], "uid": "c4e1b97dbe16d5e25caab90c80f85213"}, {"name": "test_set_app_notifications", "children": [{"name": "TestEllaSetAppNotifications", "children": [{"name": "测试set app notifications返回正确的不支持响应", "uid": "9f1b567357aa63da", "parentUid": "e2b37df150554a2656df5f000b5f3877", "status": "failed", "time": {"start": 1754491536654, "stop": 1754491536654, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e2b37df150554a2656df5f000b5f3877"}], "uid": "c477cad1f43ef2333b7849edf5ce05ed"}, {"name": "test_set_battery_saver_settings", "children": [{"name": "TestEllaSetBatterySaverSettings", "children": [{"name": "测试set battery saver settings返回正确的不支持响应", "uid": "21b7d6cbcb6ed0b3", "parentUid": "c420f6bca101c62ba94f9e8e7f31a573", "status": "failed", "time": {"start": 1754491570571, "stop": 1754491570571, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c420f6bca101c62ba94f9e8e7f31a573"}], "uid": "8b4c47455f601b3d7d6f1e100772a44a"}, {"name": "test_set_call_back_with_last_used_sim", "children": [{"name": "TestEllaSetCallBackLastUsedSim", "children": [{"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "d000d74ce17fdd03", "parentUid": "4c0f190c6f4aee497792a6d856a0035a", "status": "failed", "time": {"start": 1754491604681, "stop": 1754491604681, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c0f190c6f4aee497792a6d856a0035a"}], "uid": "7cea4873dfe6764b25d4b6368eec9cfb"}, {"name": "test_set_color_style", "children": [{"name": "TestEllaSetColorStyle", "children": [{"name": "测试set color style返回正确的不支持响应", "uid": "2d060a6b4f115242", "parentUid": "a93fd41464aaeca8c5187ded9998a8cf", "status": "failed", "time": {"start": 1754491638468, "stop": 1754491638468, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a93fd41464aaeca8c5187ded9998a8cf"}], "uid": "ba0686e84222e058748253df8047a50b"}, {"name": "test_set_compatibility_mode", "children": [{"name": "TestEllaSetCompatibilityMode", "children": [{"name": "测试set compatibility mode返回正确的不支持响应", "uid": "e4f39a55dd8c7a46", "parentUid": "3b4b879cb25f4ec1c9e5f56f7b979c37", "status": "failed", "time": {"start": 1754491672752, "stop": 1754491672752, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3b4b879cb25f4ec1c9e5f56f7b979c37"}], "uid": "1909cdae019ead080ae02a578b4d7316"}, {"name": "test_set_cover_screen_apps", "children": [{"name": "TestEllaSetCoverScreenApps", "children": [{"name": "测试set cover screen apps返回正确的不支持响应", "uid": "1e0c6b7b2439c45e", "parentUid": "37a4a9b94837c06f3d0d560b86bbe29b", "status": "failed", "time": {"start": 1754491707155, "stop": 1754491707155, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "37a4a9b94837c06f3d0d560b86bbe29b"}], "uid": "5df35bd6f80d4c6657cbae8b27064aab"}, {"name": "test_set_customized_cover_screen", "children": [{"name": "TestEllaSetCustomizedCoverScreen", "children": [{"name": "测试set customized cover screen返回正确的不支持响应", "uid": "b7758b8752b969b0", "parentUid": "4d3fece58e4f70510cd292befe33b96a", "status": "failed", "time": {"start": 1754491741363, "stop": 1754491741363, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4d3fece58e4f70510cd292befe33b96a"}], "uid": "3fa52a12d7acae5645ea3aa75477d44b"}, {"name": "test_set_date_time", "children": [{"name": "TestEllaSetDateTime", "children": [{"name": "测试set date & time返回正确的不支持响应", "uid": "1302051b604a5885", "parentUid": "9d950448d0ff30907a7af780f114a130", "status": "failed", "time": {"start": 1754491775165, "stop": 1754491775165, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d950448d0ff30907a7af780f114a130"}], "uid": "8c30598a8b1f5940667edde71b30791c"}, {"name": "test_set_edge_mistouch_prevention", "children": [{"name": "TestEllaSetEdgeMistouchPrevention", "children": [{"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "81503ead67be5ff1", "parentUid": "dada83ec1ab33079d5f86d4ac7ee1f04", "status": "failed", "time": {"start": 1754491809607, "stop": 1754491809607, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dada83ec1ab33079d5f86d4ac7ee1f04"}], "uid": "a30de810322f2776f8c899c615c4be77"}, {"name": "test_set_flex_still_mode", "children": [{"name": "TestEllaSetFlexStillMode", "children": [{"name": "测试set flex-still mode返回正确的不支持响应", "uid": "281d4eee43b52921", "parentUid": "a51d316b425964f68fef53807195a46f", "status": "failed", "time": {"start": 1754491843808, "stop": 1754491843808, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a51d316b425964f68fef53807195a46f"}], "uid": "0100b1b2aa4c49467859f43e6353bedb"}, {"name": "test_set_flip_case_feature", "children": [{"name": "TestEllaSetFlipCaseFeature", "children": [{"name": "测试set flip case feature返回正确的不支持响应", "uid": "48fec51a8fe153c0", "parentUid": "d5b01634d272a358f06ff35ca4ca4a23", "status": "failed", "time": {"start": 1754491877618, "stop": 1754491877618, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d5b01634d272a358f06ff35ca4ca4a23"}], "uid": "659c6f22cfed2953c4ba8d4b8dd7294a"}, {"name": "test_set_floating_windows", "children": [{"name": "TestEllaSetFloatingWindows", "children": [{"name": "测试set floating windows返回正确的不支持响应", "uid": "a45dfd46ac1039a0", "parentUid": "e15e2e981396bcf4d57ba068a480c5b1", "status": "failed", "time": {"start": 1754491911964, "stop": 1754491911964, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e15e2e981396bcf4d57ba068a480c5b1"}], "uid": "1dc2787970a8d9c589a8ae300f462858"}, {"name": "test_set_folding_screen_zone", "children": [{"name": "TestEllaSetFoldingScreenZone", "children": [{"name": "测试set folding screen zone返回正确的不支持响应", "uid": "3139a90de321bf94", "parentUid": "47c139cd34b0a780094ffb98e1812572", "status": "failed", "time": {"start": 1754491946633, "stop": 1754491946633, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47c139cd34b0a780094ffb98e1812572"}], "uid": "45d2d7d6fad9b4da39cc6dac0c4fa337"}, {"name": "test_set_font_size", "children": [{"name": "TestEllaSetFontSize", "children": [{"name": "测试set font size返回正确的不支持响应", "uid": "c6e0954bd4fb0f5f", "parentUid": "80b6fcd56edd3e33d66094a25cc5d31b", "status": "failed", "time": {"start": 1754491981088, "stop": 1754491981088, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80b6fcd56edd3e33d66094a25cc5d31b"}], "uid": "9a3389ccf2eca5621def99072303502f"}, {"name": "test_set_gesture_navigation", "children": [{"name": "TestEllaSetGestureNavigation", "children": [{"name": "测试set gesture navigation返回正确的不支持响应", "uid": "7fbeb58f6ba7a50", "parentUid": "5a5a307b4aee14702733cda564e2594e", "status": "failed", "time": {"start": 1754492015552, "stop": 1754492015552, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a5a307b4aee14702733cda564e2594e"}], "uid": "f45d715ad98a0594e567c013238fb055"}, {"name": "test_set_languages", "children": [{"name": "TestEllaSetLanguages", "children": [{"name": "测试set languages返回正确的不支持响应", "uid": "75a4fdcde784a72f", "parentUid": "3e28dcd9a9cdb251320f0e3b7e655a4d", "status": "failed", "time": {"start": 1754492049446, "stop": 1754492049446, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e28dcd9a9cdb251320f0e3b7e655a4d"}], "uid": "eedc9f2088f9e7ed345a41dcf938b3aa"}, {"name": "test_set_lockscreen_passwords", "children": [{"name": "TestEllaSetLockscreenPasswords", "children": [{"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "cee648585821b0fe", "parentUid": "41dd05458efc06498e30ee8a35ffd98f", "status": "failed", "time": {"start": 1754492083595, "stop": 1754492083595, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "41dd05458efc06498e30ee8a35ffd98f"}], "uid": "62cf905659379d62ce72a50cba40e0f8"}, {"name": "test_set_my_fonts", "children": [{"name": "TestEllaSetMyFonts", "children": [{"name": "测试set my fonts返回正确的不支持响应", "uid": "ea9bd37543fd3683", "parentUid": "3feb62634faa8c2680759a67a0f0b2df", "status": "failed", "time": {"start": 1754492118018, "stop": 1754492118019, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3feb62634faa8c2680759a67a0f0b2df"}], "uid": "64427e107c50bb3a09e1b67c0bf1e141"}, {"name": "test_set_my_themes", "children": [{"name": "TestEllaSetMyThemes", "children": [{"name": "测试set my themes返回正确的不支持响应", "uid": "3004ceb745527485", "parentUid": "b86797063b9d865d350ec21ac7112b5f", "status": "failed", "time": {"start": 1754492151822, "stop": 1754492151822, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b86797063b9d865d350ec21ac7112b5f"}], "uid": "716eecd28b7d4b5ede25b5df0e5507c2"}, {"name": "test_set_off_a_firework", "children": [{"name": "TestEllaSetOffFirework", "children": [{"name": "测试set off a firework能正常执行", "uid": "5efaff886e3fa80b", "parentUid": "021acb4eaaacdcf72d568b2312fa9bfc", "status": "failed", "time": {"start": 1754492185666, "stop": 1754492185666, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "021acb4eaaacdcf72d568b2312fa9bfc"}], "uid": "8425a95213c647a6dbc3c547777d6228"}, {"name": "test_set_parallel_windows", "children": [{"name": "TestEllaSetParallelWindows", "children": [{"name": "测试set parallel windows返回正确的不支持响应", "uid": "131444e9a0aa3b4a", "parentUid": "e9b410d4bf477a3418fde54f839d7e6d", "status": "failed", "time": {"start": 1754492219671, "stop": 1754492219671, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9b410d4bf477a3418fde54f839d7e6d"}], "uid": "55c6cd519ecfe75eb797fa2c66d0d75f"}, {"name": "test_set_personal_hotspot", "children": [{"name": "TestEllaSetPersonalHotspot", "children": [{"name": "测试set personal hotspot返回正确的不支持响应", "uid": "a0c877bf5b68dcb1", "parentUid": "b3579038871378a6fdcc9bb20849bdf1", "status": "failed", "time": {"start": 1754492253715, "stop": 1754492253715, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b3579038871378a6fdcc9bb20849bdf1"}], "uid": "b482fd86e6dab754c48a5bf619de5afb"}, {"name": "test_set_phantom_v_pen", "children": [{"name": "TestEllaSetPhantomVPen", "children": [{"name": "测试set phantom v pen返回正确的不支持响应", "uid": "d8fd0397f7c4e6aa", "parentUid": "7c5353b9ef36136e92a92708b7e45bb3", "status": "failed", "time": {"start": 1754492287573, "stop": 1754492287573, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c5353b9ef36136e92a92708b7e45bb3"}], "uid": "107e8fe279ca1fd2b153352fda86fb4a"}, {"name": "test_set_phone_number", "children": [{"name": "TestEllaSetPhoneNumber", "children": [{"name": "测试set phone number返回正确的不支持响应", "uid": "36e3d9257c05a679", "parentUid": "96b22c407c943460e8486fc825169a80", "status": "failed", "time": {"start": 1754492321593, "stop": 1754492321593, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "96b22c407c943460e8486fc825169a80"}], "uid": "e9cd1fa5db40d2ec604808d9c497fadb"}, {"name": "test_set_scheduled_power_on_off_and_restart", "children": [{"name": "TestEllaSetScheduledPowerOffRestart", "children": [{"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "279ad67ed21598d8", "parentUid": "58b82848dae0adadd98c95016e0b8711", "status": "failed", "time": {"start": 1754492355647, "stop": 1754492355647, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58b82848dae0adadd98c95016e0b8711"}], "uid": "e6b0ed51c0d597afdb5bf8c4e6af53c3"}, {"name": "test_set_screen_refresh_rate", "children": [{"name": "TestEllaSetScreenRefreshRate", "children": [{"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "b5759ffb8039a256", "parentUid": "3e9009cde7fd47070b3529a63de695c3", "status": "failed", "time": {"start": 1754492389562, "stop": 1754492389562, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e9009cde7fd47070b3529a63de695c3"}], "uid": "c9de8a50bcaa3dc3fd1b3a9409aa8ba0"}, {"name": "test_set_screen_relay", "children": [{"name": "TestEllaSetScreenRelay", "children": [{"name": "测试set screen relay返回正确的不支持响应", "uid": "2df8c73485357aa8", "parentUid": "61fe1e0731921d580561f77ccf2de7a2", "status": "failed", "time": {"start": 1754492424236, "stop": 1754492424236, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "61fe1e0731921d580561f77ccf2de7a2"}], "uid": "7ebda1dd475915217421f499f80df8c5"}, {"name": "test_set_screen_timeout", "children": [{"name": "TestEllaSetScreenTimeout", "children": [{"name": "测试set screen timeout返回正确的不支持响应", "uid": "6af11f61922afd2f", "parentUid": "3f7e6a161af6de02774a99275b95e1d7", "status": "failed", "time": {"start": 1754492458670, "stop": 1754492458670, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f7e6a161af6de02774a99275b95e1d7"}], "uid": "3ad3475cd36fab63233e05c3002bb8c5"}, {"name": "test_set_screen_to_minimum_brightness", "children": [{"name": "TestEllaSetScreenMinimumBrightness", "children": [{"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "b4f4ccf9053281c7", "parentUid": "70cd5196189a628ba70f4e189ab3ed2a", "status": "failed", "time": {"start": 1754492493249, "stop": 1754492493249, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "70cd5196189a628ba70f4e189ab3ed2a"}], "uid": "ccff2bc4a244de015f51207ad9fb9973"}, {"name": "test_set_sim_ringtone", "children": [{"name": "TestEllaSetSimRingtone", "children": [{"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "de40562d3921e98a", "parentUid": "b540694383c716efd22be6cf332cff66", "status": "failed", "time": {"start": 1754492527633, "stop": 1754492527633, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b540694383c716efd22be6cf332cff66"}], "uid": "f521b50083932bc320d541b59f4fa12e"}, {"name": "test_set_smart_hub", "children": [{"name": "TestEllaSetSmartHub", "children": [{"name": "测试set smart hub返回正确的不支持响应", "uid": "e1ee406f1611eb73", "parentUid": "9982a7cb76e7edde540c33f77bcb4131", "status": "failed", "time": {"start": 1754492562170, "stop": 1754492562170, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9982a7cb76e7edde540c33f77bcb4131"}], "uid": "9159365ae1f13aa7202a2ecabae352cd"}, {"name": "test_set_smart_panel", "children": [{"name": "TestEllaSetSmartPanel", "children": [{"name": "测试set smart panel返回正确的不支持响应", "uid": "a79d5f2983e68bda", "parentUid": "8186149225876d2c7f48b35cdec99dd3", "status": "failed", "time": {"start": 1754492596508, "stop": 1754492596508, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8186149225876d2c7f48b35cdec99dd3"}], "uid": "6d2c866f4f10ac8e5f2e0399839eca9f"}, {"name": "test_set_special_function", "children": [{"name": "TestEllaSetSpecialFunction", "children": [{"name": "测试set special function返回正确的不支持响应", "uid": "d5f2f0dc1b836163", "parentUid": "a485e9dc82964f874c4ca66999bcf15a", "status": "failed", "time": {"start": 1754492630353, "stop": 1754492630353, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a485e9dc82964f874c4ca66999bcf15a"}], "uid": "1385459e098e23ca404584fba544f87b"}, {"name": "test_set_split_screen_apps", "children": [{"name": "TestEllaSetSplitScreenApps", "children": [{"name": "测试set split-screen apps返回正确的不支持响应", "uid": "1ae31f6c836ef647", "parentUid": "e704a416305aeb7b8861f78da3d399fd", "status": "failed", "time": {"start": 1754492664470, "stop": 1754492664470, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e704a416305aeb7b8861f78da3d399fd"}], "uid": "18aa5f1d09e55e245cdba84af89bdb4f"}, {"name": "test_set_timezone", "children": [{"name": "TestEllaSetTimezone", "children": [{"name": "测试set timezone返回正确的不支持响应", "uid": "393c6d5c28a42af0", "parentUid": "f68e969f0dc6fa17bc3cbb1c76e0468d", "status": "failed", "time": {"start": 1754492698847, "stop": 1754492698847, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f68e969f0dc6fa17bc3cbb1c76e0468d"}], "uid": "6b5e2d316297d46c06b128e02645ba18"}, {"name": "test_set_ultra_power_saving", "children": [{"name": "TestEllaSetUltraPowerSaving", "children": [{"name": "测试set ultra power saving返回正确的不支持响应", "uid": "762351f9affe7b3a", "parentUid": "c853a846bf2e6118074af640a3bdfded", "status": "failed", "time": {"start": 1754492733532, "stop": 1754492733532, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c853a846bf2e6118074af640a3bdfded"}], "uid": "516f9e12e9fe59099bceb66c299b4b06"}, {"name": "test_start_running", "children": [{"name": "TestEllaStartRunning", "children": [{"name": "测试start running能正常执行", "uid": "a4c01f6014bd6c51", "parentUid": "a24fb40874550e9588bb7a1e759255e9", "status": "failed", "time": {"start": 1754492767858, "stop": 1754492767858, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a24fb40874550e9588bb7a1e759255e9"}], "uid": "d46bf7e5557e0738a865ecc07e2d584c"}, {"name": "test_start_walking", "children": [{"name": "TestEllaStartWalking", "children": [{"name": "测试start walking能正常执行", "uid": "5d77080ffe1fccbb", "parentUid": "c6ec8ec07b5dd2102a991e8a20680e71", "status": "failed", "time": {"start": 1754492802065, "stop": 1754492802065, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c6ec8ec07b5dd2102a991e8a20680e71"}], "uid": "a4e7eb3a60d774ac67cd2cc111cb9845"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "TestEllaSwitchEquilibriumMode", "children": [{"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "8f35d4cdbada546a", "parentUid": "0bfb9433b5ffe1874f0d5456da733833", "status": "failed", "time": {"start": 1754492835997, "stop": 1754492835997, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0bfb9433b5ffe1874f0d5456da733833"}], "uid": "6dfd47afc83ade6b7e64a599884aa82b"}, {"name": "test_switch_to_performance_mode", "children": [{"name": "TestEllaSwitchPerformanceMode", "children": [{"name": "测试switch to performance mode返回正确的不支持响应", "uid": "ab6461e475482ce", "parentUid": "e8f8d6344d1896b53428623c96ae9737", "status": "failed", "time": {"start": 1754492870120, "stop": 1754492870120, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e8f8d6344d1896b53428623c96ae9737"}], "uid": "660b4eae367594166e98133010b58c55"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "TestEllaSwitchPowerSavingMode", "children": [{"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "342df515163bc531", "parentUid": "69d2b561fc70829d0f0c3bcbc22bd24b", "status": "failed", "time": {"start": 1754492904126, "stop": 1754492904126, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "69d2b561fc70829d0f0c3bcbc22bd24b"}], "uid": "89fed13c0557d811b5a98a81c18249c0"}, {"name": "test_switching_charging_speed", "children": [{"name": "TestEllaSwitchingChargingSpeed", "children": [{"name": "测试switching charging speed能正常执行", "uid": "19f30a50229fa046", "parentUid": "364923db55b7a4749bd0f0a3d56b478a", "status": "failed", "time": {"start": 1754492938029, "stop": 1754492938029, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "364923db55b7a4749bd0f0a3d56b478a"}], "uid": "2bc0a442c3b51933b3e020072f001bf8"}, {"name": "test_take_notes", "children": [{"name": "TestEllaTakeNotes", "children": [{"name": "测试take notes能正常执行", "uid": "787d73d161918578", "parentUid": "44b041dc5921837d99b03ee251fe9a80", "status": "failed", "time": {"start": 1754492972187, "stop": 1754492972187, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "44b041dc5921837d99b03ee251fe9a80"}], "uid": "9ce4c8d89aae56754ff0ae0d7cf834cc"}, {"name": "test_tell_me_joke", "children": [{"name": "TestEllaTellMeJoke", "children": [{"name": "测试tell me joke能正常执行", "uid": "9e625edf27bc68e4", "parentUid": "7d17397dac8db6170e4185f194ee95e9", "status": "failed", "time": {"start": 1754493006073, "stop": 1754493006073, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7d17397dac8db6170e4185f194ee95e9"}], "uid": "eeee5549335b4d4ecf3fac8dfda2e40d"}, {"name": "test_the_second", "children": [{"name": "TestEllaSecond", "children": [{"name": "测试the second返回正确的不支持响应", "uid": "5673296d3573d0a2", "parentUid": "ba446261c47bba6a43d497764fa39a1e", "status": "failed", "time": {"start": 1754493040820, "stop": 1754493040820, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ba446261c47bba6a43d497764fa39a1e"}], "uid": "3ef96941774e396d5f61e6c374c2a964"}, {"name": "test_turn_off_driving_mode", "children": [{"name": "TestEllaTurnOffDrivingMode", "children": [{"name": "测试turn off driving mode返回正确的不支持响应", "uid": "72369a52d654526c", "parentUid": "24ecd251ff3a56a4c2b20fac8ab55f2f", "status": "failed", "time": {"start": 1754493075432, "stop": 1754493075432, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "24ecd251ff3a56a4c2b20fac8ab55f2f"}], "uid": "ca08b2eb3eff172bf5d579af818a108f"}, {"name": "test_turn_off_show_battery_percentage", "children": [{"name": "TestEllaTurnOffShowBatteryPercentage", "children": [{"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "79a4858764b02126", "parentUid": "a93695196bd86c4de1585781fedbe080", "status": "failed", "time": {"start": 1754493109867, "stop": 1754493109867, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a93695196bd86c4de1585781fedbe080"}], "uid": "492b5eb198aa5b8127740d2701bc4490"}, {"name": "test_turn_on_driving_mode", "children": [{"name": "TestEllaTurnDrivingMode", "children": [{"name": "测试turn on driving mode返回正确的不支持响应", "uid": "93ff373023646cf6", "parentUid": "6160fde1e3814087ef49c469733a5e59", "status": "failed", "time": {"start": 1754493143901, "stop": 1754493143901, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6160fde1e3814087ef49c469733a5e59"}], "uid": "7dbd97d94de7ac9fc92ce34bec95b544"}, {"name": "test_turn_on_high_brightness_mode", "children": [{"name": "TestEllaTurnHighBrightnessMode", "children": [{"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "e9e9c48b1db33302", "parentUid": "afa769cc76891525f7fca4f496a4aa9e", "status": "failed", "time": {"start": 1754493177669, "stop": 1754493177669, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "afa769cc76891525f7fca4f496a4aa9e"}], "uid": "9751732cdae9d7a072c84060577699cd"}, {"name": "test_turn_on_show_battery_percentage", "children": [{"name": "TestEllaTurnShowBatteryPercentage", "children": [{"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "921d2165e4b8b2c6", "parentUid": "9a14e86a2195f01365f057ce6c9b9e71", "status": "failed", "time": {"start": 1754493211769, "stop": 1754493211769, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9a14e86a2195f01365f057ce6c9b9e71"}], "uid": "d5d7ce8427ad486d072c9aae7e2d5b3f"}, {"name": "test_vedio_call_number_by_whatsapp", "children": [{"name": "TestEllaVedioCallNumberWhatsapp", "children": [{"name": "测试vedio call number by whatsapp能正常执行", "uid": "7eb514c65b1432a5", "parentUid": "0d26a41f5b9615ad2942aaedf4c3033b", "status": "failed", "time": {"start": 1754493246248, "stop": 1754493246248, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d26a41f5b9615ad2942aaedf4c3033b"}], "uid": "2aeb91366c9de96caf75e843c25ff4e8"}, {"name": "test_voice_setting_page", "children": [{"name": "TestEllaVoiceSettingPage", "children": [{"name": "测试Voice setting page返回正确的不支持响应", "uid": "b883efebf4029015", "parentUid": "087c3d15647e1625a6f4e506048dafc7", "status": "failed", "time": {"start": 1754493280335, "stop": 1754493280335, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "087c3d15647e1625a6f4e506048dafc7"}], "uid": "20224dbf8d73e834d2be6a17094deb05"}, {"name": "test_what_date_is_it", "children": [{"name": "TestEllaWhatDateIsIt", "children": [{"name": "测试what date is it能正常执行", "uid": "7452352603fb4f0", "parentUid": "a6477b2e019a056f6425d2950e664eda", "status": "failed", "time": {"start": 1754493314194, "stop": 1754493314194, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a6477b2e019a056f6425d2950e664eda"}], "uid": "462cfd09072e082d758a28b5f86133d1"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "TestEllaWhatSWheatherToday", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "182ba89eca203a55", "parentUid": "34ec790ead0442abc792eadbded82b45", "status": "failed", "time": {"start": 1754493348988, "stop": 1754493348988, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34ec790ead0442abc792eadbded82b45"}], "uid": "93ccf0164b324d8401f93f28368fa587"}, {"name": "test_what_time_is_it_in_london", "children": [{"name": "TestEllaWhatTimeIsItLondon", "children": [{"name": "测试what time is it in London能正常执行", "uid": "b68ac7588583315d", "parentUid": "9e6b9c0060829efcc73d4ea740df1b49", "status": "failed", "time": {"start": 1754493383221, "stop": 1754493383221, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e6b9c0060829efcc73d4ea740df1b49"}], "uid": "af53bd7e2482c27610ecc08ef1183aeb"}, {"name": "test_yandex_eats", "children": [{"name": "TestEllaYandexEats", "children": [{"name": "测试yandex eats返回正确的不支持响应", "uid": "70d9a7a738f698e4", "parentUid": "3c01adf30480b292f5f2b75550755b66", "status": "failed", "time": {"start": 1754493417107, "stop": 1754493417107, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3c01adf30480b292f5f2b75550755b66"}], "uid": "e099b3554c54efdee17091c4c12f77c7"}], "uid": "09cb3650ff0a2cc2af23d31dd3c975a2"}]}