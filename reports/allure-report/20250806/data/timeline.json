{"uid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "name": "timeline", "children": [{"name": "SHCypanyuy0", "children": [{"name": "13932-MainThread", "children": [{"name": "测试long screenshot能正常执行", "uid": "6f950886f9130f1d", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486588509, "stop": 1754486605351, "duration": 16842}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试countdown 5 min能正常执行", "uid": "56d2b9e1c2fc39f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754486409251, "stop": 1754486426730, "duration": 17479}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open wifi", "uid": "1ccd3d9645a92f1f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486796964, "stop": 1754486811101, "duration": 14137}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how's the weather today?返回正确的不支持响应", "uid": "98a23a7493ce922d", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485130948, "stop": 1754485148953, "duration": 18005}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "18f399f8e5fa9ce7", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489681401, "stop": 1754489681401, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play jay chou's music by spotify", "uid": "785b6165f13072c3", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484131304, "stop": 1754484148477, "duration": 17173}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the screen record能正常执行", "uid": "d8abb8f7b66ff219", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754488052845, "stop": 1754488069899, "duration": 17054}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play football video by youtube", "uid": "9d20fb01702a451", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490993122, "stop": 1754490993122, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how is the weather today能正常执行", "uid": "4f2d2b6c9aaf70e6", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485073283, "stop": 1754485091642, "duration": 18359}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me take a long screenshot能正常执行", "uid": "324c32662b1b44a7", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486527843, "stop": 1754486546055, "duration": 18212}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open dialer能正常执行", "uid": "eafb693ce639dfd3", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483836056, "stop": 1754483858924, "duration": 22868}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "8f35d4cdbada546a", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492835997, "stop": 1754492835997, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set flex-still mode返回正确的不支持响应", "uid": "281d4eee43b52921", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491843808, "stop": 1754491843808, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试previous music能正常执行", "uid": "f261e24a4d6c223d", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484257844, "stop": 1754484272643, "duration": 14799}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "cee648585821b0fe", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492083595, "stop": 1754492083595, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试summarize content on this page能正常执行", "uid": "cb536fc6dbd8afa0", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485675842, "stop": 1754485690128, "duration": 14286}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "c7e38f605f9066ee", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489783936, "stop": 1754489783936, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "dc6a66865aca9b84", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487198377, "stop": 1754487216856, "duration": 18479}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable magic voice changer能正常执行", "uid": "21f356a729ddd2", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484891757, "stop": 1754484905166, "duration": 13409}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check my to-do list能正常执行", "uid": "f6680ac844d1bbf7", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488998385, "stop": 1754488998385, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open bluetooth", "uid": "bf9c38fe63dfb10f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486713130, "stop": 1754486727490, "duration": 14360}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's the weather like in shanghai today能正常执行", "uid": "24be109e76a80bd6", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485927315, "stop": 1754485945789, "duration": 18474}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "6dc578f8342571a5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754485540658, "stop": 1754485555831, "duration": 15173}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play rock music", "uid": "16387a05e5a29fcb", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484192926, "stop": 1754484212122, "duration": 19196}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试could you please search an for me能正常执行", "uid": "f990be4b0bb6b336", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754484863011, "stop": 1754484878667, "duration": 15656}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "872f2d0df76fd076", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490259190, "stop": 1754490259190, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download qq能正常执行", "uid": "36600db5fc8f1ba2", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754488253518, "stop": 1754488270639, "duration": 17121}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试searching for a method of violent murder能正常执行", "uid": "a34c28ac75d4d56f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "broken", "time": {"start": 1754485482383, "stop": 1754485498034, "duration": 15651}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play love sotry", "uid": "960f945a0e52aa89", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491027353, "stop": 1754491027353, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试next channel能正常执行", "uid": "c38fd4800ac0add4", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483686048, "stop": 1754483700009, "duration": 13961}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "1d658bbc993404eb", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487914418, "stop": 1754487928231, "duration": 13813}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "a231ab2220a212ac", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487941157, "stop": 1754487955456, "duration": 14299}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set color style返回正确的不支持响应", "uid": "2d060a6b4f115242", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491638468, "stop": 1754491638468, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "3d61731e0bcd9778", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489373111, "stop": 1754489373111, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "e06d746a0349fe32", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486496685, "stop": 1754486515106, "duration": 18421}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set customized cover screen返回正确的不支持响应", "uid": "b7758b8752b969b0", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491741363, "stop": 1754491741363, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off light theme能正常执行", "uid": "5052494e445ea3a2", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487805511, "stop": 1754487819220, "duration": 13709}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "20fe2dd2bfdd7b37", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490292806, "stop": 1754490292806, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to nfc settings", "uid": "29843c65bf2d03c6", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490548481, "stop": 1754490548481, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Barrage Notification能正常执行", "uid": "a4197bd04ef3d35d", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754487369682, "stop": 1754487385769, "duration": 16087}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause music能正常执行", "uid": "a0a18ec854bf4f4", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483985350, "stop": 1754483998995, "duration": 13645}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "de40562d3921e98a", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492527633, "stop": 1754492527633, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set languages返回正确的不支持响应", "uid": "75a4fdcde784a72f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492049446, "stop": 1754492049446, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how to set screenshots返回正确的不支持响应", "uid": "6bb6c7260139caa2", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490225039, "stop": 1754490225039, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试screen record能正常执行", "uid": "fbf4ec4a014b304c", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486861875, "stop": 1754486880906, "duration": 19031}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "921d2165e4b8b2c6", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754493211769, "stop": 1754493211769, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's the wheather today?能正常执行", "uid": "182ba89eca203a55", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754493348988, "stop": 1754493348988, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable unfreeze返回正确的不支持响应", "uid": "b0fa49b954710d82", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489510717, "stop": 1754489510717, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigate from to red square能正常执行", "uid": "c06d0b280e1c66e9", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488350360, "stop": 1754488350360, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close aivana能正常执行", "uid": "5dac0aba48d9341d", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484695704, "stop": 1754484728197, "duration": 32493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试the second返回正确的不支持响应", "uid": "5673296d3573d0a2", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754493040820, "stop": 1754493040820, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set folding screen zone返回正确的不支持响应", "uid": "3139a90de321bf94", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491946633, "stop": 1754491946633, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play video", "uid": "95b3160676d4ff72", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491128564, "stop": 1754491128564, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试resume music能正常执行", "uid": "59a2f1889b5d1733", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754484285303, "stop": 1754484298395, "duration": 13092}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable network enhancement返回正确的不支持响应", "uid": "21e4a005e4281783", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489407275, "stop": 1754489407275, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "d000d74ce17fdd03", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491604681, "stop": 1754491604681, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令", "uid": "a410281354d5dfa1", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483923329, "stop": 1754483946357, "duration": 23028}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off the 7AM alarm", "uid": "62059e19cf44d484", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484397719, "stop": 1754484414263, "duration": 16544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set phone number返回正确的不支持响应", "uid": "36e3d9257c05a679", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492321593, "stop": 1754492321593, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check front camera information能正常执行", "uid": "1dd78894947cdfc0", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754486277352, "stop": 1754486296726, "duration": 19374}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试global gdp trends能正常执行", "uid": "67c045159680a9ba", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484947633, "stop": 1754484964615, "duration": 16982}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what·s the weather today？能正常执行", "uid": "3eabaf55f35a4e66", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485958650, "stop": 1754485979502, "duration": 20852}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "79a4858764b02126", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754493109867, "stop": 1754493109867, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a photo能正常执行", "uid": "bf068ac0f1aaa3f3", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754487624970, "stop": 1754487655439, "duration": 30469}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close whatsapp能正常执行", "uid": "84cdaf12b67214f6", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484836336, "stop": 1754484850464, "duration": 14128}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable call rejection返回正确的不支持响应", "uid": "61e0a20218e2b282", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489304958, "stop": 1754489304958, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试search the address in the image能正常执行", "uid": "62c368eb30849dec", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491434814, "stop": 1754491434814, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop run能正常执行", "uid": "d04ff44af557a074", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485622231, "stop": 1754485635679, "duration": 13448}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Search for addresses on the screen能正常执行", "uid": "1ee5d8b15798efff", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491400647, "stop": 1754491400647, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试driving mode返回正确的不支持响应", "uid": "4d07f0c6c7ca376f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489613176, "stop": 1754489613176, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch Magic Voice to Grace能正常执行", "uid": "a4c00a2e50a210dd", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487316406, "stop": 1754487330699, "duration": 14293}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open whatsapp", "uid": "720a5005d504c9d4", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490856630, "stop": 1754490856630, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Hyper Charge能正常执行", "uid": "948460c746878063", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754487491529, "stop": 1754487504641, "duration": 13112}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switched to data mode能正常执行", "uid": "4f285330353aa837", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754487597893, "stop": 1754487611697, "duration": 13804}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable running lock返回正确的不支持响应", "uid": "65dc203fdb610f69", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489885466, "stop": 1754489885466, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试maximum volume能正常执行", "uid": "56bfc1a59701e4b9", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486618078, "stop": 1754486633129, "duration": 15051}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play jay chou's music", "uid": "8b0780f7c44c1094", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484099673, "stop": 1754484118282, "duration": 18609}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试change man voice能正常执行", "uid": "cbd155ed4bc579f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488725683, "stop": 1754488725683, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试continue music能正常执行", "uid": "150527037135ce30", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483540136, "stop": 1754483553519, "duration": 13383}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to equilibrium mode能正常执行", "uid": "aa06e05449f42426", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754487428781, "stop": 1754487445077, "duration": 16296}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试change your voice能正常执行", "uid": "3a8cee725f31ec40", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488759791, "stop": 1754488759791, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set my themes返回正确的不支持响应", "uid": "3004ceb745527485", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492151822, "stop": 1754492151822, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试more settings返回正确的不支持响应", "uid": "97abb069c96ff50c", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490685463, "stop": 1754490685463, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to default mode能正常执行", "uid": "da92ff9a78d65b18", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487399113, "stop": 1754487415490, "duration": 16377}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen timeout返回正确的不支持响应", "uid": "6af11f61922afd2f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492458670, "stop": 1754492458670, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download basketball能正常执行", "uid": "2dcf43a24b3034da", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488226197, "stop": 1754488240470, "duration": 14273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "1736a9777edb77af", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490376488, "stop": 1754490376488, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on location services能正常执行", "uid": "426f048698440e79", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487968462, "stop": 1754487982877, "duration": 14415}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close folax能正常执行", "uid": "84f2ec66ef73d014", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484788120, "stop": 1754484823514, "duration": 35394}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable unfreeze返回正确的不支持响应", "uid": "3ad176dfb601300e", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489953337, "stop": 1754489953337, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set off a firework能正常执行", "uid": "5efaff886e3fa80b", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492185666, "stop": 1754492185666, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试reset phone返回正确的不支持响应", "uid": "6c2113f7cf07a5f1", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491298781, "stop": 1754491298781, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "51b5e93e599225aa", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483566493, "stop": 1754483580500, "duration": 14007}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start screen recording能正常执行", "uid": "93bd3202f99050e7", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487107718, "stop": 1754487125254, "duration": 17536}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set app notifications返回正确的不支持响应", "uid": "9f1b567357aa63da", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491536654, "stop": 1754491536654, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试tell me a joke能正常执行", "uid": "f4753f2d44bd35b5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754485810136, "stop": 1754485825927, "duration": 15791}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试give me some money能正常执行", "uid": "a2c6fbbe98591301", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484917871, "stop": 1754484935237, "duration": 17366}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take notes on how to build a treehouse能正常执行", "uid": "18284c5918c912a5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485783943, "stop": 1754485797292, "duration": 13349}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off driving mode返回正确的不支持响应", "uid": "72369a52d654526c", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754493075432, "stop": 1754493075432, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start record能正常执行", "uid": "937f207343b8f0c2", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487046321, "stop": 1754487063346, "duration": 17025}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the screen record能正常执行", "uid": "5c6774d94dd8b0dd", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487230107, "stop": 1754487247631, "duration": 17524}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on bluetooth能正常执行", "uid": "2674d3ebdb6a082a", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487860460, "stop": 1754487874263, "duration": 13803}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to call notifications返回正确的不支持响应", "uid": "53d43e24245acf0a", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490428166, "stop": 1754490428166, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set gesture navigation返回正确的不支持响应", "uid": "7fbeb58f6ba7a50", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492015552, "stop": 1754492015552, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "f56294cf89c411bc", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489817654, "stop": 1754489817654, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's the wheather today?能正常执行", "uid": "f9dec29930734510", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754485992367, "stop": 1754486007276, "duration": 14909}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download basketball返回正确的不支持响应", "uid": "4634ae81442a4912", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489578969, "stop": 1754489578969, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on nfc能正常执行", "uid": "ac959bcc15134bc9", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487996245, "stop": 1754488010760, "duration": 14515}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download app能正常执行", "uid": "13a234a2652ea3ee", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754488197689, "stop": 1754488213139, "duration": 15450}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a note on how to build a treehouse能正常执行", "uid": "2238b295fb197f63", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485757660, "stop": 1754485771164, "duration": 13504}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me take a screenshot能正常执行", "uid": "ec98df5d96735ef2", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486558792, "stop": 1754486575565, "duration": 16773}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试who is harry potter能正常执行", "uid": "a4a4e1574a0da588", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486103923, "stop": 1754486120135, "duration": 16212}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to power saving mode能正常执行", "uid": "bda91739b78aea60", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487544598, "stop": 1754487558679, "duration": 14081}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "cc78c9c9b8318173", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489545030, "stop": 1754489545030, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigate to shanghai disneyland能正常执行", "uid": "25bd4771bf8a6eb2", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488383986, "stop": 1754488383986, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "af1edd5c403db9e4", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487076214, "stop": 1754487094660, "duration": 18446}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch charging modes能正常执行", "uid": "7f6bcf1a52131d92", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754487289081, "stop": 1754487302980, "duration": 13899}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the lucky能正常执行", "uid": "cd10f690763a13fb", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488418472, "stop": 1754488418472, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how's the weather today in shanghai能正常执行", "uid": "8cc2b9f9f777deb5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485161947, "stop": 1754485180586, "duration": 18639}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable touch optimization返回正确的不支持响应", "uid": "cd66040bc232be03", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489919354, "stop": 1754489919354, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start walking能正常执行", "uid": "5d77080ffe1fccbb", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492802065, "stop": 1754492802065, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "5f1cafc0f7162a3b", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490512810, "stop": 1754490512810, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off the 8 am alarm", "uid": "b2f989e8480f8e5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484427410, "stop": 1754484443541, "duration": 16131}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set a timer for 10 minutes能正常执行", "uid": "c81e6489bc0d344c", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486923814, "stop": 1754486941667, "duration": 17853}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试hello hello能正常执行", "uid": "f297484465772a4b", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484977458, "stop": 1754484992659, "duration": 15201}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the first address in the image能正常执行", "uid": "18f772aa522a4c5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490754497, "stop": 1754490754497, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open folax能正常执行", "uid": "912cd52a27bc4c68", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483897917, "stop": 1754483910207, "duration": 12290}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set timezone返回正确的不支持响应", "uid": "393c6d5c28a42af0", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492698847, "stop": 1754492698847, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试why is my phone not ringing on incoming calls能正常执行", "uid": "76ccf467a41e6ff5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486163336, "stop": 1754486183062, "duration": 19726}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open facebook能正常执行", "uid": "c6e041786c62d65a", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488452897, "stop": 1754488452897, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how is the wheather today能正常执行", "uid": "9925076591462c7e", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485104484, "stop": 1754485118022, "duration": 13538}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "bf2f4d9af9c68c1c", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489851594, "stop": 1754489851594, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "c0ca4907bf7ec56c", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754488083064, "stop": 1754488101011, "duration": 17947}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试call mom through whatsapp能正常执行", "uid": "9a1de76db3282d62", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754484578227, "stop": 1754484599288, "duration": 21061}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "b5759ffb8039a256", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492389562, "stop": 1754492389562, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set floating windows返回正确的不支持响应", "uid": "a45dfd46ac1039a0", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491911964, "stop": 1754491911964, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set personal hotspot返回正确的不支持响应", "uid": "a0c877bf5b68dcb1", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492253715, "stop": 1754492253715, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "9f545d6fdce360ca", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486893641, "stop": 1754486911064, "duration": 17423}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "uid": "d692af8b399c4ed0", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754488327511, "stop": 1754488348943, "duration": 21432}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "c3973bb228d29704", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488964292, "stop": 1754488964292, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试where is the carlcare service outlet能正常执行", "uid": "d0d3620174008d52", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488169099, "stop": 1754488184592, "duration": 15493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "99fd73c7fe1b75de", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489202925, "stop": 1754489202925, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试What languages do you support能正常执行", "uid": "a2181f30c28e43a1", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485901463, "stop": 1754485914737, "duration": 13274}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试minimum volume能正常执行", "uid": "14594ed883fdf82e", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486684458, "stop": 1754486699961, "duration": 15503}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "5f1d181210935ac4", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490474108, "stop": 1754490474108, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play music", "uid": "e5b4b544bc7f272c", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484161498, "stop": 1754484179971, "duration": 18473}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试kill whatsapp能正常执行", "uid": "65c217eaa39ab993", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490617576, "stop": 1754490617576, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i want to make a call能正常执行", "uid": "a101b5b4fa37ac44", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485273623, "stop": 1754485293097, "duration": 19474}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "ac3ea7385a27322b", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487343552, "stop": 1754487356601, "duration": 13049}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试memory cleanup能正常执行", "uid": "12433c483fa32d60", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486646004, "stop": 1754486671642, "duration": 25638}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "65e88368633e1d8a", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490822706, "stop": 1754490822706, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试yandex eats返回正确的不支持响应", "uid": "70d9a7a738f698e4", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754493417107, "stop": 1754493417107, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how to say hello in french能正常执行", "uid": "3b03094079bed1b9", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485193306, "stop": 1754485205264, "duration": 11958}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试book a flight to paris返回正确的不支持响应", "uid": "e11b0aebcd0a881b", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "broken", "time": {"start": 1754484547829, "stop": 1754484565109, "duration": 17280}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "156bf9e6b63b254c", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487261064, "stop": 1754487276226, "duration": 15162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试decrease the brightness能正常执行", "uid": "72e5b9b0b1fda4b5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486439616, "stop": 1754486454073, "duration": 14457}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set my fonts返回正确的不支持响应", "uid": "ea9bd37543fd3683", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492118018, "stop": 1754492118019, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to smart charge能正常执行", "uid": "8ad6893c56a2881f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754487571731, "stop": 1754487584828, "duration": 13097}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "aa18187ae31ed8c0", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754485511220, "stop": 1754485527264, "duration": 16044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set flip case feature返回正确的不支持响应", "uid": "48fec51a8fe153c0", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491877618, "stop": 1754491877618, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause fm能正常执行", "uid": "27db06ac743e18ff", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483959022, "stop": 1754483972317, "duration": 13295}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open flashlight", "uid": "b1fe075113ee100d", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486767570, "stop": 1754486783909, "duration": 16339}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what date is it能正常执行", "uid": "7452352603fb4f0", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754493314194, "stop": 1754493314194, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable auto pickup返回正确的不支持响应", "uid": "7f1ee703c6a94109", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489236847, "stop": 1754489236847, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "bdff75fea7ed5983", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489066550, "stop": 1754489066550, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close ella能正常执行", "uid": "560b5c32ce990b3a", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484741120, "stop": 1754484775039, "duration": 33919}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to flash notification能正常执行", "uid": "4d5b68cf4e8d17f8", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754487458459, "stop": 1754487478499, "duration": 20040}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试who is j k rowling能正常执行", "uid": "44baa1a8a8e2a655", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486133031, "stop": 1754486150400, "duration": 17369}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试please show me where i am能正常执行", "uid": "971d76c9403829e0", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491196950, "stop": 1754491196950, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set date & time返回正确的不支持响应", "uid": "1302051b604a5885", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491775165, "stop": 1754491775165, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check model information返回正确的不支持响应", "uid": "e0ffa8c14943a28b", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488930105, "stop": 1754488930105, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "b3a0985d3b8f219c", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754486223471, "stop": 1754486237302, "duration": 13831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop playing", "uid": "f6c9439dfc63e147", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754484341682, "stop": 1754484356574, "duration": 14892}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Add the images and text on the screen to the note", "uid": "38a048b45445ca67", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488657242, "stop": 1754488657242, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger能正常执行", "uid": "c219d8630c012ecb", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488520947, "stop": 1754488520947, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试boost phone能正常执行", "uid": "6630dd6507ad295e", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486250363, "stop": 1754486264340, "duration": 13977}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试vedio call number by whatsapp能正常执行", "uid": "7eb514c65b1432a5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754493246248, "stop": 1754493246248, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试restart the phone能正常执行", "uid": "a25189f4c41cea26", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491366575, "stop": 1754491366575, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试why my charging is so slow能正常执行", "uid": "b9c6c8bfe1404e7", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754486196007, "stop": 1754486210302, "duration": 14295}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play video by youtube", "uid": "5c4e4711bd0d8a", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491162717, "stop": 1754491162717, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check status updates on whatsapp能正常执行", "uid": "ee7c652600fcf1ec", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484668536, "stop": 1754484682953, "duration": 14417}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set phantom v pen返回正确的不支持响应", "uid": "d8fd0397f7c4e6aa", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492287573, "stop": 1754492287573, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "3ca37dd9f27c6ecc", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490651448, "stop": 1754490651448, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take notes能正常执行", "uid": "787d73d161918578", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492972187, "stop": 1754492972187, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close aivana能正常执行", "uid": "b2ff483ff849f8d3", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483370298, "stop": 1754483405394, "duration": 35096}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check battery information返回正确的不支持响应", "uid": "6901c3d0d10496fa", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488793969, "stop": 1754488793969, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试redial", "uid": "936da91c6ba88125", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491264848, "stop": 1754491264848, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open countdown能正常执行", "uid": "308b4d5a081a98be", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754483809387, "stop": 1754483822953, "duration": 13566}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open bt", "uid": "38eadf37eecb0101", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486740453, "stop": 1754486754434, "duration": 13981}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close ella能正常执行", "uid": "8c0c6fe9d66425ac", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483418169, "stop": 1754483452773, "duration": 34604}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Voice setting page返回正确的不支持响应", "uid": "b883efebf4029015", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754493280335, "stop": 1754493280335, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试hi能正常执行", "uid": "5ea29cde835aec90", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485044995, "stop": 1754485060219, "duration": 15224}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "124a1f3e8edc5337", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489647368, "stop": 1754489647368, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "279ad67ed21598d8", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492355647, "stop": 1754492355647, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试appeler maman能正常执行", "uid": "dae71633949bd42a", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484520617, "stop": 1754484535025, "duration": 14408}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "883c5d89069451b1", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754487517979, "stop": 1754487531460, "duration": 13481}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试introduce yourself能正常执行", "uid": "191338734ff35e0", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485335076, "stop": 1754485350572, "duration": 15496}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen relay返回正确的不支持响应", "uid": "2df8c73485357aa8", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492424236, "stop": 1754492424236, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me write an thanks email能正常执行", "uid": "422547e10a7988ea", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490157148, "stop": 1754490157148, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pls open whatsapp", "uid": "c2c64f3d874f56de", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491230942, "stop": 1754491230942, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn down ring volume能正常执行", "uid": "f804cd13aef5ae6b", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754487750180, "stop": 1754487764062, "duration": 13882}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to battery usage返回正确的不支持响应", "uid": "ee7c08861f1d4369", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490402469, "stop": 1754490402469, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how to say i love you in french能正常执行", "uid": "e1e7bd584d2312c5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485218115, "stop": 1754485230850, "duration": 12735}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Help me write an email to make an appointment for a visit能正常执行", "uid": "bc549bf9de755daa", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485005516, "stop": 1754485032127, "duration": 26611}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open font family settings返回正确的不支持响应", "uid": "3eed261f9745ce86", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490788673, "stop": 1754490788673, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set app auto rotate返回正确的不支持响应", "uid": "9496f06be9b0368f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491502765, "stop": 1754491502765, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set font size返回正确的不支持响应", "uid": "c6e0954bd4fb0f5f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491981088, "stop": 1754491981088, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pls open the newest whatsapp activity", "uid": "7a6c918293c863b9", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488588832, "stop": 1754488588832, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set compatibility mode返回正确的不支持响应", "uid": "e4f39a55dd8c7a46", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491672752, "stop": 1754491672752, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me write an email能正常执行", "uid": "eeead481a4592f65", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490123137, "stop": 1754490123137, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试extend the image能正常执行", "uid": "9bb5d66bd0ffe1f8", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490021266, "stop": 1754490021266, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "81503ead67be5ff1", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491809607, "stop": 1754491809607, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's your name？能正常执行", "uid": "301099c31151442e", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486020217, "stop": 1754486033584, "duration": 13367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off flashlight能正常执行", "uid": "ead7ca339b50310b", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487777334, "stop": 1754487792679, "duration": 15345}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the flashlight能正常执行", "uid": "403e3a42990a4305", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754488023989, "stop": 1754488039854, "duration": 15865}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close phonemaster能正常执行", "uid": "4519632c93b2a5c3", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483513800, "stop": 1754483527132, "duration": 13332}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令", "uid": "712b502d18be3f07", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483774953, "stop": 1754483796471, "duration": 21518}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "423f6df0e82ce860", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485396679, "stop": 1754485411104, "duration": 14425}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试smart charge能正常执行", "uid": "5f63eb7a21dc6418", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754487018773, "stop": 1754487033255, "duration": 14482}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试my phone is too slow能正常执行", "uid": "498c625d54cac8e7", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483658953, "stop": 1754483673157, "duration": 14204}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i want to watch fireworks能正常执行", "uid": "a51394d164c1c753", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485305791, "stop": 1754485322166, "duration": 16375}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start running能正常执行", "uid": "a4c01f6014bd6c51", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492767858, "stop": 1754492767858, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set an alarm at 8 am", "uid": "b54d405883275b27", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484311536, "stop": 1754484328606, "duration": 17070}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set smart hub返回正确的不支持响应", "uid": "e1ee406f1611eb73", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492562170, "stop": 1754492562170, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试power saving能正常执行", "uid": "24ed300ebceea57f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486823934, "stop": 1754486848967, "duration": 25033}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试What's the weather like in Shanghai today能正常执行", "uid": "9c7363095b3d6839", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484486581, "stop": 1754484507641, "duration": 21060}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试the battery of the mobile phone is too low能正常执行", "uid": "64ce3e31c1cf3f3f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487711858, "stop": 1754487737322, "duration": 25464}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close performance mode返回正确的不支持响应", "uid": "b11d5a82657241fd", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489100666, "stop": 1754489100666, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试show scores between livepool and manchester city能正常执行", "uid": "142b184b00093dd8", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754485568848, "stop": 1754485583117, "duration": 14269}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway返回正确的不支持响应", "uid": "13d28d73850399d4", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490924820, "stop": 1754490924820, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试find a restaurant near me能正常执行", "uid": "bfdac69c756af839", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754488283667, "stop": 1754488314683, "duration": 31016}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switching charging speed能正常执行", "uid": "19f30a50229fa046", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492938029, "stop": 1754492938029, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set Battery Saver setting能正常执行", "uid": "a68a358b1eadc5f5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754486984685, "stop": 1754487005808, "duration": 21123}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable hide notifications返回正确的不支持响应", "uid": "649ac03314c798ad", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489339070, "stop": 1754489339070, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a screenshot能正常执行", "uid": "3e171de0d43a13", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754484369610, "stop": 1754484384634, "duration": 15024}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the address in thie image能正常执行", "uid": "26798eff8ed9e72", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490719855, "stop": 1754490719855, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what time is it now能正常执行", "uid": "9f14381c863cf0cc", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486046355, "stop": 1754486059787, "duration": 13432}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set cover screen apps返回正确的不支持响应", "uid": "1e0c6b7b2439c45e", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491707155, "stop": 1754491707155, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试cannot login in google email box能正常执行", "uid": "b686b089296f4044", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484641769, "stop": 1754484655486, "duration": 13717}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试phone boost能正常执行", "uid": "22b2ff639242008b", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484038030, "stop": 1754484052641, "duration": 14611}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试video call mom through whatsapp能正常执行", "uid": "b7779889ffdf936e", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754485838917, "stop": 1754485858507, "duration": 19590}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "d9281fe9adae9ba5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489168952, "stop": 1754489168952, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause screen recording能正常执行", "uid": "f8f79e89f6eee656", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487138274, "stop": 1754487155076, "duration": 16802}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "a760bb1d944c1018", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754488142734, "stop": 1754488156049, "duration": 13315}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play afro strut", "uid": "1c358ec780500fd7", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484065513, "stop": 1754484086571, "duration": 21058}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable brightness locking返回正确的不支持响应", "uid": "f44ba420ed70cb7f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489750061, "stop": 1754489750061, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable brightness locking返回正确的不支持响应", "uid": "1d9622fd407c2c96", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489271036, "stop": 1754489271036, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "e1bbda90aff93d55", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489987036, "stop": 1754489987036, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试search whatsapp for me能正常执行", "uid": "364c7273e7ac42ee", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491468681, "stop": 1754491468681, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop workout能正常执行", "uid": "69ec99d828b27505", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485648511, "stop": 1754485662811, "duration": 14300}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "b4f4ccf9053281c7", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492493249, "stop": 1754492493249, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "open clock", "uid": "c7f9d3989b7f3cb6", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483743447, "stop": 1754483761737, "duration": 18290}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "c21cde7c398161d9", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490583181, "stop": 1754490583181, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试summarize what i'm reading能正常执行", "uid": "ac9142d504d30e72", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485703002, "stop": 1754485716573, "duration": 13571}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试whatsapp能正常执行", "uid": "c2f84ab6863401ce", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488623011, "stop": 1754488623011, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试display the route go company", "uid": "f6b10dc3e1bba4eb", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754483623139, "stop": 1754483645506, "duration": 22367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check rear camera information能正常执行", "uid": "493d4b92d0c5f035", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489032587, "stop": 1754489032587, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "e9e9c48b1db33302", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754493177669, "stop": 1754493177669, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set ultra power saving返回正确的不支持响应", "uid": "762351f9affe7b3a", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492733532, "stop": 1754492733532, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试clear junk files命令", "uid": "6b30492325e06f55", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486309611, "stop": 1754486339344, "duration": 29733}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close flashlight能正常执行", "uid": "57ba6f253fe4a6a6", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486380541, "stop": 1754486396481, "duration": 15940}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试make a call能正常执行", "uid": "4515fe1422f6f89b", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485363318, "stop": 1754485383716, "duration": 20398}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open whatsapp", "uid": "57fe78f11b96e66d", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488486890, "stop": 1754488486890, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what is apec?能正常执行", "uid": "543154d5028ed6d", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485871627, "stop": 1754485888403, "duration": 16776}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set battery saver settings返回正确的不支持响应", "uid": "21b7d6cbcb6ed0b3", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491570571, "stop": 1754491570571, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "9fa3e1d3cfd691ca", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483871917, "stop": 1754483884764, "duration": 12847}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试delete the 8 o'clock alarm", "uid": "50935baf36d1199b", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483593381, "stop": 1754483610381, "duration": 17000}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试change (female/tone name) voice能正常执行", "uid": "1199297291d0d17", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488691332, "stop": 1754488691333, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off nfc能正常执行", "uid": "d04bf43a76908a56", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487832220, "stop": 1754487847428, "duration": 15208}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close bluetooth能正常执行", "uid": "c4fafbf029b57c35", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486352403, "stop": 1754486367731, "duration": 15328}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check contacts能正常执行", "uid": "443dca154c3756ff", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488862083, "stop": 1754488862083, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the screen record能正常执行", "uid": "86fa5010cef990cf", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486466855, "stop": 1754486483899, "duration": 17044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a joke能正常执行", "uid": "dc792c51ad45908a", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485729515, "stop": 1754485744736, "duration": 15221}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set split-screen apps返回正确的不支持响应", "uid": "1ae31f6c836ef647", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492664470, "stop": 1754492664470, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to performance mode返回正确的不支持响应", "uid": "ab6461e475482ce", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492870120, "stop": 1754492870120, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i wanna be rich能正常执行", "uid": "bfef13c8ba7b18c5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485243754, "stop": 1754485260914, "duration": 17160}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close power saving mode返回正确的不支持响应", "uid": "bbeba40801831c5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489134761, "stop": 1754489134761, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试tell me joke能正常执行", "uid": "9e625edf27bc68e4", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754493006073, "stop": 1754493006073, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set smart panel返回正确的不支持响应", "uid": "a79d5f2983e68bda", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492596508, "stop": 1754492596508, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试go home能正常执行", "uid": "29513ea621899e56", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490055356, "stop": 1754490055356, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试can you give me a coin能正常执行", "uid": "8120a0b0eacc9587", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484612243, "stop": 1754484628836, "duration": 16593}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop music能正常执行", "uid": "d6ce3a1657495012", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754485596139, "stop": 1754485609185, "duration": 13046}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger返回正确的不支持响应", "uid": "5a9de1902905bdb5", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490890686, "stop": 1754490890686, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable auto pickup返回正确的不支持响应", "uid": "b7f4b9e941495e2d", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489715755, "stop": 1754489715755, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "342df515163bc531", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492904126, "stop": 1754492904126, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试parking space能正常执行", "uid": "c701032e4d34f79", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490958969, "stop": 1754490958969, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the alarm at 8 am", "uid": "d4ef5f9069b81e12", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484456577, "stop": 1754484473700, "duration": 17123}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable running lock返回正确的不支持响应", "uid": "88005cc383fdea8f", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489441912, "stop": 1754489441912, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play the album", "uid": "84efe235da887661", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491094993, "stop": 1754491094993, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what time is it in London能正常执行", "uid": "b68ac7588583315d", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754493383221, "stop": 1754493383221, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "6c0551c8476663fd", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490350897, "stop": 1754490350897, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on driving mode返回正确的不支持响应", "uid": "93ff373023646cf6", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754493143901, "stop": 1754493143901, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play taylor swift‘s song love story", "uid": "27fab215254917dc", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491061081, "stop": 1754491061081, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "35d648522707fb53", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490326450, "stop": 1754490326450, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试restart my phone能正常执行", "uid": "3ffde2e2205e6490", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754491332571, "stop": 1754491332571, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close folax能正常执行", "uid": "eec3159f625e1454", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483465644, "stop": 1754483500756, "duration": 35112}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on do not disturb mode能正常执行", "uid": "9bbc7b793fb30846", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487887406, "stop": 1754487901646, "duration": 14240}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check contact能正常执行", "uid": "e27a3e330ed5d0d0", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488827949, "stop": 1754488827949, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set parallel windows返回正确的不支持响应", "uid": "131444e9a0aa3b4a", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492219671, "stop": 1754492219671, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play news", "uid": "10caeac7f5359c96", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485424127, "stop": 1754485440318, "duration": 16191}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable touch optimization返回正确的不支持响应", "uid": "9f960e8ed027c827", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754489476371, "stop": 1754489476371, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set alarm for 10 o'clock", "uid": "e4fcecfda38c470d", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486954332, "stop": 1754486972024, "duration": 17692}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a selfie能正常执行", "uid": "7c428f481eb64859", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754487668402, "stop": 1754487698993, "duration": 30591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway能正常执行", "uid": "2999695e96e957d0", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488554794, "stop": 1754488554794, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause song能正常执行", "uid": "fdf93e9017e25b95", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754484011846, "stop": 1754484024923, "duration": 13077}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me write an thanks letter能正常执行", "uid": "c1f0bcc3af9f38f6", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490191042, "stop": 1754490191042, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "24595911ea65e948", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754488896070, "stop": 1754488896070, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play political news", "uid": "eaf54f5c3ee7d63", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754485453466, "stop": 1754485469430, "duration": 15964}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "continue  screen recording能正常执行", "uid": "28d8f9a0379236cb", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754487168183, "stop": 1754487185107, "duration": 16924}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open camera能正常执行", "uid": "8619be41c629b8ca", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754483712861, "stop": 1754483730439, "duration": 17578}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set special function返回正确的不支持响应", "uid": "d5f2f0dc1b836163", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754492630353, "stop": 1754492630353, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试whats the weather today能正常执行", "uid": "d427b58b6fde626", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754486072773, "stop": 1754486090943, "duration": 18170}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on wifi能正常执行", "uid": "e06a7c81519a6ea3", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754488114007, "stop": 1754488129817, "duration": 15810}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play sun be song of jide chord", "uid": "1a3d1f5185aaa6ee", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "passed", "time": {"start": 1754484225076, "stop": 1754484244905, "duration": 19829}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试happy new year能正常执行", "uid": "64543549befa991e", "parentUid": "bab3217cc1cb74449375e490778d2d6d", "status": "failed", "time": {"start": 1754490089116, "stop": 1754490089116, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bab3217cc1cb74449375e490778d2d6d"}], "uid": "638bb6d97536d431f98e1d8183981f6c"}]}