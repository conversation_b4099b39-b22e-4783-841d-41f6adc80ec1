{"uid": "4b4757e66a1912dae1a509f688f20b0f", "name": "categories", "children": [{"name": "Product defects", "children": [{"name": "AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', '', '', '', \"8:34 Dialogue Explore Swipe down to view earlier chats 08:33 pm Hi, I'm <PERSON> I can answer your questions, summarize content, and provide creative inspiration. Refresh ChatGPT Limits Role as Therapist Can you give me some money? Expand character images display the route go company DeepSeek-R1 Feel free to ask me any questions…\", '[com.google.android.permissioncontroller页面内容] 8:33']'\nassert False", "children": [{"name": "测试display the route go company", "uid": "f6b10dc3e1bba4eb", "parentUid": "3adfcbb3412537c494abf3f1293493db", "status": "failed", "time": {"start": 1754483623139, "stop": 1754483645506, "duration": 22367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3adfcbb3412537c494abf3f1293493db"}, {"name": "AssertionError: 响应未包含期望内容: ['5 minutes', '10 minutes', '20 minutes']\nassert False", "children": [{"name": "测试open countdown能正常执行", "uid": "308b4d5a081a98be", "parentUid": "75d852aa63b861f204bde9067fb234d7", "status": "failed", "time": {"start": 1754483809387, "stop": 1754483822953, "duration": 13566}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "75d852aa63b861f204bde9067fb234d7"}, {"name": "AssertionError: 响应未包含期望内容: ['Ok']\nassert False", "children": [{"name": "测试pause song能正常执行", "uid": "fdf93e9017e25b95", "parentUid": "90b0f15d519cbcb48d2d2b1b19e1b7b1", "status": "failed", "time": {"start": 1754484011846, "stop": 1754484024923, "duration": 13077}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "90b0f15d519cbcb48d2d2b1b19e1b7b1"}, {"name": "AssertionError: 响应未包含期望内容: ['No music playing in the background']\nassert False", "children": [{"name": "测试resume music能正常执行", "uid": "59a2f1889b5d1733", "parentUid": "f85266d7fac1fe96c035e637cb30965b", "status": "failed", "time": {"start": 1754484285303, "stop": 1754484298395, "duration": 13092}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop playing", "uid": "f6c9439dfc63e147", "parentUid": "f85266d7fac1fe96c035e637cb30965b", "status": "failed", "time": {"start": 1754484341682, "stop": 1754484356574, "duration": 14892}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f85266d7fac1fe96c035e637cb30965b"}, {"name": "AssertionError: 响应未包含期望内容: ['Screenshot saved']\nassert False", "children": [{"name": "测试take a screenshot能正常执行", "uid": "3e171de0d43a13", "parentUid": "c154425a6a957d13d85cbd4afb773f28", "status": "failed", "time": {"start": 1754484369610, "stop": 1754484384634, "duration": 15024}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c154425a6a957d13d85cbd4afb773f28"}, {"name": "AssertionError: 响应未包含期望内容: ['Calling Mom...']\nassert False", "children": [{"name": "测试call mom through whatsapp能正常执行", "uid": "9a1de76db3282d62", "parentUid": "defbb428537d958d0deb475c25bce911", "status": "failed", "time": {"start": 1754484578227, "stop": 1754484599288, "duration": 21061}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试video call mom through whatsapp能正常执行", "uid": "b7779889ffdf936e", "parentUid": "defbb428537d958d0deb475c25bce911", "status": "failed", "time": {"start": 1754485838917, "stop": 1754485858507, "duration": 19590}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "defbb428537d958d0deb475c25bce911"}, {"name": "AssertionError: 响应未包含期望内容: ['Search Information']\nassert False", "children": [{"name": "测试could you please search an for me能正常执行", "uid": "f990be4b0bb6b336", "parentUid": "9340816f8142803a53cbe42467ec469f", "status": "failed", "time": {"start": 1754484863011, "stop": 1754484878667, "duration": 15656}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9340816f8142803a53cbe42467ec469f"}, {"name": "AssertionError: 响应未包含期望内容: ['WhatsApp is not installed yet']\nassert False", "children": [{"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "aa18187ae31ed8c0", "parentUid": "cd83a8ba9fa1be6164e1ab47cca1e5e3", "status": "failed", "time": {"start": 1754485511220, "stop": 1754485527264, "duration": 16044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cd83a8ba9fa1be6164e1ab47cca1e5e3"}, {"name": "AssertionError: 响应未包含期望内容: ['Premier League Goals Ranking']\nassert False", "children": [{"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "6dc578f8342571a5", "parentUid": "968491c31f7e73b0cd2b15d7bdd63075", "status": "failed", "time": {"start": 1754485540658, "stop": 1754485555831, "duration": 15173}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "968491c31f7e73b0cd2b15d7bdd63075"}, {"name": "AssertionError: 响应未包含期望内容: ['Liverpool']\nassert False", "children": [{"name": "测试show scores between livepool and manchester city能正常执行", "uid": "142b184b00093dd8", "parentUid": "fb3484a16d7c4fdd7d169e4e21d16df7", "status": "failed", "time": {"start": 1754485568848, "stop": 1754485583117, "duration": 14269}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb3484a16d7c4fdd7d169e4e21d16df7"}, {"name": "AssertionError: 响应未包含期望内容: ['No music playing in the background.']\nassert False", "children": [{"name": "测试stop music能正常执行", "uid": "d6ce3a1657495012", "parentUid": "9f05e17638dece8532a51576e374482f", "status": "failed", "time": {"start": 1754485596139, "stop": 1754485609185, "duration": 13046}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9f05e17638dece8532a51576e374482f"}, {"name": "AssertionError: 响应未包含期望内容: [\"Here's a joke for you\"]\nassert False", "children": [{"name": "测试tell me a joke能正常执行", "uid": "f4753f2d44bd35b5", "parentUid": "451b5a50bea9158be02a728e085db107", "status": "failed", "time": {"start": 1754485810136, "stop": 1754485825927, "duration": 15791}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "451b5a50bea9158be02a728e085db107"}, {"name": "AssertionError: 响应未包含期望内容: ['Chong Qing Shi is Fair today. The high is forecast as 37°C and the low as 28°C.']\nassert False", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "f9dec29930734510", "parentUid": "1ff649f1409abb91bfbcbcab690d9ccb", "status": "failed", "time": {"start": 1754485992367, "stop": 1754486007276, "duration": 14909}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1ff649f1409abb91bfbcbcab690d9ccb"}, {"name": "AssertionError: 响应未包含期望内容: ['These suggestions are for your reference']\nassert False", "children": [{"name": "测试why my charging is so slow能正常执行", "uid": "b9c6c8bfe1404e7", "parentUid": "83e6ce370469a50cbcd5886ffacbabdd", "status": "failed", "time": {"start": 1754486196007, "stop": 1754486210302, "duration": 14295}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83e6ce370469a50cbcd5886ffacbabdd"}, {"name": "AssertionError: 初始=51, 最终=74, 响应='['Adjustment the brightness to 50%', 'Brightness is at 50% now.', '', '']'\nassert 74 == 75", "children": [{"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "b3a0985d3b8f219c", "parentUid": "307213dde6a9b74020d0d324d9531e8f", "status": "failed", "time": {"start": 1754486223471, "stop": 1754486237302, "duration": 13831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "307213dde6a9b74020d0d324d9531e8f"}, {"name": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "children": [{"name": "测试check front camera information能正常执行", "uid": "1dd78894947cdfc0", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754486277352, "stop": 1754486296726, "duration": 19374}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set Battery Saver setting能正常执行", "uid": "a68a358b1eadc5f5", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754486984685, "stop": 1754487005808, "duration": 21123}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Barrage Notification能正常执行", "uid": "a4197bd04ef3d35d", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754487369682, "stop": 1754487385769, "duration": 16087}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to equilibrium mode能正常执行", "uid": "aa06e05449f42426", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754487428781, "stop": 1754487445077, "duration": 16296}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download basketball能正常执行", "uid": "2dcf43a24b3034da", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754488226197, "stop": 1754488240470, "duration": 14273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c7fcb7e700713ec9d3081e0b69d1f2e"}, {"name": "AssertionError: 初始=False, 最终=False, 响应='['countdown 5 min', 'Done!', '', '']'\nassert False", "children": [{"name": "测试countdown 5 min能正常执行", "uid": "56d2b9e1c2fc39f", "parentUid": "21500f7c3681e4d3dc63ad1c7b96e799", "status": "failed", "time": {"start": 1754486409251, "stop": 1754486426730, "duration": 17479}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "21500f7c3681e4d3dc63ad1c7b96e799"}, {"name": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "children": [{"name": "测试smart charge能正常执行", "uid": "5f63eb7a21dc6418", "parentUid": "40c8e511bd59d1b6469cd14c6e42d010", "status": "failed", "time": {"start": 1754487018773, "stop": 1754487033255, "duration": 14482}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch charging modes能正常执行", "uid": "7f6bcf1a52131d92", "parentUid": "40c8e511bd59d1b6469cd14c6e42d010", "status": "failed", "time": {"start": 1754487289081, "stop": 1754487302980, "duration": 13899}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Hyper Charge能正常执行", "uid": "948460c746878063", "parentUid": "40c8e511bd59d1b6469cd14c6e42d010", "status": "failed", "time": {"start": 1754487491529, "stop": 1754487504641, "duration": 13112}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "883c5d89069451b1", "parentUid": "40c8e511bd59d1b6469cd14c6e42d010", "status": "failed", "time": {"start": 1754487517979, "stop": 1754487531460, "duration": 13481}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to smart charge能正常执行", "uid": "8ad6893c56a2881f", "parentUid": "40c8e511bd59d1b6469cd14c6e42d010", "status": "failed", "time": {"start": 1754487571731, "stop": 1754487584828, "duration": 13097}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "40c8e511bd59d1b6469cd14c6e42d010"}, {"name": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "children": [{"name": "测试switch to flash notification能正常执行", "uid": "4d5b68cf4e8d17f8", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1754487458459, "stop": 1754487478499, "duration": 20040}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试where is the carlcare service outlet能正常执行", "uid": "d0d3620174008d52", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1754488169099, "stop": 1754488184592, "duration": 15493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a710013a126f24211d2305c197d8623"}, {"name": "AssertionError: 响应未包含期望内容: ['Mobile data is turned on now']\nassert False", "children": [{"name": "测试switched to data mode能正常执行", "uid": "4f285330353aa837", "parentUid": "e5c6867b760cd31e5f27b7fbd2a51931", "status": "failed", "time": {"start": 1754487597893, "stop": 1754487611697, "duration": 13804}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e5c6867b760cd31e5f27b7fbd2a51931"}, {"name": "AssertionError: 文件不存在！\nassert False", "children": [{"name": "测试take a photo能正常执行", "uid": "bf068ac0f1aaa3f3", "parentUid": "b16df34f724c90a3340ae49c743c6a99", "status": "failed", "time": {"start": 1754487624970, "stop": 1754487655439, "duration": 30469}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a selfie能正常执行", "uid": "7c428f481eb64859", "parentUid": "b16df34f724c90a3340ae49c743c6a99", "status": "failed", "time": {"start": 1754487668402, "stop": 1754487698993, "duration": 30591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b16df34f724c90a3340ae49c743c6a99"}, {"name": "AssertionError: 响应未包含期望内容: ['Ringtone volume has been turned down.']\nassert False", "children": [{"name": "测试turn down ring volume能正常执行", "uid": "f804cd13aef5ae6b", "parentUid": "e00fa4c632909e9c0bd1d6a3c0018ca4", "status": "failed", "time": {"start": 1754487750180, "stop": 1754487764062, "duration": 13882}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e00fa4c632909e9c0bd1d6a3c0018ca4"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D463AC0>.wait_for_page_load", "children": [{"name": "测试navigate from to red square能正常执行", "uid": "c06d0b280e1c66e9", "parentUid": "0885656cd1e38ce0bebbf753a55ce610", "status": "failed", "time": {"start": 1754488350360, "stop": 1754488350360, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0885656cd1e38ce0bebbf753a55ce610"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E3089D0>.wait_for_page_load", "children": [{"name": "测试navigate to shanghai disneyland能正常执行", "uid": "25bd4771bf8a6eb2", "parentUid": "0d94dd51170b2701b5227d1acaf41e12", "status": "failed", "time": {"start": 1754488383986, "stop": 1754488383986, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d94dd51170b2701b5227d1acaf41e12"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1776D0>.wait_for_page_load", "children": [{"name": "测试navigation to the lucky能正常执行", "uid": "cd10f690763a13fb", "parentUid": "8caba42d4e95c4ab8c4295621744556e", "status": "failed", "time": {"start": 1754488418472, "stop": 1754488418472, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8caba42d4e95c4ab8c4295621744556e"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D9BCEB0>.wait_for_page_load", "children": [{"name": "测试open facebook能正常执行", "uid": "c6e041786c62d65a", "parentUid": "226255cc4aa0f2e3249d815741746f8e", "status": "failed", "time": {"start": 1754488452897, "stop": 1754488452897, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "226255cc4aa0f2e3249d815741746f8e"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6E52A0>.wait_for_page_load", "children": [{"name": "测试open whatsapp", "uid": "57fe78f11b96e66d", "parentUid": "3bd450da8064ab03f819da5a33a721bc", "status": "failed", "time": {"start": 1754488486890, "stop": 1754488486890, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3bd450da8064ab03f819da5a33a721bc"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E309360>.wait_for_page_load", "children": [{"name": "测试order a burger能正常执行", "uid": "c219d8630c012ecb", "parentUid": "31a731449800d80dabe93d306cfc0925", "status": "failed", "time": {"start": 1754488520947, "stop": 1754488520947, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "31a731449800d80dabe93d306cfc0925"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D951A50>.wait_for_page_load", "children": [{"name": "测试order a takeaway能正常执行", "uid": "2999695e96e957d0", "parentUid": "771c60673906e103cd0a69c4341c6fc4", "status": "failed", "time": {"start": 1754488554794, "stop": 1754488554794, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "771c60673906e103cd0a69c4341c6fc4"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D174F40>.wait_for_page_load", "children": [{"name": "测试pls open the newest whatsapp activity", "uid": "7a6c918293c863b9", "parentUid": "0c0d0e71710ef4be2685bae68deb5269", "status": "failed", "time": {"start": 1754488588832, "stop": 1754488588832, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0c0d0e71710ef4be2685bae68deb5269"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D99D990>.wait_for_page_load", "children": [{"name": "测试whatsapp能正常执行", "uid": "c2f84ab6863401ce", "parentUid": "7d6703c3f4f135918984c0d939961cc5", "status": "failed", "time": {"start": 1754488623011, "stop": 1754488623011, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7d6703c3f4f135918984c0d939961cc5"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D492230>.wait_for_page_load", "children": [{"name": "测试Add the images and text on the screen to the note", "uid": "38a048b45445ca67", "parentUid": "b3a6711dda5299c2c591b1a8b7a7002b", "status": "failed", "time": {"start": 1754488657242, "stop": 1754488657242, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b3a6711dda5299c2c591b1a8b7a7002b"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065DB29750>.wait_for_page_load", "children": [{"name": "测试change (female/tone name) voice能正常执行", "uid": "1199297291d0d17", "parentUid": "88d750c8080428c383f4f91a8978866f", "status": "failed", "time": {"start": 1754488691332, "stop": 1754488691333, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "88d750c8080428c383f4f91a8978866f"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D874DF0>.wait_for_page_load", "children": [{"name": "测试change man voice能正常执行", "uid": "cbd155ed4bc579f", "parentUid": "f153bb84001c0290c80e7efe01c0905b", "status": "failed", "time": {"start": 1754488725683, "stop": 1754488725683, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f153bb84001c0290c80e7efe01c0905b"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D9918D0>.wait_for_page_load", "children": [{"name": "测试change your voice能正常执行", "uid": "3a8cee725f31ec40", "parentUid": "29c5ec944bb01541072019c1f617d6ce", "status": "failed", "time": {"start": 1754488759791, "stop": 1754488759791, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "29c5ec944bb01541072019c1f617d6ce"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1BDA20>.wait_for_page_load", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "6901c3d0d10496fa", "parentUid": "f7a6f3fdb81a587449b24db4a86e2f59", "status": "failed", "time": {"start": 1754488793969, "stop": 1754488793969, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f7a6f3fdb81a587449b24db4a86e2f59"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D9BDAB0>.wait_for_page_load", "children": [{"name": "测试check contact能正常执行", "uid": "e27a3e330ed5d0d0", "parentUid": "6a47f2a758aab82b86d5418cc526333f", "status": "failed", "time": {"start": 1754488827949, "stop": 1754488827949, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Voice setting page返回正确的不支持响应", "uid": "b883efebf4029015", "parentUid": "6a47f2a758aab82b86d5418cc526333f", "status": "failed", "time": {"start": 1754493280335, "stop": 1754493280335, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a47f2a758aab82b86d5418cc526333f"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065DB29BA0>.wait_for_page_load", "children": [{"name": "测试check contacts能正常执行", "uid": "443dca154c3756ff", "parentUid": "0370f9922d6ad45efd9055aafbe9b316", "status": "failed", "time": {"start": 1754488862083, "stop": 1754488862083, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0370f9922d6ad45efd9055aafbe9b316"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6E4040>.wait_for_page_load", "children": [{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "24595911ea65e948", "parentUid": "5b56bc6bc7058e6ae7b95291df2cb768", "status": "failed", "time": {"start": 1754488896070, "stop": 1754488896070, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5b56bc6bc7058e6ae7b95291df2cb768"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1E79D0>.wait_for_page_load", "children": [{"name": "测试check model information返回正确的不支持响应", "uid": "e0ffa8c14943a28b", "parentUid": "bb77a68d5505783c5ba675a1f8383266", "status": "failed", "time": {"start": 1754488930105, "stop": 1754488930105, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bb77a68d5505783c5ba675a1f8383266"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1770D0>.wait_for_page_load", "children": [{"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "c3973bb228d29704", "parentUid": "ce70b6690b6ff4034f024fac23655702", "status": "failed", "time": {"start": 1754488964292, "stop": 1754488964292, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ce70b6690b6ff4034f024fac23655702"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D874460>.wait_for_page_load", "children": [{"name": "测试check my to-do list能正常执行", "uid": "f6680ac844d1bbf7", "parentUid": "fda5120bfeeea33c5517240e8f2a615b", "status": "failed", "time": {"start": 1754488998385, "stop": 1754488998385, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fda5120bfeeea33c5517240e8f2a615b"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E3110C0>.wait_for_page_load", "children": [{"name": "测试check rear camera information能正常执行", "uid": "493d4b92d0c5f035", "parentUid": "a3068192a074925c8e15310ae5d8a198", "status": "failed", "time": {"start": 1754489032587, "stop": 1754489032587, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3068192a074925c8e15310ae5d8a198"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6A4F40>.wait_for_page_load", "children": [{"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "bdff75fea7ed5983", "parentUid": "27454e1d5dd9d468e72ab5eb7b069c3b", "status": "failed", "time": {"start": 1754489066550, "stop": 1754489066550, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "27454e1d5dd9d468e72ab5eb7b069c3b"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D524DF0>.wait_for_page_load", "children": [{"name": "测试close performance mode返回正确的不支持响应", "uid": "b11d5a82657241fd", "parentUid": "e5951f67f4a7515bdf921a1d61a01e05", "status": "failed", "time": {"start": 1754489100666, "stop": 1754489100666, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e5951f67f4a7515bdf921a1d61a01e05"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065C9CA080>.wait_for_page_load", "children": [{"name": "测试close power saving mode返回正确的不支持响应", "uid": "bbeba40801831c5", "parentUid": "4d61b546d991f64c1952119fdba8cd08", "status": "failed", "time": {"start": 1754489134761, "stop": 1754489134761, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4d61b546d991f64c1952119fdba8cd08"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1747F0>.wait_for_page_load", "children": [{"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "d9281fe9adae9ba5", "parentUid": "d44f1f47d5f1de9369691b8a4e78e5e8", "status": "failed", "time": {"start": 1754489168952, "stop": 1754489168952, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d44f1f47d5f1de9369691b8a4e78e5e8"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E313910>.wait_for_page_load", "children": [{"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "99fd73c7fe1b75de", "parentUid": "efcc27b218438b176b6feeb6dcdd711f", "status": "failed", "time": {"start": 1754489202925, "stop": 1754489202925, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "efcc27b218438b176b6feeb6dcdd711f"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065DB2A890>.wait_for_page_load", "children": [{"name": "测试disable auto pickup返回正确的不支持响应", "uid": "7f1ee703c6a94109", "parentUid": "ab4983f3e9ad18a576e7d0e8c7bbc8f3", "status": "failed", "time": {"start": 1754489236847, "stop": 1754489236847, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ab4983f3e9ad18a576e7d0e8c7bbc8f3"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6E4790>.wait_for_page_load", "children": [{"name": "测试disable brightness locking返回正确的不支持响应", "uid": "1d9622fd407c2c96", "parentUid": "20b2bbb819865ca842f4dbf96767a15a", "status": "failed", "time": {"start": 1754489271036, "stop": 1754489271036, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20b2bbb819865ca842f4dbf96767a15a"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1BC220>.wait_for_page_load", "children": [{"name": "测试disable call rejection返回正确的不支持响应", "uid": "61e0a20218e2b282", "parentUid": "23deb28d7d849851f48060d11e043e50", "status": "failed", "time": {"start": 1754489304958, "stop": 1754489304958, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "23deb28d7d849851f48060d11e043e50"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D5BAB30>.wait_for_page_load", "children": [{"name": "测试disable hide notifications返回正确的不支持响应", "uid": "649ac03314c798ad", "parentUid": "2233e0a793e6144c0d845ac4a53e616a", "status": "failed", "time": {"start": 1754489339070, "stop": 1754489339070, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2233e0a793e6144c0d845ac4a53e616a"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1E5510>.wait_for_page_load", "children": [{"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "3d61731e0bcd9778", "parentUid": "727576979a8ec5dfe7409706ac8561d5", "status": "failed", "time": {"start": 1754489373111, "stop": 1754489373111, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "727576979a8ec5dfe7409706ac8561d5"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D0B4700>.wait_for_page_load", "children": [{"name": "测试disable network enhancement返回正确的不支持响应", "uid": "21e4a005e4281783", "parentUid": "2efd4dee1e3ebe4926a72af627b7711a", "status": "failed", "time": {"start": 1754489407275, "stop": 1754489407275, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2efd4dee1e3ebe4926a72af627b7711a"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6A4220>.wait_for_page_load", "children": [{"name": "测试disable running lock返回正确的不支持响应", "uid": "88005cc383fdea8f", "parentUid": "2b61036a59d6da9718f37ee5486678fa", "status": "failed", "time": {"start": 1754489441912, "stop": 1754489441912, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2b61036a59d6da9718f37ee5486678fa"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D953FA0>.wait_for_page_load", "children": [{"name": "测试disable touch optimization返回正确的不支持响应", "uid": "9f960e8ed027c827", "parentUid": "0fde832dc85b5185a9f6bb1074201a7e", "status": "failed", "time": {"start": 1754489476371, "stop": 1754489476371, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0fde832dc85b5185a9f6bb1074201a7e"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D5AAF20>.wait_for_page_load", "children": [{"name": "测试disable unfreeze返回正确的不支持响应", "uid": "b0fa49b954710d82", "parentUid": "7f62324f4c5d474cf454438f6700e594", "status": "failed", "time": {"start": 1754489510717, "stop": 1754489510717, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7f62324f4c5d474cf454438f6700e594"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065C967A00>.wait_for_page_load", "children": [{"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "cc78c9c9b8318173", "parentUid": "1b035a44e44161bf125e4dc8b32a5891", "status": "failed", "time": {"start": 1754489545030, "stop": 1754489545030, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b035a44e44161bf125e4dc8b32a5891"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D5540D0>.wait_for_page_load", "children": [{"name": "测试download basketball返回正确的不支持响应", "uid": "4634ae81442a4912", "parentUid": "fe5676c524ccf303acda15ad1c9f0338", "status": "failed", "time": {"start": 1754489578969, "stop": 1754489578969, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fe5676c524ccf303acda15ad1c9f0338"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D876E00>.wait_for_page_load", "children": [{"name": "测试driving mode返回正确的不支持响应", "uid": "4d07f0c6c7ca376f", "parentUid": "ebb180ea449fada49a622fd24eb4d2dc", "status": "failed", "time": {"start": 1754489613176, "stop": 1754489613176, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ebb180ea449fada49a622fd24eb4d2dc"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065DB2A8F0>.wait_for_page_load", "children": [{"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "124a1f3e8edc5337", "parentUid": "b8d83488759e51a00ffb9bfe7044bff1", "status": "failed", "time": {"start": 1754489647368, "stop": 1754489647368, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b8d83488759e51a00ffb9bfe7044bff1"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E30A9B0>.wait_for_page_load", "children": [{"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "18f399f8e5fa9ce7", "parentUid": "72b2c80ffc3c3688a3ddd01fed05e070", "status": "failed", "time": {"start": 1754489681401, "stop": 1754489681401, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "72b2c80ffc3c3688a3ddd01fed05e070"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6A6DD0>.wait_for_page_load", "children": [{"name": "测试enable auto pickup返回正确的不支持响应", "uid": "b7f4b9e941495e2d", "parentUid": "43f35096ae2fa8003cdc41c8dcb9f2bb", "status": "failed", "time": {"start": 1754489715755, "stop": 1754489715755, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43f35096ae2fa8003cdc41c8dcb9f2bb"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065C9CA500>.wait_for_page_load", "children": [{"name": "测试enable brightness locking返回正确的不支持响应", "uid": "f44ba420ed70cb7f", "parentUid": "f8d6646705c7683f71f7d282648d7ae4", "status": "failed", "time": {"start": 1754489750061, "stop": 1754489750061, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f8d6646705c7683f71f7d282648d7ae4"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1F7D60>.wait_for_page_load", "children": [{"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "c7e38f605f9066ee", "parentUid": "891e4e8f65b382eb5f9b77d9a79695ef", "status": "failed", "time": {"start": 1754489783936, "stop": 1754489783936, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "891e4e8f65b382eb5f9b77d9a79695ef"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065CC69690>.wait_for_page_load", "children": [{"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "f56294cf89c411bc", "parentUid": "a612f003b24f94e0340c6a59abaf2c7c", "status": "failed", "time": {"start": 1754489817654, "stop": 1754489817654, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a612f003b24f94e0340c6a59abaf2c7c"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1F4B50>.wait_for_page_load", "children": [{"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "bf2f4d9af9c68c1c", "parentUid": "911f287824cca7add2035d9bf2e6ad9b", "status": "failed", "time": {"start": 1754489851594, "stop": 1754489851594, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "911f287824cca7add2035d9bf2e6ad9b"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D5BBD00>.wait_for_page_load", "children": [{"name": "测试enable running lock返回正确的不支持响应", "uid": "65dc203fdb610f69", "parentUid": "392095a3a4f115bc41fbd6e7a898cbf7", "status": "failed", "time": {"start": 1754489885466, "stop": 1754489885466, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "392095a3a4f115bc41fbd6e7a898cbf7"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E30A1A0>.wait_for_page_load", "children": [{"name": "测试enable touch optimization返回正确的不支持响应", "uid": "cd66040bc232be03", "parentUid": "33ca1cd81b87a9e6111df28591e28262", "status": "failed", "time": {"start": 1754489919354, "stop": 1754489919354, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "33ca1cd81b87a9e6111df28591e28262"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D492E00>.wait_for_page_load", "children": [{"name": "测试enable unfreeze返回正确的不支持响应", "uid": "3ad176dfb601300e", "parentUid": "dc311e6741b2894e98a599fe69ad57f9", "status": "failed", "time": {"start": 1754489953337, "stop": 1754489953337, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dc311e6741b2894e98a599fe69ad57f9"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D526500>.wait_for_page_load", "children": [{"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "e1bbda90aff93d55", "parentUid": "ec5f12d755b2f47c7d2bded4cd374774", "status": "failed", "time": {"start": 1754489987036, "stop": 1754489987036, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ec5f12d755b2f47c7d2bded4cd374774"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EACA200>.wait_for_page_load", "children": [{"name": "测试extend the image能正常执行", "uid": "9bb5d66bd0ffe1f8", "parentUid": "4e5d035535b91674f0b1bd13c6c9d447", "status": "failed", "time": {"start": 1754490021266, "stop": 1754490021266, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4e5d035535b91674f0b1bd13c6c9d447"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1E7160>.wait_for_page_load", "children": [{"name": "测试go home能正常执行", "uid": "29513ea621899e56", "parentUid": "56af3af65d5adee338111383e1a9b3ec", "status": "failed", "time": {"start": 1754490055356, "stop": 1754490055356, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "56af3af65d5adee338111383e1a9b3ec"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E312440>.wait_for_page_load", "children": [{"name": "测试happy new year能正常执行", "uid": "64543549befa991e", "parentUid": "2c7bc5a6b451285b992654a58b5b09e0", "status": "failed", "time": {"start": 1754490089116, "stop": 1754490089116, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2c7bc5a6b451285b992654a58b5b09e0"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D5556C0>.wait_for_page_load", "children": [{"name": "测试help me write an email能正常执行", "uid": "eeead481a4592f65", "parentUid": "0e56e70257d70f98c0bbf2b885ffe371", "status": "failed", "time": {"start": 1754490123137, "stop": 1754490123137, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0e56e70257d70f98c0bbf2b885ffe371"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065C948C10>.wait_for_page_load", "children": [{"name": "测试help me write an thanks email能正常执行", "uid": "422547e10a7988ea", "parentUid": "2c99f2be6f975fdb24317f8678e6456f", "status": "failed", "time": {"start": 1754490157148, "stop": 1754490157148, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2c99f2be6f975fdb24317f8678e6456f"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D555810>.wait_for_page_load", "children": [{"name": "测试help me write an thanks letter能正常执行", "uid": "c1f0bcc3af9f38f6", "parentUid": "8981f1e690d2339cfc25363c482db5e6", "status": "failed", "time": {"start": 1754490191042, "stop": 1754490191042, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8981f1e690d2339cfc25363c482db5e6"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6A6CB0>.wait_for_page_load", "children": [{"name": "测试how to set screenshots返回正确的不支持响应", "uid": "6bb6c7260139caa2", "parentUid": "5320fd6872c73cf0d062838dcb200b48", "status": "failed", "time": {"start": 1754490225039, "stop": 1754490225039, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5320fd6872c73cf0d062838dcb200b48"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1E7580>.wait_for_page_load", "children": [{"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "872f2d0df76fd076", "parentUid": "8aa4e890635ff56c41f444e0d927c7b0", "status": "failed", "time": {"start": 1754490259190, "stop": 1754490259190, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8aa4e890635ff56c41f444e0d927c7b0"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065C9CAA40>.wait_for_page_load", "children": [{"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "20fe2dd2bfdd7b37", "parentUid": "665935f5792e25baccaf441cab7b91e8", "status": "failed", "time": {"start": 1754490292806, "stop": 1754490292806, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "665935f5792e25baccaf441cab7b91e8"}, {"name": "Failed: Ella应用启动异常: Ella应用启动失败\nassert False\n +  where False = start_app()\n +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E3099C0>.start_app", "children": [{"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "35d648522707fb53", "parentUid": "0b54b6d65f419bd3a3ca705b33f162dd", "status": "failed", "time": {"start": 1754490326450, "stop": 1754490326450, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0b54b6d65f419bd3a3ca705b33f162dd"}, {"name": "Failed: Ella应用启动异常: Ella应用启动失败\nassert False\n +  where False = start_app()\n +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D525720>.start_app", "children": [{"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "6c0551c8476663fd", "parentUid": "98aa3eb225fd58219d8b16444e845c74", "status": "failed", "time": {"start": 1754490350897, "stop": 1754490350897, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98aa3eb225fd58219d8b16444e845c74"}, {"name": "Failed: Ella应用启动异常: Ella应用启动失败\nassert False\n +  where False = start_app()\n +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D5B8F40>.start_app", "children": [{"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "1736a9777edb77af", "parentUid": "d94d61056e8d2aefa07cc8ecfea9b0d7", "status": "failed", "time": {"start": 1754490376488, "stop": 1754490376488, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d94d61056e8d2aefa07cc8ecfea9b0d7"}, {"name": "Failed: Ella应用启动异常: Ella应用启动失败\nassert False\n +  where False = start_app()\n +    where start_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D0B44F0>.start_app", "children": [{"name": "测试jump to battery usage返回正确的不支持响应", "uid": "ee7c08861f1d4369", "parentUid": "89205fe6c6249bf416b19c66c5970a9c", "status": "failed", "time": {"start": 1754490402469, "stop": 1754490402469, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "89205fe6c6249bf416b19c66c5970a9c"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E312D70>.wait_for_page_load", "children": [{"name": "测试jump to call notifications返回正确的不支持响应", "uid": "53d43e24245acf0a", "parentUid": "bb52cce09beeca965ac96319bff72638", "status": "failed", "time": {"start": 1754490428166, "stop": 1754490428166, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bb52cce09beeca965ac96319bff72638"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065CC7CEB0>.wait_for_page_load", "children": [{"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "5f1d181210935ac4", "parentUid": "ff5a4da4f1a89ce9971d2bf1f6a930aa", "status": "failed", "time": {"start": 1754490474108, "stop": 1754490474108, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ff5a4da4f1a89ce9971d2bf1f6a930aa"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E310D30>.wait_for_page_load", "children": [{"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "5f1cafc0f7162a3b", "parentUid": "d1b6a1a3c52d7eecb129c5be5a1c4333", "status": "failed", "time": {"start": 1754490512810, "stop": 1754490512810, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d1b6a1a3c52d7eecb129c5be5a1c4333"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1E5B10>.wait_for_page_load", "children": [{"name": "测试jump to nfc settings", "uid": "29843c65bf2d03c6", "parentUid": "629061bafa5adb66cace7615202a0661", "status": "failed", "time": {"start": 1754490548481, "stop": 1754490548481, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "629061bafa5adb66cace7615202a0661"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D4920B0>.wait_for_page_load", "children": [{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "c21cde7c398161d9", "parentUid": "29cf1cdd563cd44ba77c74338b81d89e", "status": "failed", "time": {"start": 1754490583181, "stop": 1754490583181, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "29cf1cdd563cd44ba77c74338b81d89e"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6A65C0>.wait_for_page_load", "children": [{"name": "测试kill whatsapp能正常执行", "uid": "65c217eaa39ab993", "parentUid": "05f5cc0736bcc025fd570dcf23fdda71", "status": "failed", "time": {"start": 1754490617576, "stop": 1754490617576, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "05f5cc0736bcc025fd570dcf23fdda71"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D9BDD20>.wait_for_page_load", "children": [{"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "3ca37dd9f27c6ecc", "parentUid": "6a6a66aa6c72a53bd9690c63979644e5", "status": "failed", "time": {"start": 1754490651448, "stop": 1754490651448, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a6a66aa6c72a53bd9690c63979644e5"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E30B130>.wait_for_page_load", "children": [{"name": "测试more settings返回正确的不支持响应", "uid": "97abb069c96ff50c", "parentUid": "7b161d5c952654898f3d8639a2c61cb0", "status": "failed", "time": {"start": 1754490685463, "stop": 1754490685463, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b161d5c952654898f3d8639a2c61cb0"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065C9CB1F0>.wait_for_page_load", "children": [{"name": "测试navigation to the address in thie image能正常执行", "uid": "26798eff8ed9e72", "parentUid": "9e60c7423299b167c6e6062f2854a6bf", "status": "failed", "time": {"start": 1754490719855, "stop": 1754490719855, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e60c7423299b167c6e6062f2854a6bf"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB63640>.wait_for_page_load", "children": [{"name": "测试navigation to the first address in the image能正常执行", "uid": "18f772aa522a4c5", "parentUid": "7be5565b5e4768353faa27711cda9c2e", "status": "failed", "time": {"start": 1754490754497, "stop": 1754490754497, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7be5565b5e4768353faa27711cda9c2e"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065DB2B490>.wait_for_page_load", "children": [{"name": "测试open font family settings返回正确的不支持响应", "uid": "3eed261f9745ce86", "parentUid": "cf59fd0a9228621c0f12c14050ec3842", "status": "failed", "time": {"start": 1754490788673, "stop": 1754490788673, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cf59fd0a9228621c0f12c14050ec3842"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065CC6B220>.wait_for_page_load", "children": [{"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "65e88368633e1d8a", "parentUid": "593af1ca44d8b0e858da48bdf71f2769", "status": "failed", "time": {"start": 1754490822706, "stop": 1754490822706, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "593af1ca44d8b0e858da48bdf71f2769"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065CC6BCD0>.wait_for_page_load", "children": [{"name": "测试open whatsapp", "uid": "720a5005d504c9d4", "parentUid": "5d6ddaaa47866104fa6eeea80a0aa5a9", "status": "failed", "time": {"start": 1754490856630, "stop": 1754490856630, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5d6ddaaa47866104fa6eeea80a0aa5a9"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065CAF3190>.wait_for_page_load", "children": [{"name": "测试order a burger返回正确的不支持响应", "uid": "5a9de1902905bdb5", "parentUid": "24ba96a524bc6744776d60a347957932", "status": "failed", "time": {"start": 1754490890686, "stop": 1754490890686, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "24ba96a524bc6744776d60a347957932"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EBBF6A0>.wait_for_page_load", "children": [{"name": "测试order a takeaway返回正确的不支持响应", "uid": "13d28d73850399d4", "parentUid": "98f5a521c31384b9a08ae0397f5c0a23", "status": "failed", "time": {"start": 1754490924820, "stop": 1754490924820, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98f5a521c31384b9a08ae0397f5c0a23"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6E57E0>.wait_for_page_load", "children": [{"name": "测试parking space能正常执行", "uid": "c701032e4d34f79", "parentUid": "2fdb31606c46f5b38ad339669d1f8ae3", "status": "failed", "time": {"start": 1754490958969, "stop": 1754490958969, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2fdb31606c46f5b38ad339669d1f8ae3"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6A7940>.wait_for_page_load", "children": [{"name": "测试play football video by youtube", "uid": "9d20fb01702a451", "parentUid": "6c9098d8acc0fc3720e9f8b9d2a441c1", "status": "failed", "time": {"start": 1754490993122, "stop": 1754490993122, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6c9098d8acc0fc3720e9f8b9d2a441c1"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E312710>.wait_for_page_load", "children": [{"name": "测试play love sotry", "uid": "960f945a0e52aa89", "parentUid": "7c81258cfa57d96e20cacc86136bd851", "status": "failed", "time": {"start": 1754491027353, "stop": 1754491027353, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c81258cfa57d96e20cacc86136bd851"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D463070>.wait_for_page_load", "children": [{"name": "测试play taylor swift‘s song love story", "uid": "27fab215254917dc", "parentUid": "c6119b94fbd01c7ea1b5d6fc3beb86a1", "status": "failed", "time": {"start": 1754491061081, "stop": 1754491061081, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c6119b94fbd01c7ea1b5d6fc3beb86a1"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E308AF0>.wait_for_page_load", "children": [{"name": "测试play the album", "uid": "84efe235da887661", "parentUid": "3daf259d416602fa79f22403bc2d9a82", "status": "failed", "time": {"start": 1754491094993, "stop": 1754491094993, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3daf259d416602fa79f22403bc2d9a82"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1BE1D0>.wait_for_page_load", "children": [{"name": "测试play video", "uid": "95b3160676d4ff72", "parentUid": "d9fb99209b01eb8496ec5ad90bab4226", "status": "failed", "time": {"start": 1754491128564, "stop": 1754491128564, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9fb99209b01eb8496ec5ad90bab4226"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D0B4E50>.wait_for_page_load", "children": [{"name": "测试play video by youtube", "uid": "5c4e4711bd0d8a", "parentUid": "15fd3dfdba211694c96dfcf7e9f03ed1", "status": "failed", "time": {"start": 1754491162717, "stop": 1754491162717, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "15fd3dfdba211694c96dfcf7e9f03ed1"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D9BC5E0>.wait_for_page_load", "children": [{"name": "测试please show me where i am能正常执行", "uid": "971d76c9403829e0", "parentUid": "9022fee637ec80ca2895603802416dda", "status": "failed", "time": {"start": 1754491196950, "stop": 1754491196950, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9022fee637ec80ca2895603802416dda"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D0B69B0>.wait_for_page_load", "children": [{"name": "测试pls open whatsapp", "uid": "c2c64f3d874f56de", "parentUid": "63389b86fa7b6373c3b7325338e14932", "status": "failed", "time": {"start": 1754491230942, "stop": 1754491230942, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "63389b86fa7b6373c3b7325338e14932"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB895D0>.wait_for_page_load", "children": [{"name": "测试redial", "uid": "936da91c6ba88125", "parentUid": "658344c26691a025605c72f08140f008", "status": "failed", "time": {"start": 1754491264848, "stop": 1754491264848, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "658344c26691a025605c72f08140f008"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D460DF0>.wait_for_page_load", "children": [{"name": "测试reset phone返回正确的不支持响应", "uid": "6c2113f7cf07a5f1", "parentUid": "819812a55044067171905432d8e6095e", "status": "failed", "time": {"start": 1754491298781, "stop": 1754491298781, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "819812a55044067171905432d8e6095e"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E30AF20>.wait_for_page_load", "children": [{"name": "测试restart my phone能正常执行", "uid": "3ffde2e2205e6490", "parentUid": "b46ecfaeaebd886b9cf1095051462521", "status": "failed", "time": {"start": 1754491332571, "stop": 1754491332571, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b46ecfaeaebd886b9cf1095051462521"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EBBDC90>.wait_for_page_load", "children": [{"name": "测试restart the phone能正常执行", "uid": "a25189f4c41cea26", "parentUid": "c1ccf46dc03035a69f25dd653a1e40e4", "status": "failed", "time": {"start": 1754491366575, "stop": 1754491366575, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c1ccf46dc03035a69f25dd653a1e40e4"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D9508E0>.wait_for_page_load", "children": [{"name": "测试Search for addresses on the screen能正常执行", "uid": "1ee5d8b15798efff", "parentUid": "ae2cd8267937769b820e2d5fd0f5a43a", "status": "failed", "time": {"start": 1754491400647, "stop": 1754491400647, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ae2cd8267937769b820e2d5fd0f5a43a"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1E5210>.wait_for_page_load", "children": [{"name": "测试search the address in the image能正常执行", "uid": "62c368eb30849dec", "parentUid": "77d6caaaab4f3e97a97b5b7d17d1cb46", "status": "failed", "time": {"start": 1754491434814, "stop": 1754491434814, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "77d6caaaab4f3e97a97b5b7d17d1cb46"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D525720>.wait_for_page_load", "children": [{"name": "测试search whatsapp for me能正常执行", "uid": "364c7273e7ac42ee", "parentUid": "6f611d736502d7fcb745fffcc4681b7e", "status": "failed", "time": {"start": 1754491468681, "stop": 1754491468681, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6f611d736502d7fcb745fffcc4681b7e"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065CC6A1D0>.wait_for_page_load", "children": [{"name": "测试set app auto rotate返回正确的不支持响应", "uid": "9496f06be9b0368f", "parentUid": "df76813888e42380850a0c4c12a37ad5", "status": "failed", "time": {"start": 1754491502765, "stop": 1754491502765, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df76813888e42380850a0c4c12a37ad5"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6E64D0>.wait_for_page_load", "children": [{"name": "测试set app notifications返回正确的不支持响应", "uid": "9f1b567357aa63da", "parentUid": "b302c7568ac2014e2f991e3ae9dd0d64", "status": "failed", "time": {"start": 1754491536654, "stop": 1754491536654, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b302c7568ac2014e2f991e3ae9dd0d64"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D990D00>.wait_for_page_load", "children": [{"name": "测试set battery saver settings返回正确的不支持响应", "uid": "21b7d6cbcb6ed0b3", "parentUid": "114b4ae95ba9219d94b66efabd972ea6", "status": "failed", "time": {"start": 1754491570571, "stop": 1754491570571, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "114b4ae95ba9219d94b66efabd972ea6"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E310B50>.wait_for_page_load", "children": [{"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "d000d74ce17fdd03", "parentUid": "a7f5f2ce841f439f25c6fa9d283d315d", "status": "failed", "time": {"start": 1754491604681, "stop": 1754491604681, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a7f5f2ce841f439f25c6fa9d283d315d"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065DB28070>.wait_for_page_load", "children": [{"name": "测试set color style返回正确的不支持响应", "uid": "2d060a6b4f115242", "parentUid": "27f24e6a9a5c7092796b008be9f3304b", "status": "failed", "time": {"start": 1754491638468, "stop": 1754491638468, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "27f24e6a9a5c7092796b008be9f3304b"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB717E0>.wait_for_page_load", "children": [{"name": "测试set compatibility mode返回正确的不支持响应", "uid": "e4f39a55dd8c7a46", "parentUid": "942b3de87d53e71a4c9698e8701d76bc", "status": "failed", "time": {"start": 1754491672752, "stop": 1754491672752, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "942b3de87d53e71a4c9698e8701d76bc"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EAC9CF0>.wait_for_page_load", "children": [{"name": "测试set cover screen apps返回正确的不支持响应", "uid": "1e0c6b7b2439c45e", "parentUid": "c8b4aefde2dfba2797e6f0eb312c2620", "status": "failed", "time": {"start": 1754491707155, "stop": 1754491707155, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c8b4aefde2dfba2797e6f0eb312c2620"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1E5BA0>.wait_for_page_load", "children": [{"name": "测试set customized cover screen返回正确的不支持响应", "uid": "b7758b8752b969b0", "parentUid": "db5ba304f971d265b5780147c2cadef9", "status": "failed", "time": {"start": 1754491741363, "stop": 1754491741363, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "db5ba304f971d265b5780147c2cadef9"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E30BDC0>.wait_for_page_load", "children": [{"name": "测试set date & time返回正确的不支持响应", "uid": "1302051b604a5885", "parentUid": "349a2f962dd836ea6c1f8a5f9369e174", "status": "failed", "time": {"start": 1754491775165, "stop": 1754491775165, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "349a2f962dd836ea6c1f8a5f9369e174"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EBBC460>.wait_for_page_load", "children": [{"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "81503ead67be5ff1", "parentUid": "4c7d1d768009d75679d338b569fbb3be", "status": "failed", "time": {"start": 1754491809607, "stop": 1754491809607, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c7d1d768009d75679d338b569fbb3be"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6E7F40>.wait_for_page_load", "children": [{"name": "测试set flex-still mode返回正确的不支持响应", "uid": "281d4eee43b52921", "parentUid": "bffa9926d83cf12ab2ddf4102483e3d7", "status": "failed", "time": {"start": 1754491843808, "stop": 1754491843808, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bffa9926d83cf12ab2ddf4102483e3d7"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB81B40>.wait_for_page_load", "children": [{"name": "测试set flip case feature返回正确的不支持响应", "uid": "48fec51a8fe153c0", "parentUid": "6359b46cb4647099e2f80e3dbd72a33a", "status": "failed", "time": {"start": 1754491877618, "stop": 1754491877618, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6359b46cb4647099e2f80e3dbd72a33a"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D175F00>.wait_for_page_load", "children": [{"name": "测试set floating windows返回正确的不支持响应", "uid": "a45dfd46ac1039a0", "parentUid": "e19ca3bd81b0e5bd62180da7f22a86a7", "status": "failed", "time": {"start": 1754491911964, "stop": 1754491911964, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e19ca3bd81b0e5bd62180da7f22a86a7"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EACA380>.wait_for_page_load", "children": [{"name": "测试set folding screen zone返回正确的不支持响应", "uid": "3139a90de321bf94", "parentUid": "ffa9a5097678e0ca671ee89b94927887", "status": "failed", "time": {"start": 1754491946633, "stop": 1754491946633, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ffa9a5097678e0ca671ee89b94927887"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D461AE0>.wait_for_page_load", "children": [{"name": "测试set font size返回正确的不支持响应", "uid": "c6e0954bd4fb0f5f", "parentUid": "278e627ca950e9451605db0f4fc40d75", "status": "failed", "time": {"start": 1754491981088, "stop": 1754491981088, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "278e627ca950e9451605db0f4fc40d75"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6E5330>.wait_for_page_load", "children": [{"name": "测试set gesture navigation返回正确的不支持响应", "uid": "7fbeb58f6ba7a50", "parentUid": "c069e4e6f45302e458a2bc2e5e31cc5d", "status": "failed", "time": {"start": 1754492015552, "stop": 1754492015552, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c069e4e6f45302e458a2bc2e5e31cc5d"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E30A7D0>.wait_for_page_load", "children": [{"name": "测试set languages返回正确的不支持响应", "uid": "75a4fdcde784a72f", "parentUid": "c332f94c205daddaa5eb642b9ef7eb2c", "status": "failed", "time": {"start": 1754492049446, "stop": 1754492049446, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c332f94c205daddaa5eb642b9ef7eb2c"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D527190>.wait_for_page_load", "children": [{"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "cee648585821b0fe", "parentUid": "175d08ba128981f5665e6e3ab0394d45", "status": "failed", "time": {"start": 1754492083595, "stop": 1754492083595, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "175d08ba128981f5665e6e3ab0394d45"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB718D0>.wait_for_page_load", "children": [{"name": "测试set my fonts返回正确的不支持响应", "uid": "ea9bd37543fd3683", "parentUid": "9a2a811208880d6bfc28f339aaab528f", "status": "failed", "time": {"start": 1754492118018, "stop": 1754492118019, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9a2a811208880d6bfc28f339aaab528f"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D9510C0>.wait_for_page_load", "children": [{"name": "测试set my themes返回正确的不支持响应", "uid": "3004ceb745527485", "parentUid": "ad3d6d4389b132a4774b2a5bbeb5199b", "status": "failed", "time": {"start": 1754492151822, "stop": 1754492151822, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ad3d6d4389b132a4774b2a5bbeb5199b"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065DB29A20>.wait_for_page_load", "children": [{"name": "测试set off a firework能正常执行", "uid": "5efaff886e3fa80b", "parentUid": "9ab88b6dce385e0a8e2581c667c1b4ff", "status": "failed", "time": {"start": 1754492185666, "stop": 1754492185666, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9ab88b6dce385e0a8e2581c667c1b4ff"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB8B9A0>.wait_for_page_load", "children": [{"name": "测试set parallel windows返回正确的不支持响应", "uid": "131444e9a0aa3b4a", "parentUid": "ed499972f2dcb1538852ab6b46d09fd2", "status": "failed", "time": {"start": 1754492219671, "stop": 1754492219671, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ed499972f2dcb1538852ab6b46d09fd2"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065C9CB850>.wait_for_page_load", "children": [{"name": "测试set personal hotspot返回正确的不支持响应", "uid": "a0c877bf5b68dcb1", "parentUid": "fc5598bf83698ab5bba2a7b3f6569198", "status": "failed", "time": {"start": 1754492253715, "stop": 1754492253715, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fc5598bf83698ab5bba2a7b3f6569198"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065CB54130>.wait_for_page_load", "children": [{"name": "测试set phantom v pen返回正确的不支持响应", "uid": "d8fd0397f7c4e6aa", "parentUid": "8a88cdca40a51677729e66e23e918b61", "status": "failed", "time": {"start": 1754492287573, "stop": 1754492287573, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8a88cdca40a51677729e66e23e918b61"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB61D50>.wait_for_page_load", "children": [{"name": "测试set phone number返回正确的不支持响应", "uid": "36e3d9257c05a679", "parentUid": "03c661f42220f337b51696b530afb558", "status": "failed", "time": {"start": 1754492321593, "stop": 1754492321593, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "03c661f42220f337b51696b530afb558"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065ECCE350>.wait_for_page_load", "children": [{"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "279ad67ed21598d8", "parentUid": "a2177a98ea4341cf7e521024e1dd7e86", "status": "failed", "time": {"start": 1754492355647, "stop": 1754492355647, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a2177a98ea4341cf7e521024e1dd7e86"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EACBC40>.wait_for_page_load", "children": [{"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "b5759ffb8039a256", "parentUid": "c01cbfd09c4b9092d3cce470673db300", "status": "failed", "time": {"start": 1754492389562, "stop": 1754492389562, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c01cbfd09c4b9092d3cce470673db300"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D526EC0>.wait_for_page_load", "children": [{"name": "测试set screen relay返回正确的不支持响应", "uid": "2df8c73485357aa8", "parentUid": "b6444642c29a8f08541afedf216a9bbc", "status": "failed", "time": {"start": 1754492424236, "stop": 1754492424236, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start running能正常执行", "uid": "a4c01f6014bd6c51", "parentUid": "b6444642c29a8f08541afedf216a9bbc", "status": "failed", "time": {"start": 1754492767858, "stop": 1754492767858, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b6444642c29a8f08541afedf216a9bbc"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E30A770>.wait_for_page_load", "children": [{"name": "测试set screen timeout返回正确的不支持响应", "uid": "6af11f61922afd2f", "parentUid": "8a6ee13ab0e739c0510eeda0592a1c78", "status": "failed", "time": {"start": 1754492458670, "stop": 1754492458670, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8a6ee13ab0e739c0510eeda0592a1c78"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1748B0>.wait_for_page_load", "children": [{"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "b4f4ccf9053281c7", "parentUid": "bb41362a3e29620135dab1fcbd359a1f", "status": "failed", "time": {"start": 1754492493249, "stop": 1754492493249, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bb41362a3e29620135dab1fcbd359a1f"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D4921A0>.wait_for_page_load", "children": [{"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "de40562d3921e98a", "parentUid": "66693c40d85598d0d13481dff4d4cca8", "status": "failed", "time": {"start": 1754492527633, "stop": 1754492527633, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "66693c40d85598d0d13481dff4d4cca8"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6A5690>.wait_for_page_load", "children": [{"name": "测试set smart hub返回正确的不支持响应", "uid": "e1ee406f1611eb73", "parentUid": "e6940eab7f32fd68a8a35afcd29464a6", "status": "failed", "time": {"start": 1754492562170, "stop": 1754492562170, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e6940eab7f32fd68a8a35afcd29464a6"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB70C70>.wait_for_page_load", "children": [{"name": "测试set smart panel返回正确的不支持响应", "uid": "a79d5f2983e68bda", "parentUid": "077214f8d39c349a8927cca55afabe61", "status": "failed", "time": {"start": 1754492596508, "stop": 1754492596508, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "077214f8d39c349a8927cca55afabe61"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB725F0>.wait_for_page_load", "children": [{"name": "测试set special function返回正确的不支持响应", "uid": "d5f2f0dc1b836163", "parentUid": "9514d37029b503bebc7ef2bd803fa287", "status": "failed", "time": {"start": 1754492630353, "stop": 1754492630353, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9514d37029b503bebc7ef2bd803fa287"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065E6E6920>.wait_for_page_load", "children": [{"name": "测试set split-screen apps返回正确的不支持响应", "uid": "1ae31f6c836ef647", "parentUid": "cfc23300d95bdfedbbb445aa2c5c9569", "status": "failed", "time": {"start": 1754492664470, "stop": 1754492664470, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cfc23300d95bdfedbbb445aa2c5c9569"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB8AD10>.wait_for_page_load", "children": [{"name": "测试set timezone返回正确的不支持响应", "uid": "393c6d5c28a42af0", "parentUid": "198732afd8a52a020148d040309e78fe", "status": "failed", "time": {"start": 1754492698847, "stop": 1754492698847, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "198732afd8a52a020148d040309e78fe"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D876560>.wait_for_page_load", "children": [{"name": "测试set ultra power saving返回正确的不支持响应", "uid": "762351f9affe7b3a", "parentUid": "6258219e5f77ab0a8a40fd24341870e2", "status": "failed", "time": {"start": 1754492733532, "stop": 1754492733532, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6258219e5f77ab0a8a40fd24341870e2"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB61870>.wait_for_page_load", "children": [{"name": "测试start walking能正常执行", "uid": "5d77080ffe1fccbb", "parentUid": "d000813da4b7570e9a44288d8c39e71b", "status": "failed", "time": {"start": 1754492802065, "stop": 1754492802065, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d000813da4b7570e9a44288d8c39e71b"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EACA740>.wait_for_page_load", "children": [{"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "8f35d4cdbada546a", "parentUid": "58bf51ddb87c2c5c75b4aa5b89fe9269", "status": "failed", "time": {"start": 1754492835997, "stop": 1754492835997, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58bf51ddb87c2c5c75b4aa5b89fe9269"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D99F0D0>.wait_for_page_load", "children": [{"name": "测试switch to performance mode返回正确的不支持响应", "uid": "ab6461e475482ce", "parentUid": "8ec68d80ff04d2da5fe4d132070f8c76", "status": "failed", "time": {"start": 1754492870120, "stop": 1754492870120, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8ec68d80ff04d2da5fe4d132070f8c76"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065CC7C760>.wait_for_page_load", "children": [{"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "342df515163bc531", "parentUid": "20cfb03494bc24b5e5e399cd91efd4b7", "status": "failed", "time": {"start": 1754492904126, "stop": 1754492904126, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20cfb03494bc24b5e5e399cd91efd4b7"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D492770>.wait_for_page_load", "children": [{"name": "测试switching charging speed能正常执行", "uid": "19f30a50229fa046", "parentUid": "235294dfcd5fdb75f6d2e54d3dc36d97", "status": "failed", "time": {"start": 1754492938029, "stop": 1754492938029, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "235294dfcd5fdb75f6d2e54d3dc36d97"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D0B5780>.wait_for_page_load", "children": [{"name": "测试take notes能正常执行", "uid": "787d73d161918578", "parentUid": "ccbf23943d70c9cb0663fc28c4426720", "status": "failed", "time": {"start": 1754492972187, "stop": 1754492972187, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ccbf23943d70c9cb0663fc28c4426720"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D951D50>.wait_for_page_load", "children": [{"name": "测试tell me joke能正常执行", "uid": "9e625edf27bc68e4", "parentUid": "8cf55591c564181bd553ad443cfa9bfc", "status": "failed", "time": {"start": 1754493006073, "stop": 1754493006073, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8cf55591c564181bd553ad443cfa9bfc"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB82890>.wait_for_page_load", "children": [{"name": "测试the second返回正确的不支持响应", "uid": "5673296d3573d0a2", "parentUid": "127b90e6c8c593bd619976c1a66a3ee0", "status": "failed", "time": {"start": 1754493040820, "stop": 1754493040820, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "127b90e6c8c593bd619976c1a66a3ee0"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB73F10>.wait_for_page_load", "children": [{"name": "测试turn off driving mode返回正确的不支持响应", "uid": "72369a52d654526c", "parentUid": "ed563235048a72854e1123132fac13a3", "status": "failed", "time": {"start": 1754493075432, "stop": 1754493075432, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ed563235048a72854e1123132fac13a3"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB636D0>.wait_for_page_load", "children": [{"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "79a4858764b02126", "parentUid": "18aeb3584b457c24b77894378d9691bb", "status": "failed", "time": {"start": 1754493109867, "stop": 1754493109867, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "18aeb3584b457c24b77894378d9691bb"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EB88CA0>.wait_for_page_load", "children": [{"name": "测试turn on driving mode返回正确的不支持响应", "uid": "93ff373023646cf6", "parentUid": "38ae3c3e7e6348c5e1c6188ae979cb71", "status": "failed", "time": {"start": 1754493143901, "stop": 1754493143901, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "38ae3c3e7e6348c5e1c6188ae979cb71"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D5AA710>.wait_for_page_load", "children": [{"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "e9e9c48b1db33302", "parentUid": "3d2be61592409b96ad67743fd05d1b58", "status": "failed", "time": {"start": 1754493177669, "stop": 1754493177669, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3d2be61592409b96ad67743fd05d1b58"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D1F57B0>.wait_for_page_load", "children": [{"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "921d2165e4b8b2c6", "parentUid": "abd18e9cdc6a7a5bc556ca343803c0eb", "status": "failed", "time": {"start": 1754493211769, "stop": 1754493211769, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "abd18e9cdc6a7a5bc556ca343803c0eb"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D174EB0>.wait_for_page_load", "children": [{"name": "测试vedio call number by whatsapp能正常执行", "uid": "7eb514c65b1432a5", "parentUid": "c5af582501612b97753bc0f793a9e803", "status": "failed", "time": {"start": 1754493246248, "stop": 1754493246248, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c5af582501612b97753bc0f793a9e803"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D174BB0>.wait_for_page_load", "children": [{"name": "测试what date is it能正常执行", "uid": "7452352603fb4f0", "parentUid": "97cf9028ca49ca5eceb5063478bb2965", "status": "failed", "time": {"start": 1754493314194, "stop": 1754493314194, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "97cf9028ca49ca5eceb5063478bb2965"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065EC06770>.wait_for_page_load", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "182ba89eca203a55", "parentUid": "f324655adbe1b050da6b8e9a1457329d", "status": "failed", "time": {"start": 1754493348988, "stop": 1754493348988, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f324655adbe1b050da6b8e9a1457329d"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D5ABCD0>.wait_for_page_load", "children": [{"name": "测试what time is it in London能正常执行", "uid": "b68ac7588583315d", "parentUid": "d989f0652cd52366585b50965ad9fd2e", "status": "failed", "time": {"start": 1754493383221, "stop": 1754493383221, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d989f0652cd52366585b50965ad9fd2e"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000002065D490B50>.wait_for_page_load", "children": [{"name": "测试yandex eats返回正确的不支持响应", "uid": "70d9a7a738f698e4", "parentUid": "f1011e401616761271e4b88d43894675", "status": "failed", "time": {"start": 1754493417107, "stop": 1754493417107, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f1011e401616761271e4b88d43894675"}], "uid": "8fb3a91ba5aaf9de24cc8a92edc82b5d"}, {"name": "Test defects", "children": [{"name": "TypeError: a bytes-like object is required, not 'dict'", "children": [{"name": "测试book a flight to paris返回正确的不支持响应", "uid": "e11b0aebcd0a881b", "parentUid": "018bca9f4d9ac8e9d50a0bff138a0cdf", "status": "broken", "time": {"start": 1754484547829, "stop": 1754484565109, "duration": 17280}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "018bca9f4d9ac8e9d50a0bff138a0cdf"}, {"name": "ValueError: too many values to unpack (expected 3)", "children": [{"name": "测试searching for a method of violent murder能正常执行", "uid": "a34c28ac75d4d56f", "parentUid": "8e6c858299af90aba28521bd81ad3b69", "status": "broken", "time": {"start": 1754485482383, "stop": 1754485498034, "duration": 15651}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8e6c858299af90aba28521bd81ad3b69"}], "uid": "bdbf199525818fae7a8651db9eafe741"}]}