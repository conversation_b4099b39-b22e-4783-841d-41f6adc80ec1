"DESCRIPTION","DURATION IN MS","NAME","PARENT SUITE","START TIME","STATUS","STOP TIME","SUB SUITE","SUITE","TEST CLASS","TEST METHOD"
"could you please search an for me","15656","测试could you please search an for me能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:54:23 CST 2025","failed","Wed Aug 06 20:54:38 CST 2025","TestEllaCouldYouPleaseSearchAnMe","test_could_you_please_search_an_for_me","",""
"验证set parallel windows指令返回预期的不支持响应","0","测试set parallel windows返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:56:59 CST 2025","failed","Wed Aug 06 22:56:59 CST 2025","TestEllaSetParallelWindows","test_set_parallel_windows","",""
"验证turn off driving mode指令返回预期的不支持响应","0","测试turn off driving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:11:15 CST 2025","failed","Wed Aug 06 23:11:15 CST 2025","TestEllaTurnOffDrivingMode","test_turn_off_driving_mode","",""
"switch magic voice to Mango","13049","测试switch magic voice to Mango能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:35:43 CST 2025","passed","Wed Aug 06 21:35:56 CST 2025","TestEllaSwitchMagicVoiceToMango","test_switch_magic_voice_to_mango","",""
"hello hello","15201","测试hello hello能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:56:17 CST 2025","passed","Wed Aug 06 20:56:32 CST 2025","TestEllaHelloHello","test_hello_hello","",""
"navigate from to red square","0","测试navigate from to red square能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 21:52:30 CST 2025","failed","Wed Aug 06 21:52:30 CST 2025","TestEllaNavigateFromRedSquare","test_navigate_from_to_red_square","",""
"start screen recording","17536","测试start screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:31:47 CST 2025","passed","Wed Aug 06 21:32:05 CST 2025","TestEllaStartScreenRecording","test_start_screen_recording","",""
"show scores between livepool and manchester city","14269","测试show scores between livepool and manchester city能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:06:08 CST 2025","failed","Wed Aug 06 21:06:23 CST 2025","TestEllaShowScoresBetweenLivepoolManchesterCity","test_show_scores_between_livepool_and_manchester_city","",""
"stop screen recording","18421","stop  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:21:36 CST 2025","passed","Wed Aug 06 21:21:55 CST 2025","TestEllaTurnScreenRecord","test_end_screen_recording","",""
"continue music","13383","测试continue music能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:32:20 CST 2025","passed","Wed Aug 06 20:32:33 CST 2025","TestEllaContinueMusic","test_continue_music","",""
"验证open font family settings指令返回预期的不支持响应","0","测试open font family settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:33:08 CST 2025","failed","Wed Aug 06 22:33:08 CST 2025","TestEllaOpenSettings","test_open_font_family_settings","",""
"start running","0","测试start running能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 23:06:07 CST 2025","failed","Wed Aug 06 23:06:07 CST 2025","TestEllaStartRunning","test_start_running","",""
"turn on wifi","15810","测试turn on wifi能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:48:34 CST 2025","passed","Wed Aug 06 21:48:49 CST 2025","TestEllaTurnWifi","test_turn_on_wifi","",""
"测试play video指令","0","测试play video","testcases.test_ella.unsupported_commands","Wed Aug 06 22:38:48 CST 2025","failed","Wed Aug 06 22:38:48 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_video","",""
"测试play political news指令","15964","测试play political news","testcases.test_ella.dialogue","Wed Aug 06 21:04:13 CST 2025","passed","Wed Aug 06 21:04:29 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_political_news","",""
"测试Add the images and text on the screen to the note指令","0","测试Add the images and text on the screen to the note","testcases.test_ella.unsupported_commands","Wed Aug 06 21:57:37 CST 2025","failed","Wed Aug 06 21:57:37 CST 2025","TestEllaOpenPlayPoliticalNews","test_Add_the_images_and_text_on_the_screen_to_the_note","",""
"take a selfie","30591","测试take a selfie能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:41:08 CST 2025","failed","Wed Aug 06 21:41:38 CST 2025","TestEllaTakeSelfie","test_take_a_selfie","",""
"验证driving mode指令返回预期的不支持响应","0","测试driving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:13:33 CST 2025","failed","Wed Aug 06 22:13:33 CST 2025","TestEllaDrivingMode","test_driving_mode","",""
"测试play music指令","18473","测试play music","testcases.test_ella.component_coupling","Wed Aug 06 20:42:41 CST 2025","passed","Wed Aug 06 20:42:59 CST 2025","TestEllaOpenVisha","test_play_music","",""
"searching for a method of violent murder","15651","测试searching for a method of violent murder能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:04:42 CST 2025","broken","Wed Aug 06 21:04:58 CST 2025","TestEllaSearchingForAMethodOfViolentMurder","test_searching_for_a_method_of_violent_murder","",""
"测试play video by youtube指令","0","测试play video by youtube","testcases.test_ella.unsupported_commands","Wed Aug 06 22:39:22 CST 2025","failed","Wed Aug 06 22:39:22 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_video_by_youtube","",""
"download qq","17121","测试download qq能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 21:50:53 CST 2025","passed","Wed Aug 06 21:51:10 CST 2025","TestEllaDownloadQq","test_download_qq","",""
"验证disable magic voice changer指令返回预期的不支持响应","0","测试disable magic voice changer返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:09:33 CST 2025","failed","Wed Aug 06 22:09:33 CST 2025","TestEllaDisableMagicVoiceChanger","test_disable_magic_voice_changer","",""
"navigate to shanghai disneyland","0","测试navigate to shanghai disneyland能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 21:53:03 CST 2025","failed","Wed Aug 06 21:53:03 CST 2025","TestEllaNavigateShanghaiDisneyland","test_navigate_to_shanghai_disneyland","",""
"take a joke","15221","测试take a joke能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:08:49 CST 2025","passed","Wed Aug 06 21:09:04 CST 2025","TestEllaTakeJoke","test_take_a_joke","",""
"what's the wheather today?","14909","测试what's the wheather today?能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:13:12 CST 2025","failed","Wed Aug 06 21:13:27 CST 2025","TestEllaWhatSWheatherToday","test_what_s_the_wheather_today","",""
"验证disable unfreeze指令返回预期的不支持响应","0","测试disable unfreeze返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:11:50 CST 2025","failed","Wed Aug 06 22:11:50 CST 2025","TestEllaDisableUnfreeze","test_disable_unfreeze","",""
"验证book a flight to paris指令返回预期的不支持响应","17280","测试book a flight to paris返回正确的不支持响应","testcases.test_ella.dialogue","Wed Aug 06 20:49:07 CST 2025","broken","Wed Aug 06 20:49:25 CST 2025","TestEllaBookFlightParis","test_book_a_flight_to_paris","",""
"验证set split-screen apps指令返回预期的不支持响应","0","测试set split-screen apps返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:04:24 CST 2025","failed","Wed Aug 06 23:04:24 CST 2025","TestEllaSetSplitScreenApps","test_set_split_screen_apps","",""
"验证set color style指令返回预期的不支持响应","0","测试set color style返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:47:18 CST 2025","failed","Wed Aug 06 22:47:18 CST 2025","TestEllaSetColorStyle","test_set_color_style","",""
"cannot login in google email box","13717","测试cannot login in google email box能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:50:41 CST 2025","passed","Wed Aug 06 20:50:55 CST 2025","TestEllaCannotLoginGoogleEmailBox","test_cannot_login_in_google_email_box","",""
"验证enable zonetouch master指令返回预期的不支持响应","0","测试enable zonetouch master返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:19:47 CST 2025","failed","Wed Aug 06 22:19:47 CST 2025","TestEllaEnableZonetouchMaster","test_enable_zonetouch_master","",""
"验证set scheduled power on/off and restart指令返回预期的不支持响应","0","测试set scheduled power on/off and restart返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:59:15 CST 2025","failed","Wed Aug 06 22:59:15 CST 2025","TestEllaSetScheduledPowerOffRestart","test_set_scheduled_power_on_off_and_restart","",""
"check my to-do list","0","测试check my to-do list能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:03:18 CST 2025","failed","Wed Aug 06 22:03:18 CST 2025","TestEllaCheckMyDoList","test_check_my_to_do_list","",""
"close folax","35394","测试close folax能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:53:08 CST 2025","passed","Wed Aug 06 20:53:43 CST 2025","TestEllaCloseFolax","test_close_folax","",""
"check status updates on whatsapp","14417","测试check status updates on whatsapp能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:51:08 CST 2025","passed","Wed Aug 06 20:51:22 CST 2025","TestEllaCheckStatusUpdatesWhatsapp","test_check_status_updates_on_whatsapp","",""
"show me premier leaguage goal ranking","15173","测试show me premier leaguage goal ranking能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:05:40 CST 2025","failed","Wed Aug 06 21:05:55 CST 2025","TestEllaShowMePremierLeaguageGoalRanking","test_show_me_premier_leaguage_goal_ranking","",""
"memory cleanup","25638","测试memory cleanup能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:24:06 CST 2025","passed","Wed Aug 06 21:24:31 CST 2025","TestEllaMemoryCleanup","test_memory_cleanup","",""
"close bluetooth","15328","测试close bluetooth能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:19:12 CST 2025","passed","Wed Aug 06 21:19:27 CST 2025","TestEllaCloseBluetooth","test_close_bluetooth","",""
"what·s the weather today？","20852","测试what·s the weather today？能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:12:38 CST 2025","passed","Wed Aug 06 21:12:59 CST 2025","TestEllaWhatSWeatherToday","test_what_s_the_weather_today","",""
"why my charging is so slow","14295","测试why my charging is so slow能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:16:36 CST 2025","failed","Wed Aug 06 21:16:50 CST 2025","TestEllaWhyMyChargingIsSoSlow","test_why_my_charging_is_so_slow","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","12847","测试open contact命令 - 简洁版本","testcases.test_ella.component_coupling","Wed Aug 06 20:37:51 CST 2025","passed","Wed Aug 06 20:38:04 CST 2025","TestEllaCommandConcise","test_open_ella","",""
"Search for addresses on the screen","0","测试Search for addresses on the screen能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:43:20 CST 2025","failed","Wed Aug 06 22:43:20 CST 2025","TestEllaSearchAddressesScreen","test_search_for_addresses_on_the_screen","",""
"what's your name？","13367","测试what's your name？能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:13:40 CST 2025","passed","Wed Aug 06 21:13:53 CST 2025","TestEllaWhatSYourName","test_what_s_your_name","",""
"测试open bt指令","13981","测试open bt","testcases.test_ella.system_coupling","Wed Aug 06 21:25:40 CST 2025","passed","Wed Aug 06 21:25:54 CST 2025","TestEllaOpenBluetooth","test_open_bt","",""
"验证order a burger指令返回预期的不支持响应","0","测试order a burger返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:34:50 CST 2025","failed","Wed Aug 06 22:34:50 CST 2025","TestEllaOrderBurger","test_order_a_burger","",""
"disable magic voice changer","13409","测试disable magic voice changer能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:54:51 CST 2025","passed","Wed Aug 06 20:55:05 CST 2025","TestEllaDisableMagicVoiceChanger","test_disable_magic_voice_changer","",""
"navigation to the first address in the image","0","测试navigation to the first address in the image能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:32:34 CST 2025","failed","Wed Aug 06 22:32:34 CST 2025","TestEllaNavigationFirstAddressImage","test_navigation_to_the_first_address_in_the_image","",""
"help me write an thanks email","0","测试help me write an thanks email能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:22:37 CST 2025","failed","Wed Aug 06 22:22:37 CST 2025","TestEllaHelpMeWriteAnThanksEmail","test_help_me_write_an_thanks_email","",""
"give me some money","17366","测试give me some money能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:55:17 CST 2025","passed","Wed Aug 06 20:55:35 CST 2025","TestEllaGiveMeSomeMoney","test_give_me_some_money","",""
"验证disable all ai magic box features指令返回预期的不支持响应","0","测试disable all ai magic box features返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:06:42 CST 2025","failed","Wed Aug 06 22:06:42 CST 2025","TestEllaDisableAllAiMagicBoxFeatures","test_disable_all_ai_magic_box_features","",""
"go home","0","测试go home能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:20:55 CST 2025","failed","Wed Aug 06 22:20:55 CST 2025","TestEllaGoHome","test_go_home","",""
"turn on light theme","13813","测试turn on light theme能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:45:14 CST 2025","passed","Wed Aug 06 21:45:28 CST 2025","TestEllaTurnLightTheme","test_turn_on_light_theme","",""
"Switch Magic Voice to Grace","14293","测试Switch Magic Voice to Grace能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:35:16 CST 2025","passed","Wed Aug 06 21:35:30 CST 2025","TestEllaSwitchMagicVoiceGrace","test_switch_magic_voice_to_grace","",""
"who is harry potter","16212","测试who is harry potter能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:15:03 CST 2025","passed","Wed Aug 06 21:15:20 CST 2025","TestEllaWhoIsHarryPotter","test_who_is_harry_potter","",""
"验证yandex eats指令返回预期的不支持响应","0","测试yandex eats返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:16:57 CST 2025","failed","Wed Aug 06 23:16:57 CST 2025","TestEllaYandexEats","test_yandex_eats","",""
"check contact","0","测试check contact能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:00:27 CST 2025","failed","Wed Aug 06 22:00:27 CST 2025","TestEllaCheckContact","test_check_contact","",""
"验证Modify grape timbre指令返回预期的不支持响应","0","测试Modify grape timbre返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:30:51 CST 2025","failed","Wed Aug 06 22:30:51 CST 2025","TestEllaEnableRunningLock","test_modify_grape_timbre","",""
"how is the wheather today","13538","测试how is the wheather today能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:58:24 CST 2025","passed","Wed Aug 06 20:58:38 CST 2025","TestEllaHowIsWheatherToday","test_how_is_the_wheather_today","",""
"测试display the route go company指令","22367","测试display the route go company","testcases.test_ella.component_coupling","Wed Aug 06 20:33:43 CST 2025","failed","Wed Aug 06 20:34:05 CST 2025","TestEllaOpenMaps","test_display_the_route_go_company","",""
"turn on the screen record","17054","测试turn on the screen record能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:47:32 CST 2025","passed","Wed Aug 06 21:47:49 CST 2025","TestEllaTurnScreenRecord","test_turn_on_the_screen_record","",""
"Help me write an email to make an appointment for a visit","26611","测试Help me write an email to make an appointment for a visit能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:56:45 CST 2025","passed","Wed Aug 06 20:57:12 CST 2025","TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit","test_help_me_write_an_email_to_make_an_appointment_for_a_visit","",""
"who is j k rowling","17369","测试who is j k rowling能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:15:33 CST 2025","passed","Wed Aug 06 21:15:50 CST 2025","TestEllaWhoIsJKRowling","test_who_is_j_k_rowling","",""
"turn on location services","14415","测试turn on location services能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:46:08 CST 2025","passed","Wed Aug 06 21:46:22 CST 2025","TestEllaTurnLocationServices","test_turn_on_location_services","",""
"验证set gesture navigation指令返回预期的不支持响应","0","测试set gesture navigation返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:53:35 CST 2025","failed","Wed Aug 06 22:53:35 CST 2025","TestEllaSetGestureNavigation","test_set_gesture_navigation","",""
"switched to data mode","13804","测试switched to data mode能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:39:57 CST 2025","failed","Wed Aug 06 21:40:11 CST 2025","TestEllaSwitchedDataMode","test_switched_to_data_mode","",""
"stop  screen recording","17947","stop  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:48:03 CST 2025","passed","Wed Aug 06 21:48:21 CST 2025","TestEllaTurnScreenRecord","test_turn_on_the_screen_record","",""
"screen record","19031","测试screen record能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:27:41 CST 2025","passed","Wed Aug 06 21:28:00 CST 2025","TestEllaScreenRecord","test_screen_record","",""
"smart charge","14482","测试smart charge能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:30:18 CST 2025","failed","Wed Aug 06 21:30:33 CST 2025","TestEllaSmartCharge","test_smart_charge","",""
"what date is it","0","测试what date is it能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 23:15:14 CST 2025","failed","Wed Aug 06 23:15:14 CST 2025","TestEllaWhatDateIsIt","test_what_date_is_it","",""
"What's the weather like in Shanghai today","21060","测试What's the weather like in Shanghai today能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:48:06 CST 2025","passed","Wed Aug 06 20:48:27 CST 2025","TestEllaWhatSWeatherLikeShanghaiToday","test_what_s_the_weather_like_in_shanghai_today","",""
"验证check mobile data balance of sim2指令返回预期的不支持响应","0","测试check mobile data balance of sim2返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:01:36 CST 2025","failed","Wed Aug 06 22:01:36 CST 2025","TestEllaCheckMobileDataBalanceSim","test_check_mobile_data_balance_of_sim","",""
"global gdp trends","16982","测试global gdp trends能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:55:47 CST 2025","passed","Wed Aug 06 20:56:04 CST 2025","TestEllaGlobalGdpTrends","test_global_gdp_trends","",""
"order a burger","0","测试order a burger能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 21:55:20 CST 2025","failed","Wed Aug 06 21:55:20 CST 2025","TestEllaCommandConcise","test_order_a_burger","",""
"set Battery Saver setting","21123","测试set Battery Saver setting能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:29:44 CST 2025","failed","Wed Aug 06 21:30:05 CST 2025","TestEllaSetBatterySaverSetting","test_set_battery_saver_setting","",""
"验证disable network enhancement指令返回预期的不支持响应","0","测试disable network enhancement返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:10:07 CST 2025","failed","Wed Aug 06 22:10:07 CST 2025","TestEllaDisableNetworkEnhancement","test_disable_network_enhancement","",""
"take notes","0","测试take notes能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 23:09:32 CST 2025","failed","Wed Aug 06 23:09:32 CST 2025","TestEllaTakeNotes","test_take_notes","",""
"验证set screen relay指令返回预期的不支持响应","0","测试set screen relay返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:00:24 CST 2025","failed","Wed Aug 06 23:00:24 CST 2025","TestEllaSetScreenRelay","test_set_screen_relay","",""
"验证the second指令返回预期的不支持响应","0","测试the second返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:10:40 CST 2025","failed","Wed Aug 06 23:10:40 CST 2025","TestEllaSecond","test_the_second","",""
"find a restaurant near me","31016","测试find a restaurant near me能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 21:51:23 CST 2025","passed","Wed Aug 06 21:51:54 CST 2025","TestEllaFindRestaurantNearMe","test_find_a_restaurant_near_me","",""
"help me take a screenshot","16773","测试help me take a screenshot能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:22:38 CST 2025","passed","Wed Aug 06 21:22:55 CST 2025","TestEllaHelpMeTakeScreenshot","test_help_me_take_a_screenshot","",""
"restart the phone","0","测试restart the phone能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:42:46 CST 2025","failed","Wed Aug 06 22:42:46 CST 2025","TestEllaRestartPhone","test_restart_the_phone","",""
"turn on light theme","14299","测试turn on light theme能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:45:41 CST 2025","passed","Wed Aug 06 21:45:55 CST 2025","TestEllaTurnLightTheme","test_turn_on_light_theme","",""
"Switch to Barrage Notification","16087","测试Switch to Barrage Notification能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:36:09 CST 2025","failed","Wed Aug 06 21:36:25 CST 2025","TestEllaSwitchBarrageNotification","test_switch_to_barrage_notification","",""
"close phonemaster","13332","测试close phonemaster能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:31:53 CST 2025","passed","Wed Aug 06 20:32:07 CST 2025","TestEllaClosePhonemaster","test_close_phonemaster","",""
"what time is it in London","0","测试what time is it in London能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 23:16:23 CST 2025","failed","Wed Aug 06 23:16:23 CST 2025","TestEllaWhatTimeIsItLondon","test_what_time_is_it_in_london","",""
"take notes on how to build a treehouse","13349","测试take notes on how to build a treehouse能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:09:43 CST 2025","passed","Wed Aug 06 21:09:57 CST 2025","TestEllaTakeNotesHowBuildTreehouse","test_take_notes_on_how_to_build_a_treehouse","",""
"tell me joke","0","测试tell me joke能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 23:10:06 CST 2025","failed","Wed Aug 06 23:10:06 CST 2025","TestEllaTellMeJoke","test_tell_me_joke","",""
"测试play jay chou's music by spotify指令","17173","测试play jay chou's music by spotify","testcases.test_ella.component_coupling","Wed Aug 06 20:42:11 CST 2025","passed","Wed Aug 06 20:42:28 CST 2025","TestEllaOpenMusic","test_play_jay_chou_s_music_by_spotify","",""
"验证set font size指令返回预期的不支持响应","0","测试set font size返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:53:01 CST 2025","failed","Wed Aug 06 22:53:01 CST 2025","TestEllaSetFontSize","test_set_font_size","",""
"previous music","14799","测试previous music能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:44:17 CST 2025","passed","Wed Aug 06 20:44:32 CST 2025","TestEllaPreviousMusic","test_previous_music","",""
"stop  screen recording","18446","stop  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:31:16 CST 2025","passed","Wed Aug 06 21:31:34 CST 2025","TestEllaStartRecord","test_start_record","",""
"turn on the flashlight","15865","测试turn on the flashlight能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:47:03 CST 2025","passed","Wed Aug 06 21:47:19 CST 2025","TestEllaTurnFlashlight","test_turn_on_the_flashlight","",""
"switching charging speed","0","测试switching charging speed能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 23:08:58 CST 2025","failed","Wed Aug 06 23:08:58 CST 2025","TestEllaSwitchingChargingSpeed","test_switching_charging_speed","",""
"验证set customized cover screen指令返回预期的不支持响应","0","测试set customized cover screen返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:49:01 CST 2025","failed","Wed Aug 06 22:49:01 CST 2025","TestEllaSetCustomizedCoverScreen","test_set_customized_cover_screen","",""
"turn off light theme","13709","测试turn off light theme能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:43:25 CST 2025","passed","Wed Aug 06 21:43:39 CST 2025","TestEllaTurnOffLightTheme","test_turn_off_light_theme","",""
"set off a firework","0","测试set off a firework能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:56:25 CST 2025","failed","Wed Aug 06 22:56:25 CST 2025","TestEllaSetOffFirework","test_set_off_a_firework","",""
"使用简化的测试框架测试phonemaster开启命令，验证响应包含Done且实际打开PhoneMaster","29733","测试clear junk files命令","testcases.test_ella.system_coupling","Wed Aug 06 21:18:29 CST 2025","passed","Wed Aug 06 21:18:59 CST 2025","TestEllaClearJunkFiles","test_clear_junk_files","",""
"make a call","20398","测试make a call能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:02:43 CST 2025","passed","Wed Aug 06 21:03:03 CST 2025","TestEllaMakeCall","test_make_a_call","",""
"测试open wifi指令","14137","测试open wifi","testcases.test_ella.system_coupling","Wed Aug 06 21:26:36 CST 2025","passed","Wed Aug 06 21:26:51 CST 2025","TestEllaOpenWifi","test_open_wifi","",""
"maximum volume","15051","测试maximum volume能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:23:38 CST 2025","passed","Wed Aug 06 21:23:53 CST 2025","TestEllaMaximumVolume","test_maximum_volume","",""
"whatsapp","0","测试whatsapp能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 21:57:03 CST 2025","failed","Wed Aug 06 21:57:03 CST 2025","TestEllaWhatsapp","test_whatsapp","",""
"验证close performance mode指令返回预期的不支持响应","0","测试close performance mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:05:00 CST 2025","failed","Wed Aug 06 22:05:00 CST 2025","TestEllaClosePerformanceMode","test_close_performance_mode","",""
"what is apec?","16776","测试what is apec?能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:11:11 CST 2025","passed","Wed Aug 06 21:11:28 CST 2025","TestEllaWhatIsApec","test_what_is_apec","",""
"验证enable running lock指令返回预期的不支持响应","0","测试enable running lock返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:18:05 CST 2025","failed","Wed Aug 06 22:18:05 CST 2025","TestEllaEnableRunningLock","test_enable_running_lock","",""
"验证set personal hotspot指令返回预期的不支持响应","0","测试set personal hotspot返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:57:33 CST 2025","failed","Wed Aug 06 22:57:33 CST 2025","TestEllaSetPersonalHotspot","test_set_personal_hotspot","",""
"验证disable touch optimization指令返回预期的不支持响应","0","测试disable touch optimization返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:11:16 CST 2025","failed","Wed Aug 06 22:11:16 CST 2025","TestEllaDisableTouchOptimization","test_disable_touch_optimization","",""
"测试jump to nfc settings指令","0","测试jump to nfc settings","testcases.test_ella.unsupported_commands","Wed Aug 06 22:29:08 CST 2025","failed","Wed Aug 06 22:29:08 CST 2025","TestEllaOpenPlayPoliticalNews","test_jump_to_nfc_settings","",""
"turn on the screen record","17044","测试turn on the screen record能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:21:06 CST 2025","passed","Wed Aug 06 21:21:23 CST 2025","TestEllaTurnScreenRecord","test_end_screen_recording","",""
"countdown 5 min","17479","测试countdown 5 min能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:20:09 CST 2025","failed","Wed Aug 06 21:20:26 CST 2025","TestEllaCountdownMin","test_countdown_min","",""
"验证jump to ai wallpaper generator settings指令返回预期的不支持响应","0","测试jump to ai wallpaper generator settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:25:26 CST 2025","failed","Wed Aug 06 22:25:26 CST 2025","TestEllaJumpAiWallpaperGeneratorSettings","test_jump_to_ai_wallpaper_generator_settings","",""
"测试play rock music指令","19196","测试play rock music","testcases.test_ella.component_coupling","Wed Aug 06 20:43:12 CST 2025","passed","Wed Aug 06 20:43:32 CST 2025","TestEllaOpenVisha","test_play_rock_music","",""
"check contacts","0","测试check contacts能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:01:02 CST 2025","failed","Wed Aug 06 22:01:02 CST 2025","TestEllaCheckContacts","test_check_contacts","",""
"验证set special function指令返回预期的不支持响应","0","测试set special function返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:03:50 CST 2025","failed","Wed Aug 06 23:03:50 CST 2025","TestEllaSetSpecialFunction","test_set_special_function","",""
"验证set folding screen zone指令返回预期的不支持响应","0","测试set folding screen zone返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:52:26 CST 2025","failed","Wed Aug 06 22:52:26 CST 2025","TestEllaSetFoldingScreenZone","test_set_folding_screen_zone","",""
"resume music","13092","测试resume music能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:44:45 CST 2025","failed","Wed Aug 06 20:44:58 CST 2025","TestEllaResumeMusic","test_resume_music","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","16339","测试open flashlight","testcases.test_ella.system_coupling","Wed Aug 06 21:26:07 CST 2025","passed","Wed Aug 06 21:26:23 CST 2025","TestEllaCommandConcise","test_open_flashlight","",""
"验证order a takeaway指令返回预期的不支持响应","0","测试order a takeaway返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:35:24 CST 2025","failed","Wed Aug 06 22:35:24 CST 2025","TestEllaOrderTakeaway","test_order_a_takeaway","",""
"search the address in the image","0","测试search the address in the image能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:43:54 CST 2025","failed","Wed Aug 06 22:43:54 CST 2025","TestEllaSearchAddressImage","test_search_the_address_in_the_image","",""
"测试open whatsapp指令","0","测试open whatsapp","testcases.test_ella.unsupported_commands","Wed Aug 06 22:34:16 CST 2025","failed","Wed Aug 06 22:34:16 CST 2025","TestEllaOpenWhatsapp","test_open_whatsapp","",""
"测试open bluetooth指令","14360","测试open bluetooth","testcases.test_ella.system_coupling","Wed Aug 06 21:25:13 CST 2025","passed","Wed Aug 06 21:25:27 CST 2025","TestEllaOpenBluetooth","test_open_bluetooth","",""
"open countdown","13566","测试open countdown能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:36:49 CST 2025","failed","Wed Aug 06 20:37:02 CST 2025","TestEllaCommandConcise","test_open_countdown","",""
"Switch to Low-Temp Charge","13481","测试Switch to Low-Temp Charge能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:38:37 CST 2025","failed","Wed Aug 06 21:38:51 CST 2025","TestEllaSwitchToLowtempCharge","test_switch_to_low_temp_charge","",""
"验证Enable Call Rejection指令返回预期的不支持响应","0","测试Enable Call Rejection返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:16:57 CST 2025","failed","Wed Aug 06 22:16:57 CST 2025","TestEllaEnableCallRejection","test_enable_call_rejection","",""
"help me write an thanks letter","0","测试help me write an thanks letter能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:23:11 CST 2025","failed","Wed Aug 06 22:23:11 CST 2025","TestEllaHelpMeWriteAnThanksLetter","test_help_me_write_an_thanks_letter","",""
"验证open notification ringtone settings指令返回预期的不支持响应","0","测试open notification ringtone settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:33:42 CST 2025","failed","Wed Aug 06 22:33:42 CST 2025","TestEllaOpenSettings","test_open_notification_ringtone_settings","",""
"vedio call number by whatsapp","0","测试vedio call number by whatsapp能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 23:14:06 CST 2025","failed","Wed Aug 06 23:14:06 CST 2025","TestEllaVedioCallNumberWhatsapp","test_vedio_call_number_by_whatsapp","",""
"turn on bluetooth","13803","测试turn on bluetooth能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:44:20 CST 2025","passed","Wed Aug 06 21:44:34 CST 2025","TestEllaTurnBluetooth","test_turn_on_bluetooth","",""
"验证download basketball指令返回预期的不支持响应","0","测试download basketball返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:12:58 CST 2025","failed","Wed Aug 06 22:12:58 CST 2025","TestEllaDownloadBasketball","test_download_basketball","",""
"验证disable hide notifications指令返回预期的不支持响应","0","测试disable hide notifications返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:08:59 CST 2025","failed","Wed Aug 06 22:08:59 CST 2025","TestEllaDisableHideNotifications","test_disable_hide_notifications","",""
"验证set phantom v pen指令返回预期的不支持响应","0","测试set phantom v pen返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:58:07 CST 2025","failed","Wed Aug 06 22:58:07 CST 2025","TestEllaSetPhantomVPen","test_set_phantom_v_pen","",""
"验证Voice setting page指令返回预期的不支持响应","0","测试Voice setting page返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:14:40 CST 2025","failed","Wed Aug 06 23:14:40 CST 2025","TestEllaVoiceSettingPage","test_voice_setting_page","",""
"turn off flashlight","15345","测试turn off flashlight能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:42:57 CST 2025","passed","Wed Aug 06 21:43:12 CST 2025","TestEllaTurnOffFlashlight","test_turn_off_flashlight","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","23028","测试open contact命令","testcases.test_ella.component_coupling","Wed Aug 06 20:38:43 CST 2025","passed","Wed Aug 06 20:39:06 CST 2025","TestEllaContactCommandConcise","test_open_phone","",""
"open folax","12290","测试open folax能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:38:17 CST 2025","passed","Wed Aug 06 20:38:30 CST 2025","TestEllaCommandConcise","test_open_folax","",""
"测试play love sotry指令","0","测试play love sotry","testcases.test_ella.unsupported_commands","Wed Aug 06 22:37:07 CST 2025","failed","Wed Aug 06 22:37:07 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_love_sotry","",""
"验证set screen to minimum brightness指令返回预期的不支持响应","0","测试set screen to minimum brightness返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:01:33 CST 2025","failed","Wed Aug 06 23:01:33 CST 2025","TestEllaSetScreenMinimumBrightness","test_set_screen_to_minimum_brightness","",""
"验证jump to auto rotate screen settings指令返回预期的不支持响应","0","测试jump to auto rotate screen settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:25:50 CST 2025","failed","Wed Aug 06 22:25:50 CST 2025","TestEllaJumpAutoRotateScreenSettings","test_jump_to_auto_rotate_screen_settings","",""
"take a screenshot","15024","测试take a screenshot能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:46:09 CST 2025","failed","Wed Aug 06 20:46:24 CST 2025","TestEllaTakeScreenshot","test_take_a_screenshot","",""
"appeler maman","14408","测试appeler maman能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:48:40 CST 2025","passed","Wed Aug 06 20:48:55 CST 2025","TestEllaAppelerMaman","test_appeler_maman","",""
"order a takeaway","0","测试order a takeaway能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 21:55:54 CST 2025","failed","Wed Aug 06 21:55:54 CST 2025","TestEllaOrderATakeaway","test_order_a_takeaway","",""
"验证set sim1 ringtone指令返回预期的不支持响应","0","测试set sim1 ringtone返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:02:07 CST 2025","failed","Wed Aug 06 23:02:07 CST 2025","TestEllaSetSimRingtone","test_set_sim_ringtone","",""
"验证set screen timeout指令返回预期的不支持响应","0","测试set screen timeout返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:00:58 CST 2025","failed","Wed Aug 06 23:00:58 CST 2025","TestEllaSetScreenTimeout","test_set_screen_timeout","",""
"验证enable auto pickup指令返回预期的不支持响应","0","测试enable auto pickup返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:15:15 CST 2025","failed","Wed Aug 06 22:15:15 CST 2025","TestEllaEnableAutoPickup","test_enable_auto_pickup","",""
"pause song","13077","测试pause song能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:40:11 CST 2025","failed","Wed Aug 06 20:40:24 CST 2025","TestEllaPauseSong","test_pause_song","",""
"i want to make a call","19474","测试i want to make a call能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:01:13 CST 2025","passed","Wed Aug 06 21:01:33 CST 2025","TestEllaIWantMakeCall","test_i_want_to_make_a_call","",""
"使用open clock命令，验证响应包含Done且实际打开clock命令","18290","open clock","testcases.test_ella.component_coupling","Wed Aug 06 20:35:43 CST 2025","passed","Wed Aug 06 20:36:01 CST 2025","TestEllaCommandConcise","test_open_clock","",""
"测试stop playing指令","14892","测试stop playing","testcases.test_ella.component_coupling","Wed Aug 06 20:45:41 CST 2025","failed","Wed Aug 06 20:45:56 CST 2025","TestEllaOpenYoutube","test_stop_playing","",""
"my phone is too slow","14204","测试my phone is too slow能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:34:18 CST 2025","passed","Wed Aug 06 20:34:33 CST 2025","TestEllaMyPhoneIsTooSlow","test_my_phone_is_too_slow","",""
"验证disable brightness locking指令返回预期的不支持响应","0","测试disable brightness locking返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:07:51 CST 2025","failed","Wed Aug 06 22:07:51 CST 2025","TestEllaDisableBrightnessLocking","test_disable_brightness_locking","",""
"turn on nfc","14515","测试turn on nfc能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:46:36 CST 2025","passed","Wed Aug 06 21:46:50 CST 2025","TestEllaTurnNfc","test_turn_on_nfc","",""
"navigation to the address in thie image","0","测试navigation to the address in thie image能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:31:59 CST 2025","failed","Wed Aug 06 22:31:59 CST 2025","TestEllaNavigationAddressTheImage","test_navigation_to_the_address_in_the_image","",""
"stop workout","14300","测试stop workout能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:07:28 CST 2025","passed","Wed Aug 06 21:07:42 CST 2025","TestEllaStopWorkout","test_stop_workout","",""
"测试play football video by youtube指令","0","测试play football video by youtube","testcases.test_ella.unsupported_commands","Wed Aug 06 22:36:33 CST 2025","failed","Wed Aug 06 22:36:33 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_football_video_by_youtube","",""
"send my recent photos to mom through whatsapp","16044","测试send my recent photos to mom through whatsapp能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:05:11 CST 2025","failed","Wed Aug 06 21:05:27 CST 2025","TestEllaWhatSWeatherLikeShanghaiToday","test_send_my_recent_photos_to_mom_through_whatsapp","",""
"how's the weather today in shanghai","18639","测试how's the weather today in shanghai能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:59:21 CST 2025","passed","Wed Aug 06 20:59:40 CST 2025","TestEllaWhatSWeatherLikeShanghaiToday","test_how_s_the_weather_today_in_shanghai","",""
"验证more settings指令返回预期的不支持响应","0","测试more settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:31:25 CST 2025","failed","Wed Aug 06 22:31:25 CST 2025","TestEllaMoreSettings","test_more_settings","",""
"验证enable brightness locking指令返回预期的不支持响应","0","测试enable brightness locking返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:15:50 CST 2025","failed","Wed Aug 06 22:15:50 CST 2025","TestEllaEnableBrightnessLocking","test_enable_brightness_locking","",""
"测试set an alarm at 8 am指令","17070","测试set an alarm at 8 am","testcases.test_ella.component_coupling","Wed Aug 06 20:45:11 CST 2025","passed","Wed Aug 06 20:45:28 CST 2025","TestEllaOpenClock","test_set_an_alarm_at_8_am","",""
"验证switch to power saving mode指令返回预期的不支持响应","0","测试switch to power saving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:08:24 CST 2025","failed","Wed Aug 06 23:08:24 CST 2025","TestEllaSwitchPowerSavingMode","test_switch_to_power_saving_mode","",""
"测试play sun be song of jide chord指令","19829","测试play sun be song of jide chord","testcases.test_ella.component_coupling","Wed Aug 06 20:43:45 CST 2025","passed","Wed Aug 06 20:44:04 CST 2025","TestEllaOpenPlaySunBeSongOfJideChord","test_play_sun_be_song_of_jide_chord","",""
"验证jump to lock screen notification and display settings指令返回预期的不支持响应","0","测试jump to lock screen notification and display settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:28:32 CST 2025","failed","Wed Aug 06 22:28:32 CST 2025","TestEllaOpenSettings","test_jump_to_lock_screen_notification_and_display_settings","",""
"close folax","35112","测试close folax能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:31:05 CST 2025","passed","Wed Aug 06 20:31:40 CST 2025","TestEllaCloseFolax","test_close_folax","",""
"switch to power saving mode","14081","测试switch to power saving mode能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:39:04 CST 2025","passed","Wed Aug 06 21:39:18 CST 2025","TestEllaSwitchToPowerSavingMode","test_switch_to_power_saving_mode","",""
"check rear camera information","0","测试check rear camera information能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:03:52 CST 2025","failed","Wed Aug 06 22:03:52 CST 2025","TestEllaCheckRearCameraInformation","test_check_rear_camera_information","",""
"change your voice","0","测试change your voice能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 21:59:19 CST 2025","failed","Wed Aug 06 21:59:19 CST 2025","TestEllaChangeYourVoice","test_change_your_voice","",""
"take a note on how to build a treehouse","13504","测试take a note on how to build a treehouse能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:09:17 CST 2025","passed","Wed Aug 06 21:09:31 CST 2025","TestEllaTakeNoteHowBuildTreehouse","test_take_a_note_on_how_to_build_a_treehouse","",""
"restart my phone","0","测试restart my phone能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:42:12 CST 2025","failed","Wed Aug 06 22:42:12 CST 2025","TestEllaRestartMyPhone","test_restart_my_phone","",""
"测试set alarm for 10 o'clock指令","17692","测试set alarm for 10 o'clock","testcases.test_ella.system_coupling","Wed Aug 06 21:29:14 CST 2025","passed","Wed Aug 06 21:29:32 CST 2025","TestEllaOpenClock","test_set_alarm_for_10_o_clock","",""
"please show me where i am","0","测试please show me where i am能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:39:56 CST 2025","failed","Wed Aug 06 22:39:56 CST 2025","TestEllaPleaseShowMeWhereIAm","test_please_show_me_where_i_am","",""
"search whatsapp for me","0","测试search whatsapp for me能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:44:28 CST 2025","failed","Wed Aug 06 22:44:28 CST 2025","TestEllaSearchWhatsappMe","test_search_whatsapp_for_me","",""
"switch to default mode","16377","测试switch to default mode能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:36:39 CST 2025","passed","Wed Aug 06 21:36:55 CST 2025","TestEllaSwitchToDefaultMode","test_switch_to_default_mode","",""
"验证set smart panel指令返回预期的不支持响应","0","测试set smart panel返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:03:16 CST 2025","failed","Wed Aug 06 23:03:16 CST 2025","TestEllaSetSmartPanel","test_set_smart_panel","",""
"stop run","13448","测试stop run能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:07:02 CST 2025","passed","Wed Aug 06 21:07:15 CST 2025","TestEllaStopRun","test_stop_run","",""
"验证set battery saver settings指令返回预期的不支持响应","0","测试set battery saver settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:46:10 CST 2025","failed","Wed Aug 06 22:46:10 CST 2025","TestEllaSetBatterySaverSettings","test_set_battery_saver_settings","",""
"验证jump to battery and power saving指令返回预期的不支持响应","0","测试jump to battery and power saving返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:26:16 CST 2025","failed","Wed Aug 06 22:26:16 CST 2025","TestEllaJumpBatteryPowerSaving","test_jump_to_battery_and_power_saving","",""
"switch charging modes","13899","测试switch charging modes能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:34:49 CST 2025","failed","Wed Aug 06 21:35:02 CST 2025","TestEllaSwitchChargingModes","test_switch_charging_modes","",""
"whats the weather today","18170","测试whats the weather today能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:14:32 CST 2025","passed","Wed Aug 06 21:14:50 CST 2025","TestEllaWhatsWeatherToday","test_whats_the_weather_today","",""
"验证how's the weather today?指令返回预期的不支持响应","18005","测试how's the weather today?返回正确的不支持响应","testcases.test_ella.dialogue","Wed Aug 06 20:58:50 CST 2025","passed","Wed Aug 06 20:59:08 CST 2025","TestEllaHowSWeatherToday","test_how_s_the_weather_today","",""
"验证set timezone指令返回预期的不支持响应","0","测试set timezone返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:04:58 CST 2025","failed","Wed Aug 06 23:04:58 CST 2025","TestEllaSetTimezone","test_set_timezone","",""
"验证enable all ai magic box features指令返回预期的不支持响应","0","测试enable all ai magic box features返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:14:41 CST 2025","failed","Wed Aug 06 22:14:41 CST 2025","TestEllaEnableAllAiMagicBoxFeatures","test_enable_all_ai_magic_box_features","",""
"验证jump to adaptive brightness settings指令返回预期的不支持响应","0","测试jump to adaptive brightness settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:24:52 CST 2025","failed","Wed Aug 06 22:24:52 CST 2025","TestEllaJumpAdaptiveBrightnessSettings","test_jump_to_adaptive_brightness_settings","",""
"stop music","13046","测试stop music能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:06:36 CST 2025","failed","Wed Aug 06 21:06:49 CST 2025","TestEllaStopMusic","test_stop_music","",""
"close ella","33919","测试close ella能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:52:21 CST 2025","passed","Wed Aug 06 20:52:55 CST 2025","TestEllaCloseElla","test_close_ella","",""
"Adjustment the brightness to 50%","13831","测试Adjustment the brightness to 50%能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:17:03 CST 2025","failed","Wed Aug 06 21:17:17 CST 2025","TestEllaAdjustmentBrightness","test_adjustment_the_brightness_to","",""
"验证turn on high brightness mode指令返回预期的不支持响应","0","测试turn on high brightness mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:12:57 CST 2025","failed","Wed Aug 06 23:12:57 CST 2025","TestEllaTurnHighBrightnessMode","test_turn_on_high_brightness_mode","",""
"验证turn on show battery percentage指令返回预期的不支持响应","0","测试turn on show battery percentage返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:13:31 CST 2025","failed","Wed Aug 06 23:13:31 CST 2025","TestEllaTurnShowBatteryPercentage","test_turn_on_show_battery_percentage","",""
"happy new year","0","测试happy new year能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:21:29 CST 2025","failed","Wed Aug 06 22:21:29 CST 2025","TestEllaHappyNewYear","test_happy_new_year","",""
"验证enable unfreeze指令返回预期的不支持响应","0","测试enable unfreeze返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:19:13 CST 2025","failed","Wed Aug 06 22:19:13 CST 2025","TestEllaEnableUnfreeze","test_enable_unfreeze","",""
"i wanna be rich","17160","测试i wanna be rich能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:00:43 CST 2025","passed","Wed Aug 06 21:01:00 CST 2025","TestEllaIWannaBeRich","test_i_wanna_be_rich","",""
"验证switch to equilibrium mode指令返回预期的不支持响应","0","测试switch to equilibrium mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:07:15 CST 2025","failed","Wed Aug 06 23:07:15 CST 2025","TestEllaSwitchEquilibriumMode","test_switch_to_equilibrium_mode","",""
"why is my phone not ringing on incoming calls","19726","测试why is my phone not ringing on incoming calls能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:16:03 CST 2025","passed","Wed Aug 06 21:16:23 CST 2025","TestEllaWhyIsMyPhoneNotRingingIncomingCalls","test_why_is_my_phone_not_ringing_on_incoming_calls","",""
"pause music","13645","测试pause music能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:39:45 CST 2025","passed","Wed Aug 06 20:39:58 CST 2025","TestEllaPauseMusic","test_pause_music","",""
"download app","15450","测试download app能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 21:49:57 CST 2025","passed","Wed Aug 06 21:50:13 CST 2025","TestEllaDownloadApp","test_download_app","",""
"测试turn off the 7AM alarm指令","16544","测试turn off the 7AM alarm","testcases.test_ella.component_coupling","Wed Aug 06 20:46:37 CST 2025","passed","Wed Aug 06 20:46:54 CST 2025","TestEllaOpenClock","test_turn_off_the_7_am_alarm","",""
"验证reset phone指令返回预期的不支持响应","0","测试reset phone返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:41:38 CST 2025","failed","Wed Aug 06 22:41:38 CST 2025","TestEllaResetPhone","test_reset_phone","",""
"navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai","21432","测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 21:52:07 CST 2025","passed","Wed Aug 06 21:52:28 CST 2025","TestEllaNavigateFromBeijingShanghai","test_navigate_from_beijing_to_shanghai","",""
"验证check my balance of sim1指令返回预期的不支持响应","0","测试check my balance of sim1返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:02:44 CST 2025","failed","Wed Aug 06 22:02:44 CST 2025","TestEllaCheckMyBalanceSim","test_check_my_balance_of_sim","",""
"验证Enable Call on Hold指令返回预期的不支持响应","0","测试Enable Call on Hold返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:16:23 CST 2025","failed","Wed Aug 06 22:16:23 CST 2025","TestEllaEnableCallHold","test_enable_call_on_hold","",""
"what's the weather like in shanghai today","18474","测试what's the weather like in shanghai today能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:12:07 CST 2025","passed","Wed Aug 06 21:12:25 CST 2025","TestEllaWhatSWeatherLikeShanghaiToday","test_what_s_the_weather_like_in_shanghai_today","",""
"验证jump to notifications and status bar settings指令返回预期的不支持响应","0","测试jump to notifications and status bar settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:29:43 CST 2025","failed","Wed Aug 06 22:29:43 CST 2025","TestEllaJumpNotificationsStatusBarSettings","test_jump_to_notifications_and_status_bar_settings","",""
"验证set edge mistouch prevention指令返回预期的不支持响应","0","测试set edge mistouch prevention返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:50:09 CST 2025","failed","Wed Aug 06 22:50:09 CST 2025","TestEllaSetEdgeMistouchPrevention","test_set_edge_mistouch_prevention","",""
"open dialer","22868","测试open dialer能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:37:16 CST 2025","passed","Wed Aug 06 20:37:38 CST 2025","TestEllaCommandConcise","test_open_dialer","",""
"验证how to set screenshots指令返回预期的不支持响应","0","测试how to set screenshots返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:23:45 CST 2025","failed","Wed Aug 06 22:23:45 CST 2025","TestEllaHowSetScreenshots","test_how_to_set_screenshots","",""
"close ella","34604","测试close ella能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:30:18 CST 2025","passed","Wed Aug 06 20:30:52 CST 2025","TestEllaCloseElla","test_close_ella","",""
"how is the weather today","18359","测试how is the weather today能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:57:53 CST 2025","passed","Wed Aug 06 20:58:11 CST 2025","TestEllaHowIsWeatherToday","test_how_is_the_weather_today","",""
"switch to smart charge","13097","测试switch to smart charge能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:39:31 CST 2025","failed","Wed Aug 06 21:39:44 CST 2025","TestEllaSwitchToSmartCharge","test_switch_to_smart_charge","",""
"验证set call back with last used sim指令返回预期的不支持响应","0","测试set call back with last used sim返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:46:44 CST 2025","failed","Wed Aug 06 22:46:44 CST 2025","TestEllaSetCallBackLastUsedSim","test_set_call_back_with_last_used_sim","",""
"continue  screen recording","16924","continue  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:32:48 CST 2025","passed","Wed Aug 06 21:33:05 CST 2025","TestEllaStartScreenRecording","test_start_screen_recording","",""
"boost phone","13977","测试boost phone能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:17:30 CST 2025","passed","Wed Aug 06 21:17:44 CST 2025","TestEllaBoostPhone","test_boost_phone","",""
"what's the wheather today?","0","测试what's the wheather today?能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 23:15:48 CST 2025","failed","Wed Aug 06 23:15:48 CST 2025","TestEllaWhatSWheatherToday","test_what_s_the_wheather_today","",""
"hi","15224","测试hi能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:57:24 CST 2025","passed","Wed Aug 06 20:57:40 CST 2025","TestEllaHi","test_hi","",""
"switch to equilibrium mode","16296","测试switch to equilibrium mode能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:37:08 CST 2025","failed","Wed Aug 06 21:37:25 CST 2025","TestEllaSwitchToEquilibriumMode","test_switch_to_equilibrium_mode","",""
"summarize content on this page","14286","测试summarize content on this page能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:07:55 CST 2025","passed","Wed Aug 06 21:08:10 CST 2025","TestEllaSummarizeContentThisPage","test_summarize_content_on_this_page","",""
"验证jump to high brightness mode settings指令返回预期的不支持响应","0","测试jump to high brightness mode settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:27:54 CST 2025","failed","Wed Aug 06 22:27:54 CST 2025","TestEllaJumpHighBrightnessModeSettings","test_jump_to_high_brightness_mode_settings","",""
"parking space","0","测试parking space能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:35:58 CST 2025","failed","Wed Aug 06 22:35:58 CST 2025","TestEllaParkingSpace","test_parking_space","",""
"验证close equilibrium mode指令返回预期的不支持响应","0","测试close equilibrium mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:04:26 CST 2025","failed","Wed Aug 06 22:04:26 CST 2025","TestEllaCloseEquilibriumMode","test_close_equilibrium_mode","",""
"turn on do not disturb mode","14240","测试turn on do not disturb mode能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:44:47 CST 2025","passed","Wed Aug 06 21:45:01 CST 2025","TestEllaTurnDoNotDisturbMode","test_turn_on_do_not_disturb_mode","",""
"验证set compatibility mode指令返回预期的不支持响应","0","测试set compatibility mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:47:52 CST 2025","failed","Wed Aug 06 22:47:52 CST 2025","TestEllaSetCompatibilityMode","test_set_compatibility_mode","",""
"start walking","0","测试start walking能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 23:06:42 CST 2025","failed","Wed Aug 06 23:06:42 CST 2025","TestEllaStartWalking","test_start_walking","",""
"测试play news指令","16191","测试play news","testcases.test_ella.dialogue","Wed Aug 06 21:03:44 CST 2025","passed","Wed Aug 06 21:04:00 CST 2025","TestEllaOpenPlayNews","test_play_news","",""
"测试pls open whatsapp指令","0","测试pls open whatsapp","testcases.test_ella.unsupported_commands","Wed Aug 06 22:40:30 CST 2025","failed","Wed Aug 06 22:40:30 CST 2025","TestEllaOpenWhatsapp","test_pls_open_whatsapp","",""
"introduce yourself","15496","测试introduce yourself能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:02:15 CST 2025","passed","Wed Aug 06 21:02:30 CST 2025","TestEllaIntroduceYourself","test_introduce_yourself","",""
"where is the carlcare service outlet","15493","测试where is the carlcare service outlet能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:49:29 CST 2025","failed","Wed Aug 06 21:49:44 CST 2025","TestEllaWhereIsCarlcareServiceOutlet","test_where_is_the_carlcare_service_outlet","",""
"stop  screen recording","18479","stop  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:33:18 CST 2025","passed","Wed Aug 06 21:33:36 CST 2025","TestEllaStartScreenRecording","test_start_screen_recording","",""
"check front camera information","19374","测试check front camera information能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:17:57 CST 2025","failed","Wed Aug 06 21:18:16 CST 2025","TestEllaCheckFrontCameraInformation","test_check_front_camera_information","",""
"close whatsapp","14128","测试close whatsapp能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:53:56 CST 2025","passed","Wed Aug 06 20:54:10 CST 2025","TestEllaCloseWhatsapp","test_close_whatsapp","",""
"验证set floating windows指令返回预期的不支持响应","0","测试set floating windows返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:51:51 CST 2025","failed","Wed Aug 06 22:51:51 CST 2025","TestEllaSetFloatingWindows","test_set_floating_windows","",""
"验证set app auto rotate指令返回预期的不支持响应","0","测试set app auto rotate返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:45:02 CST 2025","failed","Wed Aug 06 22:45:02 CST 2025","TestEllaSetAppAutoRotate","test_set_app_auto_rotate","",""
"open facebook","0","测试open facebook能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 21:54:12 CST 2025","failed","Wed Aug 06 21:54:12 CST 2025","TestEllaCommandConcise","test_open_facebook","",""
"验证set flex-still mode指令返回预期的不支持响应","0","测试set flex-still mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:50:43 CST 2025","failed","Wed Aug 06 22:50:43 CST 2025","TestEllaSetFlexStillMode","test_set_flex_still_mode","",""
"测试turn on the alarm at 8 am指令","17123","测试turn on the alarm at 8 am","testcases.test_ella.component_coupling","Wed Aug 06 20:47:36 CST 2025","passed","Wed Aug 06 20:47:53 CST 2025","TestEllaOpenClock","test_turn_on_the_alarm_at_8_am","",""
"验证jump to call notifications指令返回预期的不支持响应","0","测试jump to call notifications返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:27:08 CST 2025","failed","Wed Aug 06 22:27:08 CST 2025","TestEllaJumpCallNotifications","test_jump_to_call_notifications","",""
"decrease the brightness","14457","测试decrease the brightness能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:20:39 CST 2025","passed","Wed Aug 06 21:20:54 CST 2025","TestEllaDecreaseBrightness","test_decrease_the_brightness","",""
"验证Enable Network Enhancement指令返回预期的不支持响应","0","测试Enable Network Enhancement返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:17:31 CST 2025","failed","Wed Aug 06 22:17:31 CST 2025","TestEllaEnableNetworkEnhancement","test_enable_network_enhancement","",""
"测试play the album指令","0","测试play the album","testcases.test_ella.unsupported_commands","Wed Aug 06 22:38:14 CST 2025","failed","Wed Aug 06 22:38:14 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_the_album","",""
"create a metting schedule at tomorrow","14007","测试create a metting schedule at tomorrow能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:32:46 CST 2025","passed","Wed Aug 06 20:33:00 CST 2025","TestEllaCreateMettingScheduleTomorrow","test_create_a_metting_schedule_at_tomorrow","",""
"测试delete the 8 o'clock alarm指令","17000","测试delete the 8 o'clock alarm","testcases.test_ella.component_coupling","Wed Aug 06 20:33:13 CST 2025","passed","Wed Aug 06 20:33:30 CST 2025","TestEllaOpenClock","test_delete_the_8_o_clock_alarm","",""
"验证close power saving mode指令返回预期的不支持响应","0","测试close power saving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:05:34 CST 2025","failed","Wed Aug 06 22:05:34 CST 2025","TestEllaClosePowerSavingMode","test_close_power_saving_mode","",""
"验证disable accelerate dialogue指令返回预期的不支持响应","0","测试disable accelerate dialogue返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:06:08 CST 2025","failed","Wed Aug 06 22:06:08 CST 2025","TestEllaDisableAccelerateDialogue","test_disable_accelerate_dialogue","",""
"switch to flash notification","20040","测试switch to flash notification能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:37:38 CST 2025","failed","Wed Aug 06 21:37:58 CST 2025","TestEllaSwitchToFlashNotification","test_switch_to_flash_notification","",""
"the battery of the mobile phone is too low","25464","测试the battery of the mobile phone is too low能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:41:51 CST 2025","passed","Wed Aug 06 21:42:17 CST 2025","TestEllaBatteryMobilePhoneIsTooLow","test_the_battery_of_the_mobile_phone_is_too_low","",""
"测试open whatsapp指令","0","测试open whatsapp","testcases.test_ella.third_coupling","Wed Aug 06 21:54:46 CST 2025","failed","Wed Aug 06 21:54:46 CST 2025","TestEllaOpenWhatsapp","test_open_whatsapp","",""
"help me take a long screenshot","18212","测试help me take a long screenshot能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:22:07 CST 2025","passed","Wed Aug 06 21:22:26 CST 2025","TestEllaHelpMeTakeLongScreenshot","test_help_me_take_a_long_screenshot","",""
"minimum volume","15503","测试minimum volume能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:24:44 CST 2025","passed","Wed Aug 06 21:24:59 CST 2025","TestEllaMinimumVolume","test_minimum_volume","",""
"验证jump to battery usage指令返回预期的不支持响应","0","测试jump to battery usage返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:26:42 CST 2025","failed","Wed Aug 06 22:26:42 CST 2025","TestEllaJumpBatteryUsage","test_jump_to_battery_usage","",""
"long screenshot","16842","测试long screenshot能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:23:08 CST 2025","passed","Wed Aug 06 21:23:25 CST 2025","TestEllaLongScreenshot","test_long_screenshot","",""
"video call mom through whatsapp","19590","测试video call mom through whatsapp能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:10:38 CST 2025","failed","Wed Aug 06 21:10:58 CST 2025","TestEllaVideoCallMomThroughWhatsapp","test_video_call_mom_through_whatsapp","",""
"验证set ultra power saving指令返回预期的不支持响应","0","测试set ultra power saving返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:05:33 CST 2025","failed","Wed Aug 06 23:05:33 CST 2025","TestEllaSetUltraPowerSaving","test_set_ultra_power_saving","",""
"测试redial指令","0","测试redial","testcases.test_ella.unsupported_commands","Wed Aug 06 22:41:04 CST 2025","failed","Wed Aug 06 22:41:04 CST 2025","TestEllaOpenPlayPoliticalNews","test_redial","",""
"how to say hello in french","11958","测试how to say hello in french能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:59:53 CST 2025","passed","Wed Aug 06 21:00:05 CST 2025","TestEllaHowSayHelloFrench","test_how_to_say_hello_in_french","",""
"验证set date & time指令返回预期的不支持响应","0","测试set date & time返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:49:35 CST 2025","failed","Wed Aug 06 22:49:35 CST 2025","TestEllaSetDateTime","test_set_date_time","",""
"kill whatsapp","0","测试kill whatsapp能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:30:17 CST 2025","failed","Wed Aug 06 22:30:17 CST 2025","TestEllaKillWhatsapp","test_kill_whatsapp","",""
"power saving","25033","测试power saving能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:27:03 CST 2025","passed","Wed Aug 06 21:27:28 CST 2025","TestEllaPowerSaving","test_power_saving","",""
"验证set smart hub指令返回预期的不支持响应","0","测试set smart hub返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:02:42 CST 2025","failed","Wed Aug 06 23:02:42 CST 2025","TestEllaSetSmartHub","test_set_smart_hub","",""
"tell me a joke","15791","测试tell me a joke能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:10:10 CST 2025","failed","Wed Aug 06 21:10:25 CST 2025","TestEllaTellMeJoke","test_tell_me_a_joke","",""
"turn off nfc","15208","测试turn off nfc能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:43:52 CST 2025","passed","Wed Aug 06 21:44:07 CST 2025","TestEllaTurnOffNfc","test_turn_off_nfc","",""
"close aivana","32493","测试close aivana能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:51:35 CST 2025","passed","Wed Aug 06 20:52:08 CST 2025","TestEllaCloseAivana","test_close_aivana","",""
"close flashlight","15940","测试close flashlight能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:19:40 CST 2025","passed","Wed Aug 06 21:19:56 CST 2025","TestEllaCloseFlashlight","test_close_flashlight","",""
"extend the image","0","测试extend the image能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:20:21 CST 2025","failed","Wed Aug 06 22:20:21 CST 2025","TestEllaExtendImage","test_extend_the_image","",""
"how to say i love you in french","12735","测试how to say i love you in french能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:00:18 CST 2025","passed","Wed Aug 06 21:00:30 CST 2025","TestEllaHowSayILoveYouFrench","test_how_to_say_i_love_you_in_french","",""
"can you give me a coin","16593","测试can you give me a coin能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:50:12 CST 2025","passed","Wed Aug 06 20:50:28 CST 2025","TestEllaCanYouGiveMeCoin","test_can_you_give_me_a_coin","",""
"验证check battery information指令返回预期的不支持响应","0","测试check battery information返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 21:59:53 CST 2025","failed","Wed Aug 06 21:59:53 CST 2025","TestEllaCheckBatteryInformation","test_check_battery_information","",""
"summarize what i'm reading","13571","测试summarize what i'm reading能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:08:23 CST 2025","passed","Wed Aug 06 21:08:36 CST 2025","TestEllaSummarizeWhatIMReading","test_summarize_what_i_m_reading","",""
"验证enable touch optimization指令返回预期的不支持响应","0","测试enable touch optimization返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:18:39 CST 2025","failed","Wed Aug 06 22:18:39 CST 2025","TestEllaEnableTouchOptimization","test_enable_touch_optimization","",""
"验证disable auto pickup指令返回预期的不支持响应","0","测试disable auto pickup返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:07:16 CST 2025","failed","Wed Aug 06 22:07:16 CST 2025","TestEllaDisableAutoPickup","test_disable_auto_pickup","",""
"验证disable running lock指令返回预期的不支持响应","0","测试disable running lock返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:10:41 CST 2025","failed","Wed Aug 06 22:10:41 CST 2025","TestEllaDisableRunningLock","test_disable_running_lock","",""
"验证check model information指令返回预期的不支持响应","0","测试check model information返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:02:10 CST 2025","failed","Wed Aug 06 22:02:10 CST 2025","TestEllaCheckModelInformation","test_check_model_information","",""
"change (female/tone name) voice","1","测试change (female/tone name) voice能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 21:58:11 CST 2025","failed","Wed Aug 06 21:58:11 CST 2025","TestEllaChangeFemaleToneNameVoice","test_change_female_tone_name_voice","",""
"stop  screen recording","17423","stop  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:28:13 CST 2025","passed","Wed Aug 06 21:28:31 CST 2025","TestEllaScreenRecord","test_screen_record","",""
"验证set flip case feature指令返回预期的不支持响应","0","测试set flip case feature返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:51:17 CST 2025","failed","Wed Aug 06 22:51:17 CST 2025","TestEllaSetFlipCaseFeature","test_set_flip_case_feature","",""
"phone boost","14611","测试phone boost能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:40:38 CST 2025","passed","Wed Aug 06 20:40:52 CST 2025","TestEllaPhoneBoost","test_phone_boost","",""
"help me write an email","0","测试help me write an email能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 22:22:03 CST 2025","failed","Wed Aug 06 22:22:03 CST 2025","TestEllaHelpMeWriteAnEmail","test_help_me_write_an_email","",""
"next channel","13961","测试next channel能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:34:46 CST 2025","passed","Wed Aug 06 20:35:00 CST 2025","TestEllaNextChannel","test_next_channel","",""
"i want to watch fireworks","16375","测试i want to watch fireworks能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:01:45 CST 2025","passed","Wed Aug 06 21:02:02 CST 2025","TestEllaIWantWatchFireworks","test_i_want_to_watch_fireworks","",""
"验证set phone number指令返回预期的不支持响应","0","测试set phone number返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:58:41 CST 2025","failed","Wed Aug 06 22:58:41 CST 2025","TestEllaSetPhoneNumber","test_set_phone_number","",""
"验证set screen refresh rate指令返回预期的不支持响应","0","测试set screen refresh rate返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:59:49 CST 2025","failed","Wed Aug 06 22:59:49 CST 2025","TestEllaSetScreenRefreshRate","test_set_screen_refresh_rate","",""
"验证disable zonetouch master指令返回预期的不支持响应","0","测试disable zonetouch master返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:12:25 CST 2025","failed","Wed Aug 06 22:12:25 CST 2025","TestEllaDisableZonetouchMaster","test_disable_zonetouch_master","",""
"验证enable accelerate dialogue指令返回预期的不支持响应","0","测试enable accelerate dialogue返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:14:07 CST 2025","failed","Wed Aug 06 22:14:07 CST 2025","TestEllaEnableAccelerateDialogue","test_enable_accelerate_dialogue","",""
"验证set my fonts指令返回预期的不支持响应","1","测试set my fonts返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:55:18 CST 2025","failed","Wed Aug 06 22:55:18 CST 2025","TestEllaSetMyFonts","test_set_my_fonts","",""
"turn down ring volume","13882","测试turn down ring volume能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:42:30 CST 2025","failed","Wed Aug 06 21:42:44 CST 2025","TestEllaTurnDownRingVolume","test_turn_down_ring_volume","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","21518","测试open contact命令","testcases.test_ella.component_coupling","Wed Aug 06 20:36:14 CST 2025","passed","Wed Aug 06 20:36:36 CST 2025","TestEllaContactCommandConcise","test_open_contact","",""
"stop  screen recording","15162","stop  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:34:21 CST 2025","passed","Wed Aug 06 21:34:36 CST 2025","TestEllaTurnScreenRecord","test_stop_recording","",""
"turn on the screen record","17524","测试turn on the screen record能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:33:50 CST 2025","passed","Wed Aug 06 21:34:07 CST 2025","TestEllaTurnScreenRecord","test_stop_recording","",""
"what time is it now","13432","测试what time is it now能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:14:06 CST 2025","passed","Wed Aug 06 21:14:19 CST 2025","TestEllaWhatTimeIsItNow","test_what_time_is_it_now","",""
"验证turn on driving mode指令返回预期的不支持响应","0","测试turn on driving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:12:23 CST 2025","failed","Wed Aug 06 23:12:23 CST 2025","TestEllaTurnDrivingMode","test_turn_on_driving_mode","",""
"open camera","17578","测试open camera能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:35:12 CST 2025","passed","Wed Aug 06 20:35:30 CST 2025","TestEllaCommandConcise","test_open_camera","",""
"验证set my themes指令返回预期的不支持响应","0","测试set my themes返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:55:51 CST 2025","failed","Wed Aug 06 22:55:51 CST 2025","TestEllaSetMyThemes","test_set_my_themes","",""
"验证set app notifications指令返回预期的不支持响应","0","测试set app notifications返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:45:36 CST 2025","failed","Wed Aug 06 22:45:36 CST 2025","TestEllaSetAppNotifications","test_set_app_notifications","",""
"测试turn off the 8 am alarm指令","16131","测试turn off the 8 am alarm","testcases.test_ella.component_coupling","Wed Aug 06 20:47:07 CST 2025","passed","Wed Aug 06 20:47:23 CST 2025","TestEllaOpenClock","test_turn_off_the_8_am_alarm","",""
"验证set cover screen apps指令返回预期的不支持响应","0","测试set cover screen apps返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:48:27 CST 2025","failed","Wed Aug 06 22:48:27 CST 2025","TestEllaSetCoverScreenApps","test_set_cover_screen_apps","",""
"change man voice","0","测试change man voice能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 21:58:45 CST 2025","failed","Wed Aug 06 21:58:45 CST 2025","TestEllaChangeManVoice","test_change_man_voice","",""
"call mom through whatsapp","21061","测试call mom through whatsapp能正常执行","testcases.test_ella.dialogue","Wed Aug 06 20:49:38 CST 2025","failed","Wed Aug 06 20:49:59 CST 2025","TestEllaCallMomThroughWhatsapp","test_call_mom_through_whatsapp","",""
"验证set languages指令返回预期的不支持响应","0","测试set languages返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:54:09 CST 2025","failed","Wed Aug 06 22:54:09 CST 2025","TestEllaSetLanguages","test_set_languages","",""
"download basketball","14273","测试download basketball能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 21:50:26 CST 2025","failed","Wed Aug 06 21:50:40 CST 2025","TestEllaDownloadBasketball","test_download_basketball","",""
"take a photo","30469","测试take a photo能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:40:24 CST 2025","failed","Wed Aug 06 21:40:55 CST 2025","TestEllaTakePhoto","test_take_a_photo","",""
"Switch to Hyper Charge","13112","测试Switch to Hyper Charge能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:38:11 CST 2025","failed","Wed Aug 06 21:38:24 CST 2025","TestEllaSwitchToHyperCharge","test_switch_to_hyper_charge","",""
"set a timer for 10 minutes","17853","测试set a timer for 10 minutes能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:28:43 CST 2025","passed","Wed Aug 06 21:29:01 CST 2025","TestEllaSetTimerMinutes","test_set_a_timer_for_minutes","",""
"测试pls open the newest whatsapp activity指令","0","测试pls open the newest whatsapp activity","testcases.test_ella.third_coupling","Wed Aug 06 21:56:28 CST 2025","failed","Wed Aug 06 21:56:28 CST 2025","TestEllaOpenPlsNewestWhatsappActivity","test_pls_open_the_newest_whatsapp_activity","",""
"验证turn off show battery percentage指令返回预期的不支持响应","0","测试turn off show battery percentage返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:11:49 CST 2025","failed","Wed Aug 06 23:11:49 CST 2025","TestEllaTurnOffShowBatteryPercentage","test_turn_off_show_battery_percentage","",""
"pause fm","13295","测试pause fm能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:39:19 CST 2025","passed","Wed Aug 06 20:39:32 CST 2025","TestEllaPauseFm","test_pause_fm","",""
"验证set lockscreen passwords指令返回预期的不支持响应","0","测试set lockscreen passwords返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:54:43 CST 2025","failed","Wed Aug 06 22:54:43 CST 2025","TestEllaSetLockscreenPasswords","test_set_lockscreen_passwords","",""
"start record","17025","测试start record能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:30:46 CST 2025","passed","Wed Aug 06 21:31:03 CST 2025","TestEllaStartRecord","test_start_record","",""
"wake me up at 7:00 am tomorrow","13315","测试wake me up at 7:00 am tomorrow能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:49:02 CST 2025","passed","Wed Aug 06 21:49:16 CST 2025","TestEllaWakeMeUpAmTomorrow","test_wake_me_up_at_am_tomorrow","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","14425","测试open contact命令 - 简洁版本","testcases.test_ella.dialogue","Wed Aug 06 21:03:16 CST 2025","passed","Wed Aug 06 21:03:31 CST 2025","TestEllaCommandConcise","test_open_app","",""
"测试play taylor swift‘s song love story指令","0","测试play taylor swift‘s song love story","testcases.test_ella.unsupported_commands","Wed Aug 06 22:37:41 CST 2025","failed","Wed Aug 06 22:37:41 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_taylor_swift_s_song_love_sotry","",""
"验证increase settings for special functions指令返回预期的不支持响应","0","测试increase settings for special functions返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:24:19 CST 2025","failed","Wed Aug 06 22:24:19 CST 2025","TestEllaIncreaseSettingsSpecialFunctions","test_increase_settings_for_special_functions","",""
"测试play afro strut指令","21058","测试play afro strut","testcases.test_ella.component_coupling","Wed Aug 06 20:41:05 CST 2025","passed","Wed Aug 06 20:41:26 CST 2025","TestEllaOpenPlayAfroStrut","test_play_afro_strut","",""
"测试play jay chou's music指令","18609","测试play jay chou's music","testcases.test_ella.component_coupling","Wed Aug 06 20:41:39 CST 2025","passed","Wed Aug 06 20:41:58 CST 2025","TestEllaOpenMusic","test_play_jay_chou_s_music","",""
"close aivana","35096","测试close aivana能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 20:29:30 CST 2025","passed","Wed Aug 06 20:30:05 CST 2025","TestEllaCloseAivana","test_close_aivana","",""
"pause screen recording","16802","测试pause screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 21:32:18 CST 2025","passed","Wed Aug 06 21:32:35 CST 2025","TestEllaStartScreenRecording","test_start_screen_recording","",""
"navigation to the lucky","0","测试navigation to the lucky能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 21:53:38 CST 2025","failed","Wed Aug 06 21:53:38 CST 2025","TestEllaNavigationToTheLucky","test_navigation_to_the_lucky","",""
"What languages do you support","13274","测试What languages do you support能正常执行","testcases.test_ella.dialogue","Wed Aug 06 21:11:41 CST 2025","passed","Wed Aug 06 21:11:54 CST 2025","TestEllaWhatLanguagesDoYouSupport","test_what_languages_do_you_support","",""
"验证disable call rejection指令返回预期的不支持响应","0","测试disable call rejection返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 22:08:24 CST 2025","failed","Wed Aug 06 22:08:24 CST 2025","TestEllaDisableCallRejection","test_disable_call_rejection","",""
"验证switch to performance mode指令返回预期的不支持响应","0","测试switch to performance mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 23:07:50 CST 2025","failed","Wed Aug 06 23:07:50 CST 2025","TestEllaSwitchPerformanceMode","test_switch_to_performance_mode","",""
