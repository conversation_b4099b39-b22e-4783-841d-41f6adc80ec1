{"uid": "83edc06c07f9ae9e47eb6dd1b683e4e2", "name": "packages", "children": [{"name": "testcases.test_ella", "children": [{"name": "component_coupling", "children": [{"name": "test_close_aivana", "children": [{"name": "测试close aivana能正常执行", "uid": "b2ff483ff849f8d3", "parentUid": "0a654e8ba5d406f4dced08e54877f4f8", "status": "passed", "time": {"start": 1754483370298, "stop": 1754483405394, "duration": 35096}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0a654e8ba5d406f4dced08e54877f4f8"}, {"name": "test_close_ella", "children": [{"name": "测试close ella能正常执行", "uid": "8c0c6fe9d66425ac", "parentUid": "b5a2cabf288d90878566c5ce33227175", "status": "passed", "time": {"start": 1754483418169, "stop": 1754483452773, "duration": 34604}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b5a2cabf288d90878566c5ce33227175"}, {"name": "test_close_folax", "children": [{"name": "测试close folax能正常执行", "uid": "eec3159f625e1454", "parentUid": "3b1f73acd32162c3bc6cd626dc7f1272", "status": "passed", "time": {"start": 1754483465644, "stop": 1754483500756, "duration": 35112}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3b1f73acd32162c3bc6cd626dc7f1272"}, {"name": "test_close_phonemaster", "children": [{"name": "测试close phonemaster能正常执行", "uid": "4519632c93b2a5c3", "parentUid": "e2aa70cb4f489d6ed135a8d54afbd69e", "status": "passed", "time": {"start": 1754483513800, "stop": 1754483527132, "duration": 13332}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e2aa70cb4f489d6ed135a8d54afbd69e"}, {"name": "test_continue_music", "children": [{"name": "测试continue music能正常执行", "uid": "150527037135ce30", "parentUid": "6a23546ce5380ec0616c9f82a57a36ad", "status": "passed", "time": {"start": 1754483540136, "stop": 1754483553519, "duration": 13383}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a23546ce5380ec0616c9f82a57a36ad"}, {"name": "test_create_a_metting_schedule_at_tomorrow", "children": [{"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "51b5e93e599225aa", "parentUid": "6ef10c8d834a3fd9e2b41931a6d5b667", "status": "passed", "time": {"start": 1754483566493, "stop": 1754483580500, "duration": 14007}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6ef10c8d834a3fd9e2b41931a6d5b667"}, {"name": "test_delete_the_8_o_clock_alarm", "children": [{"name": "测试delete the 8 o'clock alarm", "uid": "50935baf36d1199b", "parentUid": "744e7a2785bdcb064b4cb0104fffb47a", "status": "passed", "time": {"start": 1754483593381, "stop": 1754483610381, "duration": 17000}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "744e7a2785bdcb064b4cb0104fffb47a"}, {"name": "test_display_the_route_go_company", "children": [{"name": "测试display the route go company", "uid": "f6b10dc3e1bba4eb", "parentUid": "e22797f7f98b084598882fb5cec440fe", "status": "failed", "time": {"start": 1754483623139, "stop": 1754483645506, "duration": 22367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e22797f7f98b084598882fb5cec440fe"}, {"name": "test_my_phone_is_too_slow", "children": [{"name": "测试my phone is too slow能正常执行", "uid": "498c625d54cac8e7", "parentUid": "9df0629aeb726d588255055629526bcb", "status": "passed", "time": {"start": 1754483658953, "stop": 1754483673157, "duration": 14204}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9df0629aeb726d588255055629526bcb"}, {"name": "test_next_channel", "children": [{"name": "测试next channel能正常执行", "uid": "c38fd4800ac0add4", "parentUid": "3f23899abed5acee33c72c68b61ec080", "status": "passed", "time": {"start": 1754483686048, "stop": 1754483700009, "duration": 13961}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f23899abed5acee33c72c68b61ec080"}, {"name": "test_open_camera", "children": [{"name": "测试open camera能正常执行", "uid": "8619be41c629b8ca", "parentUid": "470a45e0cfa4244680b09ac2a8c782a2", "status": "passed", "time": {"start": 1754483712861, "stop": 1754483730439, "duration": 17578}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "470a45e0cfa4244680b09ac2a8c782a2"}, {"name": "test_open_clock", "children": [{"name": "open clock", "uid": "c7f9d3989b7f3cb6", "parentUid": "50daf9198779f130ca593a54f87ec128", "status": "passed", "time": {"start": 1754483743447, "stop": 1754483761737, "duration": 18290}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "50daf9198779f130ca593a54f87ec128"}, {"name": "test_open_contact", "children": [{"name": "测试open contact命令", "uid": "712b502d18be3f07", "parentUid": "c18b5d7a76803510c6e0d2868a096cc6", "status": "passed", "time": {"start": 1754483774953, "stop": 1754483796471, "duration": 21518}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c18b5d7a76803510c6e0d2868a096cc6"}, {"name": "test_open_countdown", "children": [{"name": "测试open countdown能正常执行", "uid": "308b4d5a081a98be", "parentUid": "34dd669acbf251ecc3b4bcb8039abeb9", "status": "failed", "time": {"start": 1754483809387, "stop": 1754483822953, "duration": 13566}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34dd669acbf251ecc3b4bcb8039abeb9"}, {"name": "test_open_dialer", "children": [{"name": "测试open dialer能正常执行", "uid": "eafb693ce639dfd3", "parentUid": "9072ee3e27d0b6c6a2866a718c2df078", "status": "passed", "time": {"start": 1754483836056, "stop": 1754483858924, "duration": 22868}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9072ee3e27d0b6c6a2866a718c2df078"}, {"name": "test_open_ella", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "9fa3e1d3cfd691ca", "parentUid": "4ee54c4b3bb105e867eb3473949bd494", "status": "passed", "time": {"start": 1754483871917, "stop": 1754483884764, "duration": 12847}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4ee54c4b3bb105e867eb3473949bd494"}, {"name": "test_open_folax", "children": [{"name": "测试open folax能正常执行", "uid": "912cd52a27bc4c68", "parentUid": "e02fc6e8f5b7a4fa147ce01474445b5b", "status": "passed", "time": {"start": 1754483897917, "stop": 1754483910207, "duration": 12290}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e02fc6e8f5b7a4fa147ce01474445b5b"}, {"name": "test_open_phone", "children": [{"name": "测试open contact命令", "uid": "a410281354d5dfa1", "parentUid": "342f26d794168c8355629898d89335fe", "status": "passed", "time": {"start": 1754483923329, "stop": 1754483946357, "duration": 23028}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "342f26d794168c8355629898d89335fe"}, {"name": "test_pause_fm", "children": [{"name": "测试pause fm能正常执行", "uid": "27db06ac743e18ff", "parentUid": "83a06f2be77f3b44f6a099f43b575dbf", "status": "passed", "time": {"start": 1754483959022, "stop": 1754483972317, "duration": 13295}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83a06f2be77f3b44f6a099f43b575dbf"}, {"name": "test_pause_music", "children": [{"name": "测试pause music能正常执行", "uid": "a0a18ec854bf4f4", "parentUid": "a3026ab8ba7d276b771465a73b8cd69f", "status": "passed", "time": {"start": 1754483985350, "stop": 1754483998995, "duration": 13645}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3026ab8ba7d276b771465a73b8cd69f"}, {"name": "test_pause_song", "children": [{"name": "测试pause song能正常执行", "uid": "fdf93e9017e25b95", "parentUid": "1a65a9bcb8b35f4b071220b80ad32958", "status": "failed", "time": {"start": 1754484011846, "stop": 1754484024923, "duration": 13077}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1a65a9bcb8b35f4b071220b80ad32958"}, {"name": "test_phone_boost", "children": [{"name": "测试phone boost能正常执行", "uid": "22b2ff639242008b", "parentUid": "e9cea1e20f3779ed164d139ee8b570ab", "status": "passed", "time": {"start": 1754484038030, "stop": 1754484052641, "duration": 14611}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9cea1e20f3779ed164d139ee8b570ab"}, {"name": "test_play_afro_strut", "children": [{"name": "测试play afro strut", "uid": "1c358ec780500fd7", "parentUid": "51e1e1cd02cc1154b480c322b15c41c7", "status": "passed", "time": {"start": 1754484065513, "stop": 1754484086571, "duration": 21058}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "51e1e1cd02cc1154b480c322b15c41c7"}, {"name": "test_play_jay_chou_s_music", "children": [{"name": "测试play jay chou's music", "uid": "8b0780f7c44c1094", "parentUid": "80ffd53a52b22963552b5b4f8ad4a69c", "status": "passed", "time": {"start": 1754484099673, "stop": 1754484118282, "duration": 18609}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80ffd53a52b22963552b5b4f8ad4a69c"}, {"name": "test_play_jay_chou_s_music_by_spotify", "children": [{"name": "测试play jay chou's music by spotify", "uid": "785b6165f13072c3", "parentUid": "fb3c6fdc98f3f31c0cad30d7f977b25b", "status": "passed", "time": {"start": 1754484131304, "stop": 1754484148477, "duration": 17173}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb3c6fdc98f3f31c0cad30d7f977b25b"}, {"name": "test_play_music", "children": [{"name": "测试play music", "uid": "e5b4b544bc7f272c", "parentUid": "012cce9a22125b4f9274376f463a21a6", "status": "passed", "time": {"start": 1754484161498, "stop": 1754484179971, "duration": 18473}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "012cce9a22125b4f9274376f463a21a6"}, {"name": "test_play_rock_music", "children": [{"name": "测试play rock music", "uid": "16387a05e5a29fcb", "parentUid": "5fe073acfe44a1d8a05ea050b2df638f", "status": "passed", "time": {"start": 1754484192926, "stop": 1754484212122, "duration": 19196}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5fe073acfe44a1d8a05ea050b2df638f"}, {"name": "test_play_sun_be_song_of_jide_chord", "children": [{"name": "测试play sun be song of jide chord", "uid": "1a3d1f5185aaa6ee", "parentUid": "e61c3229af1d310b3286353485e7dc26", "status": "passed", "time": {"start": 1754484225076, "stop": 1754484244905, "duration": 19829}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e61c3229af1d310b3286353485e7dc26"}, {"name": "test_previous_music", "children": [{"name": "测试previous music能正常执行", "uid": "f261e24a4d6c223d", "parentUid": "a7e1f637551ed2330d6d67ed54a71f33", "status": "passed", "time": {"start": 1754484257844, "stop": 1754484272643, "duration": 14799}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a7e1f637551ed2330d6d67ed54a71f33"}, {"name": "test_resume_music", "children": [{"name": "测试resume music能正常执行", "uid": "59a2f1889b5d1733", "parentUid": "0f680c0e3c91eaed54cda04d37208ed3", "status": "failed", "time": {"start": 1754484285303, "stop": 1754484298395, "duration": 13092}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0f680c0e3c91eaed54cda04d37208ed3"}, {"name": "test_set_an_alarm_at_8_am", "children": [{"name": "测试set an alarm at 8 am", "uid": "b54d405883275b27", "parentUid": "934efa6c18aa0ed520853ffbd4ef3bd7", "status": "passed", "time": {"start": 1754484311536, "stop": 1754484328606, "duration": 17070}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "934efa6c18aa0ed520853ffbd4ef3bd7"}, {"name": "test_stop_playing", "children": [{"name": "测试stop playing", "uid": "f6c9439dfc63e147", "parentUid": "c598fac3e058ad96b70d8acf40dc13d8", "status": "failed", "time": {"start": 1754484341682, "stop": 1754484356574, "duration": 14892}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c598fac3e058ad96b70d8acf40dc13d8"}, {"name": "test_take_a_screenshot", "children": [{"name": "测试take a screenshot能正常执行", "uid": "3e171de0d43a13", "parentUid": "c1793294d28b52937b4f0aa9a0fee952", "status": "failed", "time": {"start": 1754484369610, "stop": 1754484384634, "duration": 15024}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c1793294d28b52937b4f0aa9a0fee952"}, {"name": "test_turn_off_the_7_am_alarm", "children": [{"name": "测试turn off the 7AM alarm", "uid": "62059e19cf44d484", "parentUid": "32bfb55cdaddbf6d8fd789b810c908d9", "status": "passed", "time": {"start": 1754484397719, "stop": 1754484414263, "duration": 16544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "32bfb55cdaddbf6d8fd789b810c908d9"}, {"name": "test_turn_off_the_8_am_alarm", "children": [{"name": "测试turn off the 8 am alarm", "uid": "b2f989e8480f8e5", "parentUid": "568593413cf95986c665f08593ea21e9", "status": "passed", "time": {"start": 1754484427410, "stop": 1754484443541, "duration": 16131}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "568593413cf95986c665f08593ea21e9"}, {"name": "test_turn_on_the_alarm_at_8_am", "children": [{"name": "测试turn on the alarm at 8 am", "uid": "d4ef5f9069b81e12", "parentUid": "739e8257af5cd9a36c4e8e1ddaad5c24", "status": "passed", "time": {"start": 1754484456577, "stop": 1754484473700, "duration": 17123}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "739e8257af5cd9a36c4e8e1ddaad5c24"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "测试What's the weather like in Shanghai today能正常执行", "uid": "9c7363095b3d6839", "parentUid": "85d39690a527a90b7d30ade2fdd4a8fc", "status": "passed", "time": {"start": 1754484486581, "stop": 1754484507641, "duration": 21060}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "85d39690a527a90b7d30ade2fdd4a8fc"}], "uid": "155057a0afc640ed94e8c8d7c444a680"}, {"name": "dialogue", "children": [{"name": "test_appeler_maman", "children": [{"name": "测试appeler maman能正常执行", "uid": "dae71633949bd42a", "parentUid": "7954f72724ef95cff1433b0efbee5646", "status": "passed", "time": {"start": 1754484520617, "stop": 1754484535025, "duration": 14408}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7954f72724ef95cff1433b0efbee5646"}, {"name": "test_book_a_flight_to_paris", "children": [{"name": "测试book a flight to paris返回正确的不支持响应", "uid": "e11b0aebcd0a881b", "parentUid": "8f0bd634d6e7676a3a49d6dff49497fa", "status": "broken", "time": {"start": 1754484547829, "stop": 1754484565109, "duration": 17280}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8f0bd634d6e7676a3a49d6dff49497fa"}, {"name": "test_call_mom_through_whatsapp", "children": [{"name": "测试call mom through whatsapp能正常执行", "uid": "9a1de76db3282d62", "parentUid": "8541ffcf150af606538b232e3aea7434", "status": "failed", "time": {"start": 1754484578227, "stop": 1754484599288, "duration": 21061}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8541ffcf150af606538b232e3aea7434"}, {"name": "test_can_you_give_me_a_coin", "children": [{"name": "测试can you give me a coin能正常执行", "uid": "8120a0b0eacc9587", "parentUid": "7e22a70303a1dcd81523a4e5a786a8c4", "status": "passed", "time": {"start": 1754484612243, "stop": 1754484628836, "duration": 16593}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7e22a70303a1dcd81523a4e5a786a8c4"}, {"name": "test_cannot_login_in_google_email_box", "children": [{"name": "测试cannot login in google email box能正常执行", "uid": "b686b089296f4044", "parentUid": "8c0c4d699c270397d14ca2a01dc1ee04", "status": "passed", "time": {"start": 1754484641769, "stop": 1754484655486, "duration": 13717}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c0c4d699c270397d14ca2a01dc1ee04"}, {"name": "test_check_status_updates_on_whatsapp", "children": [{"name": "测试check status updates on whatsapp能正常执行", "uid": "ee7c652600fcf1ec", "parentUid": "4ea0f2069633d579cb2aa7e3f4dee562", "status": "passed", "time": {"start": 1754484668536, "stop": 1754484682953, "duration": 14417}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4ea0f2069633d579cb2aa7e3f4dee562"}, {"name": "test_close_aivana", "children": [{"name": "测试close aivana能正常执行", "uid": "5dac0aba48d9341d", "parentUid": "3cff6af8c3e7032cf8ac49aea279e1f2", "status": "passed", "time": {"start": 1754484695704, "stop": 1754484728197, "duration": 32493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3cff6af8c3e7032cf8ac49aea279e1f2"}, {"name": "test_close_ella", "children": [{"name": "测试close ella能正常执行", "uid": "560b5c32ce990b3a", "parentUid": "55271d38aa92f9a1a399fabfe12bd0fb", "status": "passed", "time": {"start": 1754484741120, "stop": 1754484775039, "duration": 33919}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "55271d38aa92f9a1a399fabfe12bd0fb"}, {"name": "test_close_folax", "children": [{"name": "测试close folax能正常执行", "uid": "84f2ec66ef73d014", "parentUid": "5705d372510031304a6c711ae06b3708", "status": "passed", "time": {"start": 1754484788120, "stop": 1754484823514, "duration": 35394}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5705d372510031304a6c711ae06b3708"}, {"name": "test_close_whatsapp", "children": [{"name": "测试close whatsapp能正常执行", "uid": "84cdaf12b67214f6", "parentUid": "254be578575e3bd49816bef9d50f165f", "status": "passed", "time": {"start": 1754484836336, "stop": 1754484850464, "duration": 14128}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "254be578575e3bd49816bef9d50f165f"}, {"name": "test_could_you_please_search_an_for_me", "children": [{"name": "测试could you please search an for me能正常执行", "uid": "f990be4b0bb6b336", "parentUid": "7327508f376f094b8e8fcbe0310288ab", "status": "failed", "time": {"start": 1754484863011, "stop": 1754484878667, "duration": 15656}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7327508f376f094b8e8fcbe0310288ab"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "测试disable magic voice changer能正常执行", "uid": "21f356a729ddd2", "parentUid": "3622231925f5d7d87dfc0a1200f98b66", "status": "passed", "time": {"start": 1754484891757, "stop": 1754484905166, "duration": 13409}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3622231925f5d7d87dfc0a1200f98b66"}, {"name": "test_give_me_some_money", "children": [{"name": "测试give me some money能正常执行", "uid": "a2c6fbbe98591301", "parentUid": "690fab1bc7060b99c06d337a10d44d74", "status": "passed", "time": {"start": 1754484917871, "stop": 1754484935237, "duration": 17366}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "690fab1bc7060b99c06d337a10d44d74"}, {"name": "test_global_gdp_trends", "children": [{"name": "测试global gdp trends能正常执行", "uid": "67c045159680a9ba", "parentUid": "76c541180f5aa46cc8bcc707c7d9c16f", "status": "passed", "time": {"start": 1754484947633, "stop": 1754484964615, "duration": 16982}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "76c541180f5aa46cc8bcc707c7d9c16f"}, {"name": "test_hello_hello", "children": [{"name": "测试hello hello能正常执行", "uid": "f297484465772a4b", "parentUid": "b9ee9e8a38234ec69db09f78700d7fbb", "status": "passed", "time": {"start": 1754484977458, "stop": 1754484992659, "duration": 15201}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b9ee9e8a38234ec69db09f78700d7fbb"}, {"name": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "children": [{"name": "测试Help me write an email to make an appointment for a visit能正常执行", "uid": "bc549bf9de755daa", "parentUid": "c54c88063ac1edf2e569991ff95d16e9", "status": "passed", "time": {"start": 1754485005516, "stop": 1754485032127, "duration": 26611}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c54c88063ac1edf2e569991ff95d16e9"}, {"name": "test_hi", "children": [{"name": "测试hi能正常执行", "uid": "5ea29cde835aec90", "parentUid": "acfbc4c8f3f5adee7b09787ceaa2dd0d", "status": "passed", "time": {"start": 1754485044995, "stop": 1754485060219, "duration": 15224}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "acfbc4c8f3f5adee7b09787ceaa2dd0d"}, {"name": "test_how_is_the_weather_today", "children": [{"name": "测试how is the weather today能正常执行", "uid": "4f2d2b6c9aaf70e6", "parentUid": "35fc4938c7d094c99682c376792faffe", "status": "passed", "time": {"start": 1754485073283, "stop": 1754485091642, "duration": 18359}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "35fc4938c7d094c99682c376792faffe"}, {"name": "test_how_is_the_wheather_today", "children": [{"name": "测试how is the wheather today能正常执行", "uid": "9925076591462c7e", "parentUid": "ccd640573d805b985ffb9441df6e5fe3", "status": "passed", "time": {"start": 1754485104484, "stop": 1754485118022, "duration": 13538}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ccd640573d805b985ffb9441df6e5fe3"}, {"name": "test_how_s_the_weather_today", "children": [{"name": "测试how's the weather today?返回正确的不支持响应", "uid": "98a23a7493ce922d", "parentUid": "addae1e5b6cb9e45cb30b52b0519d0df", "status": "passed", "time": {"start": 1754485130948, "stop": 1754485148953, "duration": 18005}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "addae1e5b6cb9e45cb30b52b0519d0df"}, {"name": "test_how_s_the_weather_today_in_shanghai", "children": [{"name": "测试how's the weather today in shanghai能正常执行", "uid": "8cc2b9f9f777deb5", "parentUid": "5cbfc9081ff73a6bd530b2831a9e3384", "status": "passed", "time": {"start": 1754485161947, "stop": 1754485180586, "duration": 18639}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cbfc9081ff73a6bd530b2831a9e3384"}, {"name": "test_how_to_say_hello_in_french", "children": [{"name": "测试how to say hello in french能正常执行", "uid": "3b03094079bed1b9", "parentUid": "58f878e12ecf6b0d82d8bb49999f4d47", "status": "passed", "time": {"start": 1754485193306, "stop": 1754485205264, "duration": 11958}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58f878e12ecf6b0d82d8bb49999f4d47"}, {"name": "test_how_to_say_i_love_you_in_french", "children": [{"name": "测试how to say i love you in french能正常执行", "uid": "e1e7bd584d2312c5", "parentUid": "82fbe34286468477e9134feb14dde382", "status": "passed", "time": {"start": 1754485218115, "stop": 1754485230850, "duration": 12735}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "82fbe34286468477e9134feb14dde382"}, {"name": "test_i_wanna_be_rich", "children": [{"name": "测试i wanna be rich能正常执行", "uid": "bfef13c8ba7b18c5", "parentUid": "e8c194f64f4bc6de5e59e9c7b048cfe4", "status": "passed", "time": {"start": 1754485243754, "stop": 1754485260914, "duration": 17160}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e8c194f64f4bc6de5e59e9c7b048cfe4"}, {"name": "test_i_want_to_make_a_call", "children": [{"name": "测试i want to make a call能正常执行", "uid": "a101b5b4fa37ac44", "parentUid": "3a5af3715ffe87f7125f4fc9d4a39a79", "status": "passed", "time": {"start": 1754485273623, "stop": 1754485293097, "duration": 19474}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3a5af3715ffe87f7125f4fc9d4a39a79"}, {"name": "test_i_want_to_watch_fireworks", "children": [{"name": "测试i want to watch fireworks能正常执行", "uid": "a51394d164c1c753", "parentUid": "2d9bf69b53882c59bc26f9d5bf23b4c7", "status": "passed", "time": {"start": 1754485305791, "stop": 1754485322166, "duration": 16375}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d9bf69b53882c59bc26f9d5bf23b4c7"}, {"name": "test_introduce_yourself", "children": [{"name": "测试introduce yourself能正常执行", "uid": "191338734ff35e0", "parentUid": "472715b25ea329acf3f408d9a469274a", "status": "passed", "time": {"start": 1754485335076, "stop": 1754485350572, "duration": 15496}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "472715b25ea329acf3f408d9a469274a"}, {"name": "test_make_a_call", "children": [{"name": "测试make a call能正常执行", "uid": "4515fe1422f6f89b", "parentUid": "11ee179329ce36ecafe38100e52ec70d", "status": "passed", "time": {"start": 1754485363318, "stop": 1754485383716, "duration": 20398}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11ee179329ce36ecafe38100e52ec70d"}, {"name": "test_open_app", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "423f6df0e82ce860", "parentUid": "c969d4a520c25845a9e33a24d4e3b7a4", "status": "passed", "time": {"start": 1754485396679, "stop": 1754485411104, "duration": 14425}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c969d4a520c25845a9e33a24d4e3b7a4"}, {"name": "test_play_news", "children": [{"name": "测试play news", "uid": "10caeac7f5359c96", "parentUid": "0545a1fc4a946cb1c690d92255face5c", "status": "passed", "time": {"start": 1754485424127, "stop": 1754485440318, "duration": 16191}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0545a1fc4a946cb1c690d92255face5c"}, {"name": "test_play_political_news", "children": [{"name": "测试play political news", "uid": "eaf54f5c3ee7d63", "parentUid": "350bcef11f4fb6eb99f97d3fa6fe1d47", "status": "passed", "time": {"start": 1754485453466, "stop": 1754485469430, "duration": 15964}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "350bcef11f4fb6eb99f97d3fa6fe1d47"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "测试searching for a method of violent murder能正常执行", "uid": "a34c28ac75d4d56f", "parentUid": "f3e7c7fb3655fd1be27c6d251a6f492c", "status": "broken", "time": {"start": 1754485482383, "stop": 1754485498034, "duration": 15651}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f3e7c7fb3655fd1be27c6d251a6f492c"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "aa18187ae31ed8c0", "parentUid": "11264fcbfd285641cac64cf3c0f1f4a8", "status": "failed", "time": {"start": 1754485511220, "stop": 1754485527264, "duration": 16044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11264fcbfd285641cac64cf3c0f1f4a8"}, {"name": "test_show_me_premier_le<PERSON><PERSON>_goal_ranking", "children": [{"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "6dc578f8342571a5", "parentUid": "51407e55f461bb75e0ea1d0bcd974a5d", "status": "failed", "time": {"start": 1754485540658, "stop": 1754485555831, "duration": 15173}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "51407e55f461bb75e0ea1d0bcd974a5d"}, {"name": "test_show_scores_between_livepool_and_manchester_city", "children": [{"name": "测试show scores between livepool and manchester city能正常执行", "uid": "142b184b00093dd8", "parentUid": "390af3b968f43ff01d77f8b7960b3916", "status": "failed", "time": {"start": 1754485568848, "stop": 1754485583117, "duration": 14269}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "390af3b968f43ff01d77f8b7960b3916"}, {"name": "test_stop_music", "children": [{"name": "测试stop music能正常执行", "uid": "d6ce3a1657495012", "parentUid": "77eaeb306e34a7a9bfb4cedeba8ee35f", "status": "failed", "time": {"start": 1754485596139, "stop": 1754485609185, "duration": 13046}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "77eaeb306e34a7a9bfb4cedeba8ee35f"}, {"name": "test_stop_run", "children": [{"name": "测试stop run能正常执行", "uid": "d04ff44af557a074", "parentUid": "ccf9f3b4d1f905064aacc7d3127597b9", "status": "passed", "time": {"start": 1754485622231, "stop": 1754485635679, "duration": 13448}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ccf9f3b4d1f905064aacc7d3127597b9"}, {"name": "test_stop_workout", "children": [{"name": "测试stop workout能正常执行", "uid": "69ec99d828b27505", "parentUid": "8ee49369bb9d601da7f062855654c904", "status": "passed", "time": {"start": 1754485648511, "stop": 1754485662811, "duration": 14300}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8ee49369bb9d601da7f062855654c904"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "测试summarize content on this page能正常执行", "uid": "cb536fc6dbd8afa0", "parentUid": "b6a6c5c1cc3bbfd99be0b798596d90fd", "status": "passed", "time": {"start": 1754485675842, "stop": 1754485690128, "duration": 14286}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b6a6c5c1cc3bbfd99be0b798596d90fd"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "测试summarize what i'm reading能正常执行", "uid": "ac9142d504d30e72", "parentUid": "a593e493c32cd99871d376fe55644611", "status": "passed", "time": {"start": 1754485703002, "stop": 1754485716573, "duration": 13571}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a593e493c32cd99871d376fe55644611"}, {"name": "test_take_a_joke", "children": [{"name": "测试take a joke能正常执行", "uid": "dc792c51ad45908a", "parentUid": "a1543d72151cd8524884aba6ef4613f6", "status": "passed", "time": {"start": 1754485729515, "stop": 1754485744736, "duration": 15221}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a1543d72151cd8524884aba6ef4613f6"}, {"name": "test_take_a_note_on_how_to_build_a_treehouse", "children": [{"name": "测试take a note on how to build a treehouse能正常执行", "uid": "2238b295fb197f63", "parentUid": "218955f9fce08124722f8aa63ee5293a", "status": "passed", "time": {"start": 1754485757660, "stop": 1754485771164, "duration": 13504}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "218955f9fce08124722f8aa63ee5293a"}, {"name": "test_take_notes_on_how_to_build_a_treehouse", "children": [{"name": "测试take notes on how to build a treehouse能正常执行", "uid": "18284c5918c912a5", "parentUid": "acd0d4c0ff78ae721b07111ac720141b", "status": "passed", "time": {"start": 1754485783943, "stop": 1754485797292, "duration": 13349}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "acd0d4c0ff78ae721b07111ac720141b"}, {"name": "test_tell_me_a_joke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "f4753f2d44bd35b5", "parentUid": "1efd42262e16a36d4ac595eed9b7e185", "status": "failed", "time": {"start": 1754485810136, "stop": 1754485825927, "duration": 15791}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1efd42262e16a36d4ac595eed9b7e185"}, {"name": "test_video_call_mom_through_whatsapp", "children": [{"name": "测试video call mom through whatsapp能正常执行", "uid": "b7779889ffdf936e", "parentUid": "f8daa61044336f258fd904820f097662", "status": "failed", "time": {"start": 1754485838917, "stop": 1754485858507, "duration": 19590}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f8daa61044336f258fd904820f097662"}, {"name": "test_what_is_apec", "children": [{"name": "测试what is apec?能正常执行", "uid": "543154d5028ed6d", "parentUid": "4567e57227f42cc7993892d86e523dbf", "status": "passed", "time": {"start": 1754485871627, "stop": 1754485888403, "duration": 16776}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4567e57227f42cc7993892d86e523dbf"}, {"name": "test_what_languages_do_you_support", "children": [{"name": "测试What languages do you support能正常执行", "uid": "a2181f30c28e43a1", "parentUid": "20b964271968b9aba9b13a138825e688", "status": "passed", "time": {"start": 1754485901463, "stop": 1754485914737, "duration": 13274}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20b964271968b9aba9b13a138825e688"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "测试what's the weather like in shanghai today能正常执行", "uid": "24be109e76a80bd6", "parentUid": "15f8c381e13aff9342434a5db73fe066", "status": "passed", "time": {"start": 1754485927315, "stop": 1754485945789, "duration": 18474}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "15f8c381e13aff9342434a5db73fe066"}, {"name": "test_what_s_the_weather_today", "children": [{"name": "测试what·s the weather today？能正常执行", "uid": "3eabaf55f35a4e66", "parentUid": "af2f763310dd868fb7293de462763b64", "status": "passed", "time": {"start": 1754485958650, "stop": 1754485979502, "duration": 20852}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "af2f763310dd868fb7293de462763b64"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "f9dec29930734510", "parentUid": "b880b898b5ae6eb8576adc4f20cdd8c6", "status": "failed", "time": {"start": 1754485992367, "stop": 1754486007276, "duration": 14909}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b880b898b5ae6eb8576adc4f20cdd8c6"}, {"name": "test_what_s_your_name", "children": [{"name": "测试what's your name？能正常执行", "uid": "301099c31151442e", "parentUid": "4b64931505303134ebf07e49f3a9c0af", "status": "passed", "time": {"start": 1754486020217, "stop": 1754486033584, "duration": 13367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4b64931505303134ebf07e49f3a9c0af"}, {"name": "test_what_time_is_it_now", "children": [{"name": "测试what time is it now能正常执行", "uid": "9f14381c863cf0cc", "parentUid": "6a367b30c7c610464ef1a8acfa0f93c9", "status": "passed", "time": {"start": 1754486046355, "stop": 1754486059787, "duration": 13432}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a367b30c7c610464ef1a8acfa0f93c9"}, {"name": "test_whats_the_weather_today", "children": [{"name": "测试whats the weather today能正常执行", "uid": "d427b58b6fde626", "parentUid": "d38e79c83442fa3e23fba8b6ba34de56", "status": "passed", "time": {"start": 1754486072773, "stop": 1754486090943, "duration": 18170}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d38e79c83442fa3e23fba8b6ba34de56"}, {"name": "test_who_is_harry_potter", "children": [{"name": "测试who is harry potter能正常执行", "uid": "a4a4e1574a0da588", "parentUid": "651e0dc624a6f14649ebc34a90907bc1", "status": "passed", "time": {"start": 1754486103923, "stop": 1754486120135, "duration": 16212}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "651e0dc624a6f14649ebc34a90907bc1"}, {"name": "test_who_is_j_k_rowling", "children": [{"name": "测试who is j k rowling能正常执行", "uid": "44baa1a8a8e2a655", "parentUid": "9e3d19482db247c568f3393a2db09f1f", "status": "passed", "time": {"start": 1754486133031, "stop": 1754486150400, "duration": 17369}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e3d19482db247c568f3393a2db09f1f"}, {"name": "test_why_is_my_phone_not_ringing_on_incoming_calls", "children": [{"name": "测试why is my phone not ringing on incoming calls能正常执行", "uid": "76ccf467a41e6ff5", "parentUid": "d08c8b89f0cacddcb2f61563717a9da6", "status": "passed", "time": {"start": 1754486163336, "stop": 1754486183062, "duration": 19726}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d08c8b89f0cacddcb2f61563717a9da6"}, {"name": "test_why_my_charging_is_so_slow", "children": [{"name": "测试why my charging is so slow能正常执行", "uid": "b9c6c8bfe1404e7", "parentUid": "015300f55fc1d98de4057611dffc7dd0", "status": "failed", "time": {"start": 1754486196007, "stop": 1754486210302, "duration": 14295}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "015300f55fc1d98de4057611dffc7dd0"}], "uid": "9c28642eff039d4ac586a58d4f4a0368"}, {"name": "system_coupling", "children": [{"name": "test_adjustment_the_brightness_to", "children": [{"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "b3a0985d3b8f219c", "parentUid": "f42ec0f584493c7e8f49de225d028d41", "status": "failed", "time": {"start": 1754486223471, "stop": 1754486237302, "duration": 13831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f42ec0f584493c7e8f49de225d028d41"}, {"name": "test_boost_phone", "children": [{"name": "测试boost phone能正常执行", "uid": "6630dd6507ad295e", "parentUid": "084463cd25c5bdf79a97950326707013", "status": "passed", "time": {"start": 1754486250363, "stop": 1754486264340, "duration": 13977}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "084463cd25c5bdf79a97950326707013"}, {"name": "test_check_front_camera_information", "children": [{"name": "测试check front camera information能正常执行", "uid": "1dd78894947cdfc0", "parentUid": "a3c3dc73a42e85d88737ae9cfea06ccf", "status": "failed", "time": {"start": 1754486277352, "stop": 1754486296726, "duration": 19374}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3c3dc73a42e85d88737ae9cfea06ccf"}, {"name": "test_clear_junk_files", "children": [{"name": "测试clear junk files命令", "uid": "6b30492325e06f55", "parentUid": "79b6df9a290803cad2cc59c26f69f0d5", "status": "passed", "time": {"start": 1754486309611, "stop": 1754486339344, "duration": 29733}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79b6df9a290803cad2cc59c26f69f0d5"}, {"name": "test_close_bluetooth", "children": [{"name": "测试close bluetooth能正常执行", "uid": "c4fafbf029b57c35", "parentUid": "43c3e77fe7845ae372f8b29b479e9c36", "status": "passed", "time": {"start": 1754486352403, "stop": 1754486367731, "duration": 15328}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43c3e77fe7845ae372f8b29b479e9c36"}, {"name": "test_close_flashlight", "children": [{"name": "测试close flashlight能正常执行", "uid": "57ba6f253fe4a6a6", "parentUid": "05e7de1f6a30400f4ba09b6b927c0c5f", "status": "passed", "time": {"start": 1754486380541, "stop": 1754486396481, "duration": 15940}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "05e7de1f6a30400f4ba09b6b927c0c5f"}, {"name": "test_countdown_min", "children": [{"name": "测试countdown 5 min能正常执行", "uid": "56d2b9e1c2fc39f", "parentUid": "5acc0b94de2365f9212477ba5756e434", "status": "failed", "time": {"start": 1754486409251, "stop": 1754486426730, "duration": 17479}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5acc0b94de2365f9212477ba5756e434"}, {"name": "test_decrease_the_brightness", "children": [{"name": "测试decrease the brightness能正常执行", "uid": "72e5b9b0b1fda4b5", "parentUid": "71d6a5aa3a46022e7d7ccf02f396978b", "status": "passed", "time": {"start": 1754486439616, "stop": 1754486454073, "duration": 14457}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71d6a5aa3a46022e7d7ccf02f396978b"}, {"name": "test_end_screen_recording", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "86fa5010cef990cf", "parentUid": "62823fc182e440a2772cd17c42ab4c29", "status": "passed", "time": {"start": 1754486466855, "stop": 1754486483899, "duration": 17044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "e06d746a0349fe32", "parentUid": "62823fc182e440a2772cd17c42ab4c29", "status": "passed", "time": {"start": 1754486496685, "stop": 1754486515106, "duration": 18421}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "62823fc182e440a2772cd17c42ab4c29"}, {"name": "test_help_me_take_a_long_screenshot", "children": [{"name": "测试help me take a long screenshot能正常执行", "uid": "324c32662b1b44a7", "parentUid": "1349bd48475843b043511c26d6b12b24", "status": "passed", "time": {"start": 1754486527843, "stop": 1754486546055, "duration": 18212}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1349bd48475843b043511c26d6b12b24"}, {"name": "test_help_me_take_a_screenshot", "children": [{"name": "测试help me take a screenshot能正常执行", "uid": "ec98df5d96735ef2", "parentUid": "f5c6c4880b5d8ab718c388b911880945", "status": "passed", "time": {"start": 1754486558792, "stop": 1754486575565, "duration": 16773}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f5c6c4880b5d8ab718c388b911880945"}, {"name": "test_long_screenshot", "children": [{"name": "测试long screenshot能正常执行", "uid": "6f950886f9130f1d", "parentUid": "eee53365e608da87e8c1a38cd7dffd82", "status": "passed", "time": {"start": 1754486588509, "stop": 1754486605351, "duration": 16842}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eee53365e608da87e8c1a38cd7dffd82"}, {"name": "test_maximum_volume", "children": [{"name": "测试maximum volume能正常执行", "uid": "56bfc1a59701e4b9", "parentUid": "df87e0d39fb14600720a434db404fb7b", "status": "passed", "time": {"start": 1754486618078, "stop": 1754486633129, "duration": 15051}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df87e0d39fb14600720a434db404fb7b"}, {"name": "test_memory_cleanup", "children": [{"name": "测试memory cleanup能正常执行", "uid": "12433c483fa32d60", "parentUid": "3060b8cc7f41dc69110dfc534e6b31d5", "status": "passed", "time": {"start": 1754486646004, "stop": 1754486671642, "duration": 25638}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3060b8cc7f41dc69110dfc534e6b31d5"}, {"name": "test_minimum_volume", "children": [{"name": "测试minimum volume能正常执行", "uid": "14594ed883fdf82e", "parentUid": "25a58f43bfaf0da153ec687fea5dcbb5", "status": "passed", "time": {"start": 1754486684458, "stop": 1754486699961, "duration": 15503}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "25a58f43bfaf0da153ec687fea5dcbb5"}, {"name": "test_open_bluetooth", "children": [{"name": "测试open bluetooth", "uid": "bf9c38fe63dfb10f", "parentUid": "31e546db33350801c6eaef968b45b6aa", "status": "passed", "time": {"start": 1754486713130, "stop": 1754486727490, "duration": 14360}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "31e546db33350801c6eaef968b45b6aa"}, {"name": "test_open_bt", "children": [{"name": "测试open bt", "uid": "38eadf37eecb0101", "parentUid": "176a0fdfd9c4144b8dfe3d66a049fda5", "status": "passed", "time": {"start": 1754486740453, "stop": 1754486754434, "duration": 13981}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "176a0fdfd9c4144b8dfe3d66a049fda5"}, {"name": "test_open_flashlight", "children": [{"name": "测试open flashlight", "uid": "b1fe075113ee100d", "parentUid": "649e9c0b88c706f3f0551679bed64e42", "status": "passed", "time": {"start": 1754486767570, "stop": 1754486783909, "duration": 16339}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "649e9c0b88c706f3f0551679bed64e42"}, {"name": "test_open_wifi", "children": [{"name": "测试open wifi", "uid": "1ccd3d9645a92f1f", "parentUid": "f9eadf40e8c6f09400b67a9959223d07", "status": "passed", "time": {"start": 1754486796964, "stop": 1754486811101, "duration": 14137}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f9eadf40e8c6f09400b67a9959223d07"}, {"name": "test_power_saving", "children": [{"name": "测试power saving能正常执行", "uid": "24ed300ebceea57f", "parentUid": "c06c413949d25e13cefc3d7381223a9b", "status": "passed", "time": {"start": 1754486823934, "stop": 1754486848967, "duration": 25033}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c06c413949d25e13cefc3d7381223a9b"}, {"name": "test_screen_record", "children": [{"name": "测试screen record能正常执行", "uid": "fbf4ec4a014b304c", "parentUid": "b54fab3751e243d1b424e37d5d287ec3", "status": "passed", "time": {"start": 1754486861875, "stop": 1754486880906, "duration": 19031}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "9f545d6fdce360ca", "parentUid": "b54fab3751e243d1b424e37d5d287ec3", "status": "passed", "time": {"start": 1754486893641, "stop": 1754486911064, "duration": 17423}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b54fab3751e243d1b424e37d5d287ec3"}, {"name": "test_set_a_timer_for_minutes", "children": [{"name": "测试set a timer for 10 minutes能正常执行", "uid": "c81e6489bc0d344c", "parentUid": "b51793332bcfdc1c2f251c5ba2c434e5", "status": "passed", "time": {"start": 1754486923814, "stop": 1754486941667, "duration": 17853}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b51793332bcfdc1c2f251c5ba2c434e5"}, {"name": "test_set_alarm_for_10_o_clock", "children": [{"name": "测试set alarm for 10 o'clock", "uid": "e4fcecfda38c470d", "parentUid": "afeb5aed32d36d5eaa4b132ea5b382e0", "status": "passed", "time": {"start": 1754486954332, "stop": 1754486972024, "duration": 17692}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "afeb5aed32d36d5eaa4b132ea5b382e0"}, {"name": "test_set_battery_saver_setting", "children": [{"name": "测试set Battery Saver setting能正常执行", "uid": "a68a358b1eadc5f5", "parentUid": "b6e60ab86df5fa2a1124b5d765cbfe9a", "status": "failed", "time": {"start": 1754486984685, "stop": 1754487005808, "duration": 21123}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b6e60ab86df5fa2a1124b5d765cbfe9a"}, {"name": "test_smart_charge", "children": [{"name": "测试smart charge能正常执行", "uid": "5f63eb7a21dc6418", "parentUid": "38c8329119fb1c04106bbcd94b91ce11", "status": "failed", "time": {"start": 1754487018773, "stop": 1754487033255, "duration": 14482}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "38c8329119fb1c04106bbcd94b91ce11"}, {"name": "test_start_record", "children": [{"name": "测试start record能正常执行", "uid": "937f207343b8f0c2", "parentUid": "9018ab08ec79118ab3c7ac811af9f4fa", "status": "passed", "time": {"start": 1754487046321, "stop": 1754487063346, "duration": 17025}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "af1edd5c403db9e4", "parentUid": "9018ab08ec79118ab3c7ac811af9f4fa", "status": "passed", "time": {"start": 1754487076214, "stop": 1754487094660, "duration": 18446}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9018ab08ec79118ab3c7ac811af9f4fa"}, {"name": "test_start_screen_recording", "children": [{"name": "测试start screen recording能正常执行", "uid": "93bd3202f99050e7", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1754487107718, "stop": 1754487125254, "duration": 17536}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause screen recording能正常执行", "uid": "f8f79e89f6eee656", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1754487138274, "stop": 1754487155076, "duration": 16802}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "continue  screen recording能正常执行", "uid": "28d8f9a0379236cb", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1754487168183, "stop": 1754487185107, "duration": 16924}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "dc6a66865aca9b84", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1754487198377, "stop": 1754487216856, "duration": 18479}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a083bce7f3fa2d36f16068ac034f4e62"}, {"name": "test_stop_recording", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "5c6774d94dd8b0dd", "parentUid": "4eb0eb9df164551aeca11800ab680c92", "status": "passed", "time": {"start": 1754487230107, "stop": 1754487247631, "duration": 17524}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "156bf9e6b63b254c", "parentUid": "4eb0eb9df164551aeca11800ab680c92", "status": "passed", "time": {"start": 1754487261064, "stop": 1754487276226, "duration": 15162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4eb0eb9df164551aeca11800ab680c92"}, {"name": "test_switch_charging_modes", "children": [{"name": "测试switch charging modes能正常执行", "uid": "7f6bcf1a52131d92", "parentUid": "43ff861df9a59eeef7ac3edd50740e02", "status": "failed", "time": {"start": 1754487289081, "stop": 1754487302980, "duration": 13899}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43ff861df9a59eeef7ac3edd50740e02"}, {"name": "test_switch_magic_voice_to_grace", "children": [{"name": "测试Switch Magic Voice to Grace能正常执行", "uid": "a4c00a2e50a210dd", "parentUid": "035194a70f8a76a4b76a28f704b8ff10", "status": "passed", "time": {"start": 1754487316406, "stop": 1754487330699, "duration": 14293}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "035194a70f8a76a4b76a28f704b8ff10"}, {"name": "test_switch_magic_voice_to_mango", "children": [{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "ac3ea7385a27322b", "parentUid": "661f1bc7755dcb73f23369637ff67460", "status": "passed", "time": {"start": 1754487343552, "stop": 1754487356601, "duration": 13049}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "661f1bc7755dcb73f23369637ff67460"}, {"name": "test_switch_to_barrage_notification", "children": [{"name": "测试Switch to Barrage Notification能正常执行", "uid": "a4197bd04ef3d35d", "parentUid": "71d9b05a5d33eaee0a7365eb0716032e", "status": "failed", "time": {"start": 1754487369682, "stop": 1754487385769, "duration": 16087}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71d9b05a5d33eaee0a7365eb0716032e"}, {"name": "test_switch_to_default_mode", "children": [{"name": "测试switch to default mode能正常执行", "uid": "da92ff9a78d65b18", "parentUid": "62ed32eba114769fb134a432054912e4", "status": "passed", "time": {"start": 1754487399113, "stop": 1754487415490, "duration": 16377}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "62ed32eba114769fb134a432054912e4"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "测试switch to equilibrium mode能正常执行", "uid": "aa06e05449f42426", "parentUid": "2d94b30b5f27ad8b3f8967a2a3fdebe3", "status": "failed", "time": {"start": 1754487428781, "stop": 1754487445077, "duration": 16296}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d94b30b5f27ad8b3f8967a2a3fdebe3"}, {"name": "test_switch_to_flash_notification", "children": [{"name": "测试switch to flash notification能正常执行", "uid": "4d5b68cf4e8d17f8", "parentUid": "561ddfab014b4de5926c248cf15d4c27", "status": "failed", "time": {"start": 1754487458459, "stop": 1754487478499, "duration": 20040}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "561ddfab014b4de5926c248cf15d4c27"}, {"name": "test_switch_to_hyper_charge", "children": [{"name": "测试Switch to Hyper Charge能正常执行", "uid": "948460c746878063", "parentUid": "99e29771f7571a2fc146c069d7b2c125", "status": "failed", "time": {"start": 1754487491529, "stop": 1754487504641, "duration": 13112}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "99e29771f7571a2fc146c069d7b2c125"}, {"name": "test_switch_to_low_temp_charge", "children": [{"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "883c5d89069451b1", "parentUid": "e6a1e2156d8852f07854b84613f04720", "status": "failed", "time": {"start": 1754487517979, "stop": 1754487531460, "duration": 13481}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e6a1e2156d8852f07854b84613f04720"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "测试switch to power saving mode能正常执行", "uid": "bda91739b78aea60", "parentUid": "dd9e8f3389e8ba0ae9839c873338d760", "status": "passed", "time": {"start": 1754487544598, "stop": 1754487558679, "duration": 14081}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dd9e8f3389e8ba0ae9839c873338d760"}, {"name": "test_switch_to_smart_charge", "children": [{"name": "测试switch to smart charge能正常执行", "uid": "8ad6893c56a2881f", "parentUid": "a22da6d101545dc9b0d8edfda3746b20", "status": "failed", "time": {"start": 1754487571731, "stop": 1754487584828, "duration": 13097}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a22da6d101545dc9b0d8edfda3746b20"}, {"name": "test_switched_to_data_mode", "children": [{"name": "测试switched to data mode能正常执行", "uid": "4f285330353aa837", "parentUid": "2902f5b25b15010f9670892ff424d92c", "status": "failed", "time": {"start": 1754487597893, "stop": 1754487611697, "duration": 13804}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2902f5b25b15010f9670892ff424d92c"}, {"name": "test_take_a_photo", "children": [{"name": "测试take a photo能正常执行", "uid": "bf068ac0f1aaa3f3", "parentUid": "07997292671f0c2fc7c4f6639faf371f", "status": "failed", "time": {"start": 1754487624970, "stop": 1754487655439, "duration": 30469}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "07997292671f0c2fc7c4f6639faf371f"}, {"name": "test_take_a_selfie", "children": [{"name": "测试take a selfie能正常执行", "uid": "7c428f481eb64859", "parentUid": "b76edbc7be133088ff6cc7dcf79d0688", "status": "failed", "time": {"start": 1754487668402, "stop": 1754487698993, "duration": 30591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b76edbc7be133088ff6cc7dcf79d0688"}, {"name": "test_the_battery_of_the_mobile_phone_is_too_low", "children": [{"name": "测试the battery of the mobile phone is too low能正常执行", "uid": "64ce3e31c1cf3f3f", "parentUid": "fd16a15b2bee6fa00e607a69966a816d", "status": "passed", "time": {"start": 1754487711858, "stop": 1754487737322, "duration": 25464}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fd16a15b2bee6fa00e607a69966a816d"}, {"name": "test_turn_down_ring_volume", "children": [{"name": "测试turn down ring volume能正常执行", "uid": "f804cd13aef5ae6b", "parentUid": "550486b451765b696814b7b6743f77fe", "status": "failed", "time": {"start": 1754487750180, "stop": 1754487764062, "duration": 13882}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "550486b451765b696814b7b6743f77fe"}, {"name": "test_turn_off_flashlight", "children": [{"name": "测试turn off flashlight能正常执行", "uid": "ead7ca339b50310b", "parentUid": "990086735fb4bd184a8ac5a25a5f821c", "status": "passed", "time": {"start": 1754487777334, "stop": 1754487792679, "duration": 15345}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "990086735fb4bd184a8ac5a25a5f821c"}, {"name": "test_turn_off_light_theme", "children": [{"name": "测试turn off light theme能正常执行", "uid": "5052494e445ea3a2", "parentUid": "83d0ab198b941cca60896fe1448789bd", "status": "passed", "time": {"start": 1754487805511, "stop": 1754487819220, "duration": 13709}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83d0ab198b941cca60896fe1448789bd"}, {"name": "test_turn_off_nfc", "children": [{"name": "测试turn off nfc能正常执行", "uid": "d04bf43a76908a56", "parentUid": "25370c24404047b47256854554ecf19e", "status": "passed", "time": {"start": 1754487832220, "stop": 1754487847428, "duration": 15208}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "25370c24404047b47256854554ecf19e"}, {"name": "test_turn_on_bluetooth", "children": [{"name": "测试turn on bluetooth能正常执行", "uid": "2674d3ebdb6a082a", "parentUid": "06858c4ec997c86c877ce6ab09e3bbaf", "status": "passed", "time": {"start": 1754487860460, "stop": 1754487874263, "duration": 13803}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "06858c4ec997c86c877ce6ab09e3bbaf"}, {"name": "test_turn_on_do_not_disturb_mode", "children": [{"name": "测试turn on do not disturb mode能正常执行", "uid": "9bbc7b793fb30846", "parentUid": "869ae35c254b699074c4a890b2986c6d", "status": "passed", "time": {"start": 1754487887406, "stop": 1754487901646, "duration": 14240}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "869ae35c254b699074c4a890b2986c6d"}, {"name": "test_turn_on_light_theme", "children": [{"name": "测试turn on light theme能正常执行", "uid": "1d658bbc993404eb", "parentUid": "9684df9c422f9cf3eef3d6f0ddd6607c", "status": "passed", "time": {"start": 1754487914418, "stop": 1754487928231, "duration": 13813}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "a231ab2220a212ac", "parentUid": "9684df9c422f9cf3eef3d6f0ddd6607c", "status": "passed", "time": {"start": 1754487941157, "stop": 1754487955456, "duration": 14299}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9684df9c422f9cf3eef3d6f0ddd6607c"}, {"name": "test_turn_on_location_services", "children": [{"name": "测试turn on location services能正常执行", "uid": "426f048698440e79", "parentUid": "13b991141a19f0fe4b82a01990c6a851", "status": "passed", "time": {"start": 1754487968462, "stop": 1754487982877, "duration": 14415}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "13b991141a19f0fe4b82a01990c6a851"}, {"name": "test_turn_on_nfc", "children": [{"name": "测试turn on nfc能正常执行", "uid": "ac959bcc15134bc9", "parentUid": "ac9c5a7d76d32a3f0024f1bbc4260940", "status": "passed", "time": {"start": 1754487996245, "stop": 1754488010760, "duration": 14515}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ac9c5a7d76d32a3f0024f1bbc4260940"}, {"name": "test_turn_on_the_flashlight", "children": [{"name": "测试turn on the flashlight能正常执行", "uid": "403e3a42990a4305", "parentUid": "c5e4b9b650952bb94f49d9ea64fb9376", "status": "passed", "time": {"start": 1754488023989, "stop": 1754488039854, "duration": 15865}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c5e4b9b650952bb94f49d9ea64fb9376"}, {"name": "test_turn_on_the_screen_record", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "d8abb8f7b66ff219", "parentUid": "5cf049f3605e39b97e2902b74b81323d", "status": "passed", "time": {"start": 1754488052845, "stop": 1754488069899, "duration": 17054}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "c0ca4907bf7ec56c", "parentUid": "5cf049f3605e39b97e2902b74b81323d", "status": "passed", "time": {"start": 1754488083064, "stop": 1754488101011, "duration": 17947}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cf049f3605e39b97e2902b74b81323d"}, {"name": "test_turn_on_wifi", "children": [{"name": "测试turn on wifi能正常执行", "uid": "e06a7c81519a6ea3", "parentUid": "8f8981d5b6534e5c462a0d8cbafb4302", "status": "passed", "time": {"start": 1754488114007, "stop": 1754488129817, "duration": 15810}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8f8981d5b6534e5c462a0d8cbafb4302"}, {"name": "test_wake_me_up_at_am_tomorrow", "children": [{"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "a760bb1d944c1018", "parentUid": "193cbbfa30db6a41aacdad7a864e09bc", "status": "passed", "time": {"start": 1754488142734, "stop": 1754488156049, "duration": 13315}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "193cbbfa30db6a41aacdad7a864e09bc"}, {"name": "test_where_is_the_carlcare_service_outlet", "children": [{"name": "测试where is the carlcare service outlet能正常执行", "uid": "d0d3620174008d52", "parentUid": "a69c8f4ca42c7f7233299fb00d064c9f", "status": "failed", "time": {"start": 1754488169099, "stop": 1754488184592, "duration": 15493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a69c8f4ca42c7f7233299fb00d064c9f"}], "uid": "93fc5475dc5c7ad817f2b1ab5b0ac7b9"}, {"name": "third_coupling", "children": [{"name": "test_download_app", "children": [{"name": "测试download app能正常执行", "uid": "13a234a2652ea3ee", "parentUid": "b132f6b6f7a47dbe39c1fb7de000fe78", "status": "passed", "time": {"start": 1754488197689, "stop": 1754488213139, "duration": 15450}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b132f6b6f7a47dbe39c1fb7de000fe78"}, {"name": "test_download_basketball", "children": [{"name": "测试download basketball能正常执行", "uid": "2dcf43a24b3034da", "parentUid": "9ad7d52518459e93bf395586c4de03d3", "status": "failed", "time": {"start": 1754488226197, "stop": 1754488240470, "duration": 14273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9ad7d52518459e93bf395586c4de03d3"}, {"name": "test_download_qq", "children": [{"name": "测试download qq能正常执行", "uid": "36600db5fc8f1ba2", "parentUid": "88f428f9095db30aa10835e50fff2f44", "status": "passed", "time": {"start": 1754488253518, "stop": 1754488270639, "duration": 17121}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "88f428f9095db30aa10835e50fff2f44"}, {"name": "test_find_a_restaurant_near_me", "children": [{"name": "测试find a restaurant near me能正常执行", "uid": "bfdac69c756af839", "parentUid": "990b9417ef5d8284b5ed1c8f1dcb2456", "status": "passed", "time": {"start": 1754488283667, "stop": 1754488314683, "duration": 31016}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "990b9417ef5d8284b5ed1c8f1dcb2456"}, {"name": "test_navigate_from_beijing_to_shanghai", "children": [{"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "uid": "d692af8b399c4ed0", "parentUid": "98910e36f666a4645d039127f271b15c", "status": "passed", "time": {"start": 1754488327511, "stop": 1754488348943, "duration": 21432}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98910e36f666a4645d039127f271b15c"}, {"name": "test_navigate_from_to_red_square", "children": [{"name": "测试navigate from to red square能正常执行", "uid": "c06d0b280e1c66e9", "parentUid": "b5db47a2899bd2831280d03f6ab5ccde", "status": "failed", "time": {"start": 1754488350360, "stop": 1754488350360, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b5db47a2899bd2831280d03f6ab5ccde"}, {"name": "test_navigate_to_shanghai_disneyland", "children": [{"name": "测试navigate to shanghai disneyland能正常执行", "uid": "25bd4771bf8a6eb2", "parentUid": "63d4dbd18e96d1791e9029023802405a", "status": "failed", "time": {"start": 1754488383986, "stop": 1754488383986, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "63d4dbd18e96d1791e9029023802405a"}, {"name": "test_navigation_to_the_lucky", "children": [{"name": "测试navigation to the lucky能正常执行", "uid": "cd10f690763a13fb", "parentUid": "26d631af39b9512dace23d786ed426bc", "status": "failed", "time": {"start": 1754488418472, "stop": 1754488418472, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "26d631af39b9512dace23d786ed426bc"}, {"name": "test_open_facebook", "children": [{"name": "测试open facebook能正常执行", "uid": "c6e041786c62d65a", "parentUid": "28034a46d61d0cc3fef6e9ffd4db7994", "status": "failed", "time": {"start": 1754488452897, "stop": 1754488452897, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "28034a46d61d0cc3fef6e9ffd4db7994"}, {"name": "test_open_whatsapp", "children": [{"name": "测试open whatsapp", "uid": "57fe78f11b96e66d", "parentUid": "300a56019ec17b2ba7bcf48ab420b3c3", "status": "failed", "time": {"start": 1754488486890, "stop": 1754488486890, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "300a56019ec17b2ba7bcf48ab420b3c3"}, {"name": "test_order_a_burger", "children": [{"name": "测试order a burger能正常执行", "uid": "c219d8630c012ecb", "parentUid": "2f48f1343c12cc6c07d8e886b62e4c0d", "status": "failed", "time": {"start": 1754488520947, "stop": 1754488520947, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2f48f1343c12cc6c07d8e886b62e4c0d"}, {"name": "test_order_a_takeaway", "children": [{"name": "测试order a takeaway能正常执行", "uid": "2999695e96e957d0", "parentUid": "540441448375adc4953206d10f542ed2", "status": "failed", "time": {"start": 1754488554794, "stop": 1754488554794, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "540441448375adc4953206d10f542ed2"}, {"name": "test_pls_open_the_newest_whatsapp_activity", "children": [{"name": "测试pls open the newest whatsapp activity", "uid": "7a6c918293c863b9", "parentUid": "18df90aff668e599fc314e80b0851b3d", "status": "failed", "time": {"start": 1754488588832, "stop": 1754488588832, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "18df90aff668e599fc314e80b0851b3d"}, {"name": "test_whatsapp", "children": [{"name": "测试whatsapp能正常执行", "uid": "c2f84ab6863401ce", "parentUid": "3936daaae04788e4f3494c8c7b4dd2c4", "status": "failed", "time": {"start": 1754488623011, "stop": 1754488623011, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3936daaae04788e4f3494c8c7b4dd2c4"}], "uid": "f10231acc09c45f9430bedaedd076cbf"}, {"name": "unsupported_commands", "children": [{"name": "test_Add_the_images_and_text_on_the_screen_to_the_note", "children": [{"name": "测试Add the images and text on the screen to the note", "uid": "38a048b45445ca67", "parentUid": "b7d205f78a6f3e533fa2c219a8aae3b4", "status": "failed", "time": {"start": 1754488657242, "stop": 1754488657242, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b7d205f78a6f3e533fa2c219a8aae3b4"}, {"name": "test_change_female_tone_name_voice", "children": [{"name": "测试change (female/tone name) voice能正常执行", "uid": "1199297291d0d17", "parentUid": "e304012317c1b3a1f774488d1d79d607", "status": "failed", "time": {"start": 1754488691332, "stop": 1754488691333, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e304012317c1b3a1f774488d1d79d607"}, {"name": "test_change_man_voice", "children": [{"name": "测试change man voice能正常执行", "uid": "cbd155ed4bc579f", "parentUid": "9a2cbc470fe28892afde5493b609c218", "status": "failed", "time": {"start": 1754488725683, "stop": 1754488725683, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9a2cbc470fe28892afde5493b609c218"}, {"name": "test_change_your_voice", "children": [{"name": "测试change your voice能正常执行", "uid": "3a8cee725f31ec40", "parentUid": "0aec3e6cbfb649afcf2d7319501ab2f5", "status": "failed", "time": {"start": 1754488759791, "stop": 1754488759791, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0aec3e6cbfb649afcf2d7319501ab2f5"}, {"name": "test_check_battery_information", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "6901c3d0d10496fa", "parentUid": "8757fd926e61f6ebb245d3d3d474c927", "status": "failed", "time": {"start": 1754488793969, "stop": 1754488793969, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8757fd926e61f6ebb245d3d3d474c927"}, {"name": "test_check_contact", "children": [{"name": "测试check contact能正常执行", "uid": "e27a3e330ed5d0d0", "parentUid": "a40100045be74641430d892fba85ca45", "status": "failed", "time": {"start": 1754488827949, "stop": 1754488827949, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a40100045be74641430d892fba85ca45"}, {"name": "test_check_contacts", "children": [{"name": "测试check contacts能正常执行", "uid": "443dca154c3756ff", "parentUid": "2d2788a266628ad79e779a5222860ff2", "status": "failed", "time": {"start": 1754488862083, "stop": 1754488862083, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d2788a266628ad79e779a5222860ff2"}, {"name": "test_check_mobile_data_balance_of_sim", "children": [{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "24595911ea65e948", "parentUid": "837efc09915a750bd95f96f1811eb69d", "status": "failed", "time": {"start": 1754488896070, "stop": 1754488896070, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "837efc09915a750bd95f96f1811eb69d"}, {"name": "test_check_model_information", "children": [{"name": "测试check model information返回正确的不支持响应", "uid": "e0ffa8c14943a28b", "parentUid": "78831bedd8240ff30a47ed4c298163e6", "status": "failed", "time": {"start": 1754488930105, "stop": 1754488930105, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "78831bedd8240ff30a47ed4c298163e6"}, {"name": "test_check_my_balance_of_sim", "children": [{"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "c3973bb228d29704", "parentUid": "bddf49f414a6afcdd8f3aa88c2990b73", "status": "failed", "time": {"start": 1754488964292, "stop": 1754488964292, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bddf49f414a6afcdd8f3aa88c2990b73"}, {"name": "test_check_my_to_do_list", "children": [{"name": "测试check my to-do list能正常执行", "uid": "f6680ac844d1bbf7", "parentUid": "941e24236863d467719d0e685e22e88f", "status": "failed", "time": {"start": 1754488998385, "stop": 1754488998385, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "941e24236863d467719d0e685e22e88f"}, {"name": "test_check_rear_camera_information", "children": [{"name": "测试check rear camera information能正常执行", "uid": "493d4b92d0c5f035", "parentUid": "0f68e4cddcec3d7808a4b93d7289e24d", "status": "failed", "time": {"start": 1754489032587, "stop": 1754489032587, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0f68e4cddcec3d7808a4b93d7289e24d"}, {"name": "test_close_equilibrium_mode", "children": [{"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "bdff75fea7ed5983", "parentUid": "2a21ec0aa1162e7205ed4479032eec28", "status": "failed", "time": {"start": 1754489066550, "stop": 1754489066550, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2a21ec0aa1162e7205ed4479032eec28"}, {"name": "test_close_performance_mode", "children": [{"name": "测试close performance mode返回正确的不支持响应", "uid": "b11d5a82657241fd", "parentUid": "95753a1df5aed1e2d1f53aaac547d09e", "status": "failed", "time": {"start": 1754489100666, "stop": 1754489100666, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "95753a1df5aed1e2d1f53aaac547d09e"}, {"name": "test_close_power_saving_mode", "children": [{"name": "测试close power saving mode返回正确的不支持响应", "uid": "bbeba40801831c5", "parentUid": "cc7047e4d72a0d0f3090e6371b0aadaf", "status": "failed", "time": {"start": 1754489134761, "stop": 1754489134761, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cc7047e4d72a0d0f3090e6371b0aadaf"}, {"name": "test_disable_accelerate_dialogue", "children": [{"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "d9281fe9adae9ba5", "parentUid": "eb416fd31892d7985df8f4a00fa61281", "status": "failed", "time": {"start": 1754489168952, "stop": 1754489168952, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eb416fd31892d7985df8f4a00fa61281"}, {"name": "test_disable_all_ai_magic_box_features", "children": [{"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "99fd73c7fe1b75de", "parentUid": "ae74f6051eb946be44a497a5345a6122", "status": "failed", "time": {"start": 1754489202925, "stop": 1754489202925, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ae74f6051eb946be44a497a5345a6122"}, {"name": "test_disable_auto_pickup", "children": [{"name": "测试disable auto pickup返回正确的不支持响应", "uid": "7f1ee703c6a94109", "parentUid": "12db62453bb023c9292784bafa6f3501", "status": "failed", "time": {"start": 1754489236847, "stop": 1754489236847, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12db62453bb023c9292784bafa6f3501"}, {"name": "test_disable_brightness_locking", "children": [{"name": "测试disable brightness locking返回正确的不支持响应", "uid": "1d9622fd407c2c96", "parentUid": "3dcacd9d4311d09538f3bfd9137d3407", "status": "failed", "time": {"start": 1754489271036, "stop": 1754489271036, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3dcacd9d4311d09538f3bfd9137d3407"}, {"name": "test_disable_call_rejection", "children": [{"name": "测试disable call rejection返回正确的不支持响应", "uid": "61e0a20218e2b282", "parentUid": "2df8406295adf2f3ca8780a6fd181bd8", "status": "failed", "time": {"start": 1754489304958, "stop": 1754489304958, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2df8406295adf2f3ca8780a6fd181bd8"}, {"name": "test_disable_hide_notifications", "children": [{"name": "测试disable hide notifications返回正确的不支持响应", "uid": "649ac03314c798ad", "parentUid": "4f012dc2e8bb211fd5ead2f1f23cd42d", "status": "failed", "time": {"start": 1754489339070, "stop": 1754489339070, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4f012dc2e8bb211fd5ead2f1f23cd42d"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "3d61731e0bcd9778", "parentUid": "a6c9b5ce2532611959a8b1cf0b7abdf0", "status": "failed", "time": {"start": 1754489373111, "stop": 1754489373111, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a6c9b5ce2532611959a8b1cf0b7abdf0"}, {"name": "test_disable_network_enhancement", "children": [{"name": "测试disable network enhancement返回正确的不支持响应", "uid": "21e4a005e4281783", "parentUid": "4c36c5378c3644d7cdbf63c08440a304", "status": "failed", "time": {"start": 1754489407275, "stop": 1754489407275, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c36c5378c3644d7cdbf63c08440a304"}, {"name": "test_disable_running_lock", "children": [{"name": "测试disable running lock返回正确的不支持响应", "uid": "88005cc383fdea8f", "parentUid": "bdd8ac18c65aee858c82eb5de9f7b502", "status": "failed", "time": {"start": 1754489441912, "stop": 1754489441912, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bdd8ac18c65aee858c82eb5de9f7b502"}, {"name": "test_disable_touch_optimization", "children": [{"name": "测试disable touch optimization返回正确的不支持响应", "uid": "9f960e8ed027c827", "parentUid": "7777d1894ae8ebdcc0703d8d7a1d888d", "status": "failed", "time": {"start": 1754489476371, "stop": 1754489476371, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7777d1894ae8ebdcc0703d8d7a1d888d"}, {"name": "test_disable_unfreeze", "children": [{"name": "测试disable unfreeze返回正确的不支持响应", "uid": "b0fa49b954710d82", "parentUid": "f4f38e300a9eadad2128e98bc9820a33", "status": "failed", "time": {"start": 1754489510717, "stop": 1754489510717, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f4f38e300a9eadad2128e98bc9820a33"}, {"name": "test_disable_zonetouch_master", "children": [{"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "cc78c9c9b8318173", "parentUid": "b2b3f74c91ec8af12a7b70a1858cb09f", "status": "failed", "time": {"start": 1754489545030, "stop": 1754489545030, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2b3f74c91ec8af12a7b70a1858cb09f"}, {"name": "test_download_basketball", "children": [{"name": "测试download basketball返回正确的不支持响应", "uid": "4634ae81442a4912", "parentUid": "ac5ff5b664434d007d90715847bb0efa", "status": "failed", "time": {"start": 1754489578969, "stop": 1754489578969, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ac5ff5b664434d007d90715847bb0efa"}, {"name": "test_driving_mode", "children": [{"name": "测试driving mode返回正确的不支持响应", "uid": "4d07f0c6c7ca376f", "parentUid": "e021bf6bc9463d8e3bd9aab9475fb30a", "status": "failed", "time": {"start": 1754489613176, "stop": 1754489613176, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e021bf6bc9463d8e3bd9aab9475fb30a"}, {"name": "test_enable_accelerate_dialogue", "children": [{"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "124a1f3e8edc5337", "parentUid": "29814f9b6b5bad3852e86d186b662dab", "status": "failed", "time": {"start": 1754489647368, "stop": 1754489647368, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "29814f9b6b5bad3852e86d186b662dab"}, {"name": "test_enable_all_ai_magic_box_features", "children": [{"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "18f399f8e5fa9ce7", "parentUid": "9b7a1bd9dfc15c1c490b49730b3b06c6", "status": "failed", "time": {"start": 1754489681401, "stop": 1754489681401, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9b7a1bd9dfc15c1c490b49730b3b06c6"}, {"name": "test_enable_auto_pickup", "children": [{"name": "测试enable auto pickup返回正确的不支持响应", "uid": "b7f4b9e941495e2d", "parentUid": "34950724c1fa063bc98eef873eb35771", "status": "failed", "time": {"start": 1754489715755, "stop": 1754489715755, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34950724c1fa063bc98eef873eb35771"}, {"name": "test_enable_brightness_locking", "children": [{"name": "测试enable brightness locking返回正确的不支持响应", "uid": "f44ba420ed70cb7f", "parentUid": "fb01cd9d24d4da8385ec6db44b9bf596", "status": "failed", "time": {"start": 1754489750061, "stop": 1754489750061, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb01cd9d24d4da8385ec6db44b9bf596"}, {"name": "test_enable_call_on_hold", "children": [{"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "c7e38f605f9066ee", "parentUid": "743e0a22021355320010c1959207f737", "status": "failed", "time": {"start": 1754489783936, "stop": 1754489783936, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "743e0a22021355320010c1959207f737"}, {"name": "test_enable_call_rejection", "children": [{"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "f56294cf89c411bc", "parentUid": "a7a8abf2aae8cc5f45b540e4f777f037", "status": "failed", "time": {"start": 1754489817654, "stop": 1754489817654, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a7a8abf2aae8cc5f45b540e4f777f037"}, {"name": "test_enable_network_enhancement", "children": [{"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "bf2f4d9af9c68c1c", "parentUid": "ecc995aaaa028fb08ab7ae1ab0e7875d", "status": "failed", "time": {"start": 1754489851594, "stop": 1754489851594, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ecc995aaaa028fb08ab7ae1ab0e7875d"}, {"name": "test_enable_running_lock", "children": [{"name": "测试enable running lock返回正确的不支持响应", "uid": "65dc203fdb610f69", "parentUid": "d576b618126fc7256f34985c398c1d3b", "status": "failed", "time": {"start": 1754489885466, "stop": 1754489885466, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d576b618126fc7256f34985c398c1d3b"}, {"name": "test_enable_touch_optimization", "children": [{"name": "测试enable touch optimization返回正确的不支持响应", "uid": "cd66040bc232be03", "parentUid": "8480a7d7a44bc6838b96d68a4c18aa05", "status": "failed", "time": {"start": 1754489919354, "stop": 1754489919354, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8480a7d7a44bc6838b96d68a4c18aa05"}, {"name": "test_enable_unfreeze", "children": [{"name": "测试enable unfreeze返回正确的不支持响应", "uid": "3ad176dfb601300e", "parentUid": "66a93da14003d58b4dfb0006f98aa4c5", "status": "failed", "time": {"start": 1754489953337, "stop": 1754489953337, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "66a93da14003d58b4dfb0006f98aa4c5"}, {"name": "test_enable_zonetouch_master", "children": [{"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "e1bbda90aff93d55", "parentUid": "8c6899d4ddce4d928d89f60cb07aeea9", "status": "failed", "time": {"start": 1754489987036, "stop": 1754489987036, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c6899d4ddce4d928d89f60cb07aeea9"}, {"name": "test_extend_the_image", "children": [{"name": "测试extend the image能正常执行", "uid": "9bb5d66bd0ffe1f8", "parentUid": "656e7206a16e23ae119789777b609183", "status": "failed", "time": {"start": 1754490021266, "stop": 1754490021266, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "656e7206a16e23ae119789777b609183"}, {"name": "test_go_home", "children": [{"name": "测试go home能正常执行", "uid": "29513ea621899e56", "parentUid": "4a5220f470f11d89726e23ef156eb88b", "status": "failed", "time": {"start": 1754490055356, "stop": 1754490055356, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a5220f470f11d89726e23ef156eb88b"}, {"name": "test_happy_new_year", "children": [{"name": "测试happy new year能正常执行", "uid": "64543549befa991e", "parentUid": "a0b2ddf900ecce63d74a6e5a2048669c", "status": "failed", "time": {"start": 1754490089116, "stop": 1754490089116, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a0b2ddf900ecce63d74a6e5a2048669c"}, {"name": "test_help_me_write_an_email", "children": [{"name": "测试help me write an email能正常执行", "uid": "eeead481a4592f65", "parentUid": "ef47f41b350d73a6140e48381c877229", "status": "failed", "time": {"start": 1754490123137, "stop": 1754490123137, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ef47f41b350d73a6140e48381c877229"}, {"name": "test_help_me_write_an_thanks_email", "children": [{"name": "测试help me write an thanks email能正常执行", "uid": "422547e10a7988ea", "parentUid": "b693f3729a9fcd922c000405462cc41b", "status": "failed", "time": {"start": 1754490157148, "stop": 1754490157148, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b693f3729a9fcd922c000405462cc41b"}, {"name": "test_help_me_write_an_thanks_letter", "children": [{"name": "测试help me write an thanks letter能正常执行", "uid": "c1f0bcc3af9f38f6", "parentUid": "bc1e1c7d2a07be4f8bd829a14683b10e", "status": "failed", "time": {"start": 1754490191042, "stop": 1754490191042, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bc1e1c7d2a07be4f8bd829a14683b10e"}, {"name": "test_how_to_set_screenshots", "children": [{"name": "测试how to set screenshots返回正确的不支持响应", "uid": "6bb6c7260139caa2", "parentUid": "98125eb3c0eb347bf93f6ebbb14e8847", "status": "failed", "time": {"start": 1754490225039, "stop": 1754490225039, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98125eb3c0eb347bf93f6ebbb14e8847"}, {"name": "test_increase_settings_for_special_functions", "children": [{"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "872f2d0df76fd076", "parentUid": "849c4d59d6ccd19947474d581af3ccb2", "status": "failed", "time": {"start": 1754490259190, "stop": 1754490259190, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "849c4d59d6ccd19947474d581af3ccb2"}, {"name": "test_jump_to_adaptive_brightness_settings", "children": [{"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "20fe2dd2bfdd7b37", "parentUid": "0355ddc9170b8d072304068f8868f806", "status": "failed", "time": {"start": 1754490292806, "stop": 1754490292806, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0355ddc9170b8d072304068f8868f806"}, {"name": "test_jump_to_ai_wallpaper_generator_settings", "children": [{"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "35d648522707fb53", "parentUid": "fb66326ff650d5ed24178fe23a3ec6c7", "status": "failed", "time": {"start": 1754490326450, "stop": 1754490326450, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb66326ff650d5ed24178fe23a3ec6c7"}, {"name": "test_jump_to_auto_rotate_screen_settings", "children": [{"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "6c0551c8476663fd", "parentUid": "4868992c324cca3aa80f892f5012edcc", "status": "failed", "time": {"start": 1754490350897, "stop": 1754490350897, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4868992c324cca3aa80f892f5012edcc"}, {"name": "test_jump_to_battery_and_power_saving", "children": [{"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "1736a9777edb77af", "parentUid": "c39df595d254493d9e1cf180846ee18a", "status": "failed", "time": {"start": 1754490376488, "stop": 1754490376488, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c39df595d254493d9e1cf180846ee18a"}, {"name": "test_jump_to_battery_usage", "children": [{"name": "测试jump to battery usage返回正确的不支持响应", "uid": "ee7c08861f1d4369", "parentUid": "0bd7fb8ef7e8ca2e3a1a1699257e50f9", "status": "failed", "time": {"start": 1754490402469, "stop": 1754490402469, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0bd7fb8ef7e8ca2e3a1a1699257e50f9"}, {"name": "test_jump_to_call_notifications", "children": [{"name": "测试jump to call notifications返回正确的不支持响应", "uid": "53d43e24245acf0a", "parentUid": "5a05d3bb6cee7f72819e49d4f6d5ee1f", "status": "failed", "time": {"start": 1754490428166, "stop": 1754490428166, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a05d3bb6cee7f72819e49d4f6d5ee1f"}, {"name": "test_jump_to_high_brightness_mode_settings", "children": [{"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "5f1d181210935ac4", "parentUid": "4a8b87bfde2b2e8ce7967634be5288ab", "status": "failed", "time": {"start": 1754490474108, "stop": 1754490474108, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a8b87bfde2b2e8ce7967634be5288ab"}, {"name": "test_jump_to_lock_screen_notification_and_display_settings", "children": [{"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "5f1cafc0f7162a3b", "parentUid": "cfbc1116cbdd7d5906a09ffc432b57db", "status": "failed", "time": {"start": 1754490512810, "stop": 1754490512810, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cfbc1116cbdd7d5906a09ffc432b57db"}, {"name": "test_jump_to_nfc_settings", "children": [{"name": "测试jump to nfc settings", "uid": "29843c65bf2d03c6", "parentUid": "0285fb88f545482d120dd7eeddef468a", "status": "failed", "time": {"start": 1754490548481, "stop": 1754490548481, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0285fb88f545482d120dd7eeddef468a"}, {"name": "test_jump_to_notifications_and_status_bar_settings", "children": [{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "c21cde7c398161d9", "parentUid": "f6098b8dc4a6271dec4f5e5f57bd4752", "status": "failed", "time": {"start": 1754490583181, "stop": 1754490583181, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f6098b8dc4a6271dec4f5e5f57bd4752"}, {"name": "test_kill_whatsapp", "children": [{"name": "测试kill whatsapp能正常执行", "uid": "65c217eaa39ab993", "parentUid": "48a28da8e5e405bb518e2fc0c08602a0", "status": "failed", "time": {"start": 1754490617576, "stop": 1754490617576, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "48a28da8e5e405bb518e2fc0c08602a0"}, {"name": "test_modify_grape_timbre", "children": [{"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "3ca37dd9f27c6ecc", "parentUid": "09910b981ee980bf81421c1efd746a52", "status": "failed", "time": {"start": 1754490651448, "stop": 1754490651448, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "09910b981ee980bf81421c1efd746a52"}, {"name": "test_more_settings", "children": [{"name": "测试more settings返回正确的不支持响应", "uid": "97abb069c96ff50c", "parentUid": "ca0b29ba7554057ab3ba8216b6b1f0ba", "status": "failed", "time": {"start": 1754490685463, "stop": 1754490685463, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ca0b29ba7554057ab3ba8216b6b1f0ba"}, {"name": "test_navigation_to_the_address_in_the_image", "children": [{"name": "测试navigation to the address in thie image能正常执行", "uid": "26798eff8ed9e72", "parentUid": "6e2b1fe773a9723006e50fc18b054a32", "status": "failed", "time": {"start": 1754490719855, "stop": 1754490719855, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e2b1fe773a9723006e50fc18b054a32"}, {"name": "test_navigation_to_the_first_address_in_the_image", "children": [{"name": "测试navigation to the first address in the image能正常执行", "uid": "18f772aa522a4c5", "parentUid": "930b7b34cf2d90f55ec5996ae313b76b", "status": "failed", "time": {"start": 1754490754497, "stop": 1754490754497, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "930b7b34cf2d90f55ec5996ae313b76b"}, {"name": "test_open_font_family_settings", "children": [{"name": "测试open font family settings返回正确的不支持响应", "uid": "3eed261f9745ce86", "parentUid": "5d1c4a01c8558acb00bb5c6e528035a3", "status": "failed", "time": {"start": 1754490788673, "stop": 1754490788673, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5d1c4a01c8558acb00bb5c6e528035a3"}, {"name": "test_open_notification_ringtone_settings", "children": [{"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "65e88368633e1d8a", "parentUid": "309e531c38122a270a22145c2b2f4a8a", "status": "failed", "time": {"start": 1754490822706, "stop": 1754490822706, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "309e531c38122a270a22145c2b2f4a8a"}, {"name": "test_open_whatsapp", "children": [{"name": "测试open whatsapp", "uid": "720a5005d504c9d4", "parentUid": "fae42569d3565f4a0509b1abe481814e", "status": "failed", "time": {"start": 1754490856630, "stop": 1754490856630, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fae42569d3565f4a0509b1abe481814e"}, {"name": "test_order_a_burger", "children": [{"name": "测试order a burger返回正确的不支持响应", "uid": "5a9de1902905bdb5", "parentUid": "12a853573a3ed32b45369299151ae46b", "status": "failed", "time": {"start": 1754490890686, "stop": 1754490890686, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12a853573a3ed32b45369299151ae46b"}, {"name": "test_order_a_takeaway", "children": [{"name": "测试order a takeaway返回正确的不支持响应", "uid": "13d28d73850399d4", "parentUid": "6116229fe40039026544aa05527a4376", "status": "failed", "time": {"start": 1754490924820, "stop": 1754490924820, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6116229fe40039026544aa05527a4376"}, {"name": "test_parking_space", "children": [{"name": "测试parking space能正常执行", "uid": "c701032e4d34f79", "parentUid": "d235978673eeb20a0aa5a17d0b9af2fb", "status": "failed", "time": {"start": 1754490958969, "stop": 1754490958969, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d235978673eeb20a0aa5a17d0b9af2fb"}, {"name": "test_play_football_video_by_youtube", "children": [{"name": "测试play football video by youtube", "uid": "9d20fb01702a451", "parentUid": "1b38b53340d3cf404c7d3184db4b3767", "status": "failed", "time": {"start": 1754490993122, "stop": 1754490993122, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b38b53340d3cf404c7d3184db4b3767"}, {"name": "test_play_love_sotry", "children": [{"name": "测试play love sotry", "uid": "960f945a0e52aa89", "parentUid": "3279616bb2594ec7b639e82e288080c3", "status": "failed", "time": {"start": 1754491027353, "stop": 1754491027353, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3279616bb2594ec7b639e82e288080c3"}, {"name": "test_play_taylor_swift_s_song_love_sotry", "children": [{"name": "测试play taylor swift‘s song love story", "uid": "27fab215254917dc", "parentUid": "7c3b9cf1e80a1543e429a21b6580f11f", "status": "failed", "time": {"start": 1754491061081, "stop": 1754491061081, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c3b9cf1e80a1543e429a21b6580f11f"}, {"name": "test_play_the_album", "children": [{"name": "测试play the album", "uid": "84efe235da887661", "parentUid": "96bab08553e379e0c0029cca77667f8f", "status": "failed", "time": {"start": 1754491094993, "stop": 1754491094993, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "96bab08553e379e0c0029cca77667f8f"}, {"name": "test_play_video", "children": [{"name": "测试play video", "uid": "95b3160676d4ff72", "parentUid": "a38748b3b1f409e94ff601029a63f98d", "status": "failed", "time": {"start": 1754491128564, "stop": 1754491128564, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a38748b3b1f409e94ff601029a63f98d"}, {"name": "test_play_video_by_youtube", "children": [{"name": "测试play video by youtube", "uid": "5c4e4711bd0d8a", "parentUid": "c7f371ce0f7eeb044f6c854937a7502b", "status": "failed", "time": {"start": 1754491162717, "stop": 1754491162717, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c7f371ce0f7eeb044f6c854937a7502b"}, {"name": "test_please_show_me_where_i_am", "children": [{"name": "测试please show me where i am能正常执行", "uid": "971d76c9403829e0", "parentUid": "a856f6c3dff3e6d986ecbf33df7fabe2", "status": "failed", "time": {"start": 1754491196950, "stop": 1754491196950, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a856f6c3dff3e6d986ecbf33df7fabe2"}, {"name": "test_pls_open_whatsapp", "children": [{"name": "测试pls open whatsapp", "uid": "c2c64f3d874f56de", "parentUid": "3dc012d4a4449a7636b0642c22bceb9e", "status": "failed", "time": {"start": 1754491230942, "stop": 1754491230942, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3dc012d4a4449a7636b0642c22bceb9e"}, {"name": "test_redial", "children": [{"name": "测试redial", "uid": "936da91c6ba88125", "parentUid": "494d3e1b731af3bce21d72c38e17e357", "status": "failed", "time": {"start": 1754491264848, "stop": 1754491264848, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "494d3e1b731af3bce21d72c38e17e357"}, {"name": "test_reset_phone", "children": [{"name": "测试reset phone返回正确的不支持响应", "uid": "6c2113f7cf07a5f1", "parentUid": "97792796a4c38ae78b9db124406767b2", "status": "failed", "time": {"start": 1754491298781, "stop": 1754491298781, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "97792796a4c38ae78b9db124406767b2"}, {"name": "test_restart_my_phone", "children": [{"name": "测试restart my phone能正常执行", "uid": "3ffde2e2205e6490", "parentUid": "c1ea683fb9d174ed7b5f8880d961bd37", "status": "failed", "time": {"start": 1754491332571, "stop": 1754491332571, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c1ea683fb9d174ed7b5f8880d961bd37"}, {"name": "test_restart_the_phone", "children": [{"name": "测试restart the phone能正常执行", "uid": "a25189f4c41cea26", "parentUid": "53adce70cf390992f02ff700aaada272", "status": "failed", "time": {"start": 1754491366575, "stop": 1754491366575, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "53adce70cf390992f02ff700aaada272"}, {"name": "test_search_for_addresses_on_the_screen", "children": [{"name": "测试Search for addresses on the screen能正常执行", "uid": "1ee5d8b15798efff", "parentUid": "735baa4624c52854b8a2a73abb447f23", "status": "failed", "time": {"start": 1754491400647, "stop": 1754491400647, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "735baa4624c52854b8a2a73abb447f23"}, {"name": "test_search_the_address_in_the_image", "children": [{"name": "测试search the address in the image能正常执行", "uid": "62c368eb30849dec", "parentUid": "cba35d4f03acbacfc1c9c3b0792e089b", "status": "failed", "time": {"start": 1754491434814, "stop": 1754491434814, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cba35d4f03acbacfc1c9c3b0792e089b"}, {"name": "test_search_whatsapp_for_me", "children": [{"name": "测试search whatsapp for me能正常执行", "uid": "364c7273e7ac42ee", "parentUid": "7c7aef6b5854cef222cfb44ffc5056fe", "status": "failed", "time": {"start": 1754491468681, "stop": 1754491468681, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c7aef6b5854cef222cfb44ffc5056fe"}, {"name": "test_set_app_auto_rotate", "children": [{"name": "测试set app auto rotate返回正确的不支持响应", "uid": "9496f06be9b0368f", "parentUid": "42a0d4edd729031d01ed4ce54f32a86f", "status": "failed", "time": {"start": 1754491502765, "stop": 1754491502765, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "42a0d4edd729031d01ed4ce54f32a86f"}, {"name": "test_set_app_notifications", "children": [{"name": "测试set app notifications返回正确的不支持响应", "uid": "9f1b567357aa63da", "parentUid": "f17166693c717b681b5c4ace219a5254", "status": "failed", "time": {"start": 1754491536654, "stop": 1754491536654, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f17166693c717b681b5c4ace219a5254"}, {"name": "test_set_battery_saver_settings", "children": [{"name": "测试set battery saver settings返回正确的不支持响应", "uid": "21b7d6cbcb6ed0b3", "parentUid": "90688cbd93c016ee7d407e660ac88b04", "status": "failed", "time": {"start": 1754491570571, "stop": 1754491570571, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "90688cbd93c016ee7d407e660ac88b04"}, {"name": "test_set_call_back_with_last_used_sim", "children": [{"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "d000d74ce17fdd03", "parentUid": "27c7eefc61a4f26939cc8e62add52a75", "status": "failed", "time": {"start": 1754491604681, "stop": 1754491604681, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "27c7eefc61a4f26939cc8e62add52a75"}, {"name": "test_set_color_style", "children": [{"name": "测试set color style返回正确的不支持响应", "uid": "2d060a6b4f115242", "parentUid": "4a9eb6eed41e8d978e3f2271d25aeeb9", "status": "failed", "time": {"start": 1754491638468, "stop": 1754491638468, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a9eb6eed41e8d978e3f2271d25aeeb9"}, {"name": "test_set_compatibility_mode", "children": [{"name": "测试set compatibility mode返回正确的不支持响应", "uid": "e4f39a55dd8c7a46", "parentUid": "71f9d2696419dde9e2fbc887cd35352f", "status": "failed", "time": {"start": 1754491672752, "stop": 1754491672752, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71f9d2696419dde9e2fbc887cd35352f"}, {"name": "test_set_cover_screen_apps", "children": [{"name": "测试set cover screen apps返回正确的不支持响应", "uid": "1e0c6b7b2439c45e", "parentUid": "3e469638b07e0f748f137f566308ee39", "status": "failed", "time": {"start": 1754491707155, "stop": 1754491707155, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e469638b07e0f748f137f566308ee39"}, {"name": "test_set_customized_cover_screen", "children": [{"name": "测试set customized cover screen返回正确的不支持响应", "uid": "b7758b8752b969b0", "parentUid": "681063f39b4b3d4670516dc945bc7e8f", "status": "failed", "time": {"start": 1754491741363, "stop": 1754491741363, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "681063f39b4b3d4670516dc945bc7e8f"}, {"name": "test_set_date_time", "children": [{"name": "测试set date & time返回正确的不支持响应", "uid": "1302051b604a5885", "parentUid": "55afb5474f6367c035d3c3b4c5aa4a0e", "status": "failed", "time": {"start": 1754491775165, "stop": 1754491775165, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "55afb5474f6367c035d3c3b4c5aa4a0e"}, {"name": "test_set_edge_mistouch_prevention", "children": [{"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "81503ead67be5ff1", "parentUid": "d063298b5e7b7ba1dd78f28933196633", "status": "failed", "time": {"start": 1754491809607, "stop": 1754491809607, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d063298b5e7b7ba1dd78f28933196633"}, {"name": "test_set_flex_still_mode", "children": [{"name": "测试set flex-still mode返回正确的不支持响应", "uid": "281d4eee43b52921", "parentUid": "fb37e27acd491ebde3cb6675b3ff4b33", "status": "failed", "time": {"start": 1754491843808, "stop": 1754491843808, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb37e27acd491ebde3cb6675b3ff4b33"}, {"name": "test_set_flip_case_feature", "children": [{"name": "测试set flip case feature返回正确的不支持响应", "uid": "48fec51a8fe153c0", "parentUid": "d093123b5193b9aead35dd1996c7cd1a", "status": "failed", "time": {"start": 1754491877618, "stop": 1754491877618, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d093123b5193b9aead35dd1996c7cd1a"}, {"name": "test_set_floating_windows", "children": [{"name": "测试set floating windows返回正确的不支持响应", "uid": "a45dfd46ac1039a0", "parentUid": "260060301e90f5edbed593882a5fec0d", "status": "failed", "time": {"start": 1754491911964, "stop": 1754491911964, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "260060301e90f5edbed593882a5fec0d"}, {"name": "test_set_folding_screen_zone", "children": [{"name": "测试set folding screen zone返回正确的不支持响应", "uid": "3139a90de321bf94", "parentUid": "d7d6eb8f64580ac8b8fb024a954198fa", "status": "failed", "time": {"start": 1754491946633, "stop": 1754491946633, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7d6eb8f64580ac8b8fb024a954198fa"}, {"name": "test_set_font_size", "children": [{"name": "测试set font size返回正确的不支持响应", "uid": "c6e0954bd4fb0f5f", "parentUid": "740d4f9856207354991fc052a0633f21", "status": "failed", "time": {"start": 1754491981088, "stop": 1754491981088, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "740d4f9856207354991fc052a0633f21"}, {"name": "test_set_gesture_navigation", "children": [{"name": "测试set gesture navigation返回正确的不支持响应", "uid": "7fbeb58f6ba7a50", "parentUid": "47320f9ee2ac872ac9be9e52d1af28d8", "status": "failed", "time": {"start": 1754492015552, "stop": 1754492015552, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47320f9ee2ac872ac9be9e52d1af28d8"}, {"name": "test_set_languages", "children": [{"name": "测试set languages返回正确的不支持响应", "uid": "75a4fdcde784a72f", "parentUid": "46ab090271dad35a2f22e1aa891d167d", "status": "failed", "time": {"start": 1754492049446, "stop": 1754492049446, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "46ab090271dad35a2f22e1aa891d167d"}, {"name": "test_set_lockscreen_passwords", "children": [{"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "cee648585821b0fe", "parentUid": "1ebb1fb5844374f960b3ecd76cddec92", "status": "failed", "time": {"start": 1754492083595, "stop": 1754492083595, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1ebb1fb5844374f960b3ecd76cddec92"}, {"name": "test_set_my_fonts", "children": [{"name": "测试set my fonts返回正确的不支持响应", "uid": "ea9bd37543fd3683", "parentUid": "c63b678cbbe7de452848a070298e3fab", "status": "failed", "time": {"start": 1754492118018, "stop": 1754492118019, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c63b678cbbe7de452848a070298e3fab"}, {"name": "test_set_my_themes", "children": [{"name": "测试set my themes返回正确的不支持响应", "uid": "3004ceb745527485", "parentUid": "452ed970a52e7ee9049bbdb424f9b57d", "status": "failed", "time": {"start": 1754492151822, "stop": 1754492151822, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "452ed970a52e7ee9049bbdb424f9b57d"}, {"name": "test_set_off_a_firework", "children": [{"name": "测试set off a firework能正常执行", "uid": "5efaff886e3fa80b", "parentUid": "bd299436c9a75170d414625cc2cf2562", "status": "failed", "time": {"start": 1754492185666, "stop": 1754492185666, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bd299436c9a75170d414625cc2cf2562"}, {"name": "test_set_parallel_windows", "children": [{"name": "测试set parallel windows返回正确的不支持响应", "uid": "131444e9a0aa3b4a", "parentUid": "b7b2179becb10c93603c2fcce15e0006", "status": "failed", "time": {"start": 1754492219671, "stop": 1754492219671, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b7b2179becb10c93603c2fcce15e0006"}, {"name": "test_set_personal_hotspot", "children": [{"name": "测试set personal hotspot返回正确的不支持响应", "uid": "a0c877bf5b68dcb1", "parentUid": "4fed48034322a5d5119c937240c4aa77", "status": "failed", "time": {"start": 1754492253715, "stop": 1754492253715, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4fed48034322a5d5119c937240c4aa77"}, {"name": "test_set_phantom_v_pen", "children": [{"name": "测试set phantom v pen返回正确的不支持响应", "uid": "d8fd0397f7c4e6aa", "parentUid": "7b8efe12e2cc4a1e4fec01a67e1ead0d", "status": "failed", "time": {"start": 1754492287573, "stop": 1754492287573, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b8efe12e2cc4a1e4fec01a67e1ead0d"}, {"name": "test_set_phone_number", "children": [{"name": "测试set phone number返回正确的不支持响应", "uid": "36e3d9257c05a679", "parentUid": "f0d8d3a4b043370c607b0bb64b8df1bd", "status": "failed", "time": {"start": 1754492321593, "stop": 1754492321593, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f0d8d3a4b043370c607b0bb64b8df1bd"}, {"name": "test_set_scheduled_power_on_off_and_restart", "children": [{"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "279ad67ed21598d8", "parentUid": "2095cb76657298bca8aa08a5ddc4452c", "status": "failed", "time": {"start": 1754492355647, "stop": 1754492355647, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2095cb76657298bca8aa08a5ddc4452c"}, {"name": "test_set_screen_refresh_rate", "children": [{"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "b5759ffb8039a256", "parentUid": "d9cca6f6b44bb5f9a4608c3db7a6bb1b", "status": "failed", "time": {"start": 1754492389562, "stop": 1754492389562, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9cca6f6b44bb5f9a4608c3db7a6bb1b"}, {"name": "test_set_screen_relay", "children": [{"name": "测试set screen relay返回正确的不支持响应", "uid": "2df8c73485357aa8", "parentUid": "66dc5bdcc3e27c2ae07a1536c41488cd", "status": "failed", "time": {"start": 1754492424236, "stop": 1754492424236, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "66dc5bdcc3e27c2ae07a1536c41488cd"}, {"name": "test_set_screen_timeout", "children": [{"name": "测试set screen timeout返回正确的不支持响应", "uid": "6af11f61922afd2f", "parentUid": "efb5e97f214d22c0692101b1024ecde2", "status": "failed", "time": {"start": 1754492458670, "stop": 1754492458670, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "efb5e97f214d22c0692101b1024ecde2"}, {"name": "test_set_screen_to_minimum_brightness", "children": [{"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "b4f4ccf9053281c7", "parentUid": "e1c10086104522b2fbc3cd552709a125", "status": "failed", "time": {"start": 1754492493249, "stop": 1754492493249, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e1c10086104522b2fbc3cd552709a125"}, {"name": "test_set_sim_ringtone", "children": [{"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "de40562d3921e98a", "parentUid": "02b906bfcb368697dfb809905922e9cb", "status": "failed", "time": {"start": 1754492527633, "stop": 1754492527633, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "02b906bfcb368697dfb809905922e9cb"}, {"name": "test_set_smart_hub", "children": [{"name": "测试set smart hub返回正确的不支持响应", "uid": "e1ee406f1611eb73", "parentUid": "1b885e8f75c6cb8827815cfc72755c55", "status": "failed", "time": {"start": 1754492562170, "stop": 1754492562170, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b885e8f75c6cb8827815cfc72755c55"}, {"name": "test_set_smart_panel", "children": [{"name": "测试set smart panel返回正确的不支持响应", "uid": "a79d5f2983e68bda", "parentUid": "116ddf2803a2d3fa8977f8f2b43e59a3", "status": "failed", "time": {"start": 1754492596508, "stop": 1754492596508, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "116ddf2803a2d3fa8977f8f2b43e59a3"}, {"name": "test_set_special_function", "children": [{"name": "测试set special function返回正确的不支持响应", "uid": "d5f2f0dc1b836163", "parentUid": "1cdef3d89962b3a41ead4e6a44623984", "status": "failed", "time": {"start": 1754492630353, "stop": 1754492630353, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1cdef3d89962b3a41ead4e6a44623984"}, {"name": "test_set_split_screen_apps", "children": [{"name": "测试set split-screen apps返回正确的不支持响应", "uid": "1ae31f6c836ef647", "parentUid": "df5405aa0bd507fb6dc22f263be7dfef", "status": "failed", "time": {"start": 1754492664470, "stop": 1754492664470, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df5405aa0bd507fb6dc22f263be7dfef"}, {"name": "test_set_timezone", "children": [{"name": "测试set timezone返回正确的不支持响应", "uid": "393c6d5c28a42af0", "parentUid": "9e7c120412bd7e0f730e61377785fa8a", "status": "failed", "time": {"start": 1754492698847, "stop": 1754492698847, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e7c120412bd7e0f730e61377785fa8a"}, {"name": "test_set_ultra_power_saving", "children": [{"name": "测试set ultra power saving返回正确的不支持响应", "uid": "762351f9affe7b3a", "parentUid": "5cae1c2c93d377af57591ba886a396b7", "status": "failed", "time": {"start": 1754492733532, "stop": 1754492733532, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cae1c2c93d377af57591ba886a396b7"}, {"name": "test_start_running", "children": [{"name": "测试start running能正常执行", "uid": "a4c01f6014bd6c51", "parentUid": "8fdaf0f924e8731506064256cfd9d49e", "status": "failed", "time": {"start": 1754492767858, "stop": 1754492767858, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8fdaf0f924e8731506064256cfd9d49e"}, {"name": "test_start_walking", "children": [{"name": "测试start walking能正常执行", "uid": "5d77080ffe1fccbb", "parentUid": "4a8c9e4aef8cc53d5214b16790f29ecf", "status": "failed", "time": {"start": 1754492802065, "stop": 1754492802065, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a8c9e4aef8cc53d5214b16790f29ecf"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "8f35d4cdbada546a", "parentUid": "eaf66c431b1bc4cdb440f63e804d5b7f", "status": "failed", "time": {"start": 1754492835997, "stop": 1754492835997, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eaf66c431b1bc4cdb440f63e804d5b7f"}, {"name": "test_switch_to_performance_mode", "children": [{"name": "测试switch to performance mode返回正确的不支持响应", "uid": "ab6461e475482ce", "parentUid": "9c64338b2e6a23f5945b6c45a6e8988f", "status": "failed", "time": {"start": 1754492870120, "stop": 1754492870120, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9c64338b2e6a23f5945b6c45a6e8988f"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "342df515163bc531", "parentUid": "0a89b8197df33175fe755b5815c5229b", "status": "failed", "time": {"start": 1754492904126, "stop": 1754492904126, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0a89b8197df33175fe755b5815c5229b"}, {"name": "test_switching_charging_speed", "children": [{"name": "测试switching charging speed能正常执行", "uid": "19f30a50229fa046", "parentUid": "556ad59feb219cf0e64a09e0c312ef81", "status": "failed", "time": {"start": 1754492938029, "stop": 1754492938029, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "556ad59feb219cf0e64a09e0c312ef81"}, {"name": "test_take_notes", "children": [{"name": "测试take notes能正常执行", "uid": "787d73d161918578", "parentUid": "7509a983d8243a8effb8a61f465b5c1a", "status": "failed", "time": {"start": 1754492972187, "stop": 1754492972187, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7509a983d8243a8effb8a61f465b5c1a"}, {"name": "test_tell_me_joke", "children": [{"name": "测试tell me joke能正常执行", "uid": "9e625edf27bc68e4", "parentUid": "cb157e6b5cd8cba7c4577e10e0dd7c22", "status": "failed", "time": {"start": 1754493006073, "stop": 1754493006073, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cb157e6b5cd8cba7c4577e10e0dd7c22"}, {"name": "test_the_second", "children": [{"name": "测试the second返回正确的不支持响应", "uid": "5673296d3573d0a2", "parentUid": "33f765d60f4eb02aa8bf10da2e90a0c5", "status": "failed", "time": {"start": 1754493040820, "stop": 1754493040820, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "33f765d60f4eb02aa8bf10da2e90a0c5"}, {"name": "test_turn_off_driving_mode", "children": [{"name": "测试turn off driving mode返回正确的不支持响应", "uid": "72369a52d654526c", "parentUid": "07d91c6eb72b9bf62af23569018b6ed9", "status": "failed", "time": {"start": 1754493075432, "stop": 1754493075432, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "07d91c6eb72b9bf62af23569018b6ed9"}, {"name": "test_turn_off_show_battery_percentage", "children": [{"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "79a4858764b02126", "parentUid": "1da9f8360bfb0009b7bbba3b558b5ad6", "status": "failed", "time": {"start": 1754493109867, "stop": 1754493109867, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1da9f8360bfb0009b7bbba3b558b5ad6"}, {"name": "test_turn_on_driving_mode", "children": [{"name": "测试turn on driving mode返回正确的不支持响应", "uid": "93ff373023646cf6", "parentUid": "fd2d2a21ef2aa878399c88b3d0e934ba", "status": "failed", "time": {"start": 1754493143901, "stop": 1754493143901, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fd2d2a21ef2aa878399c88b3d0e934ba"}, {"name": "test_turn_on_high_brightness_mode", "children": [{"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "e9e9c48b1db33302", "parentUid": "49b44a51671f9fc053854051c80f7c70", "status": "failed", "time": {"start": 1754493177669, "stop": 1754493177669, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "49b44a51671f9fc053854051c80f7c70"}, {"name": "test_turn_on_show_battery_percentage", "children": [{"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "921d2165e4b8b2c6", "parentUid": "838b34c8fa65f970357a94fb9eaf0dcc", "status": "failed", "time": {"start": 1754493211769, "stop": 1754493211769, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "838b34c8fa65f970357a94fb9eaf0dcc"}, {"name": "test_vedio_call_number_by_whatsapp", "children": [{"name": "测试vedio call number by whatsapp能正常执行", "uid": "7eb514c65b1432a5", "parentUid": "9d137f0f051ce9331aca868c940ab405", "status": "failed", "time": {"start": 1754493246248, "stop": 1754493246248, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d137f0f051ce9331aca868c940ab405"}, {"name": "test_voice_setting_page", "children": [{"name": "测试Voice setting page返回正确的不支持响应", "uid": "b883efebf4029015", "parentUid": "c2e243e17e6068d0d67070f2c20e8486", "status": "failed", "time": {"start": 1754493280335, "stop": 1754493280335, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c2e243e17e6068d0d67070f2c20e8486"}, {"name": "test_what_date_is_it", "children": [{"name": "测试what date is it能正常执行", "uid": "7452352603fb4f0", "parentUid": "91a4dbcca261fa96c4858bdb888ab618", "status": "failed", "time": {"start": 1754493314194, "stop": 1754493314194, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "91a4dbcca261fa96c4858bdb888ab618"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "182ba89eca203a55", "parentUid": "de0c4647cd25ea2211f7b7d6173d4170", "status": "failed", "time": {"start": 1754493348988, "stop": 1754493348988, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "de0c4647cd25ea2211f7b7d6173d4170"}, {"name": "test_what_time_is_it_in_london", "children": [{"name": "测试what time is it in London能正常执行", "uid": "b68ac7588583315d", "parentUid": "73e8c462f6a0b1457c865212c98a100e", "status": "failed", "time": {"start": 1754493383221, "stop": 1754493383221, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "73e8c462f6a0b1457c865212c98a100e"}, {"name": "test_yandex_eats", "children": [{"name": "测试yandex eats返回正确的不支持响应", "uid": "70d9a7a738f698e4", "parentUid": "ffa74429a7c2fa0151c7e7ef27b25ecb", "status": "failed", "time": {"start": 1754493417107, "stop": 1754493417107, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ffa74429a7c2fa0151c7e7ef27b25ecb"}], "uid": "e416b8e9994852e8ed797dec283160f6"}], "uid": "testcases.test_ella"}]}