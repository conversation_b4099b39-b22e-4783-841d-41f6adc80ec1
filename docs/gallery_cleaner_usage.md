# 相册清理工具使用指南

## 概述

相册清理工具提供了一套完整的解决方案，用于在测试过程中清理Android设备的相册文件夹。该工具参照 `file_pusher.py` 的设计模式，提供了灵活、可靠的相册清理功能。

## 核心组件

### 1. GalleryCleaner 类 (`tools/gallery_cleaner.py`)

主要的相册清理工具类，提供以下功能：

- 清理指定类型的相册文件夹
- 支持按文件类型过滤清理
- 清理自定义路径
- 获取相册状态信息
- 自动刷新媒体库

### 2. GalleryCleanupMixin 类 (`testcases/mixins/gallery_cleanup_mixin.py`)

为测试用例提供相册清理功能的混入类，包含：

- 多种清理fixture
- 便捷的清理方法
- 状态检查功能
- Allure报告集成

## 支持的文件夹类型

```python
gallery_paths = {
    'camera': '/sdcard/DCIM/Camera',        # 相机文件夹
    'pictures': '/sdcard/Pictures',         # 图片文件夹
    'download': '/sdcard/Download',         # 下载文件夹
    'screenshots': '/sdcard/Pictures/Screenshots',  # 截图文件夹
    'dcim': '/sdcard/DCIM',                # DCIM文件夹
    'movies': '/sdcard/Movies',            # 视频文件夹
    'music': '/sdcard/Music',              # 音乐文件夹
    'documents': '/sdcard/Documents'        # 文档文件夹
}
```

## 支持的文件类型

```python
media_extensions = {
    'images': ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg'],
    'videos': ['mp4', 'avi', 'mov', 'mkv', '3gp', 'wmv', 'flv', 'webm'],
    'audio': ['mp3', 'wav', 'aac', 'flac', 'ogg', 'm4a', 'wma'],
    'documents': ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt', 'xls', 'xlsx', 'ppt', 'pptx']
}
```

## 使用方法

### 1. 直接使用工具类

```python
from tools.gallery_cleaner import gallery_cleaner

# 清理相机文件夹
gallery_cleaner.clear_gallery_folder('camera')

# 清理指定类型的文件
gallery_cleaner.clear_gallery_folder('pictures', ['images', 'videos'])

# 清理所有相册文件夹
gallery_cleaner.clear_all_gallery_folders()

# 清理自定义路径
gallery_cleaner.clear_custom_path('/sdcard/MyFolder')

# 获取相册状态
status = gallery_cleaner.get_gallery_status()
```

### 2. 在测试用例中使用混入类

```python
from testcases.mixins.gallery_cleanup_mixin import BaseGalleryTest

class TestMyFeature(BaseGalleryTest):
    
    def test_with_auto_cleanup(self, clean_gallery_before_test):
        """使用fixture自动清理"""
        # 测试逻辑
        pass
    
    def test_manual_cleanup(self):
        """手动清理"""
        # 清理指定文件夹
        self.clean_gallery_folders(['camera', 'pictures'])
        
        # 测试逻辑
        pass
    
    def test_ensure_clean(self):
        """确保环境干净"""
        self.ensure_clean_gallery(['camera'])
        
        # 测试逻辑
        pass
```

### 3. 使用便捷函数

```python
from tools.gallery_cleaner import (
    clear_camera_folder,
    clear_pictures_folder, 
    clear_download_folder,
    clear_all_galleries
)

# 清理相机文件夹
clear_camera_folder()

# 清理所有相册
clear_all_galleries()
```

## 可用的Fixture

### 1. clean_gallery_before_test
测试前自动清理所有相册文件夹

```python
def test_example(self, clean_gallery_before_test):
    # 相册已被清理
    pass
```

### 2. clean_camera_only
仅清理相机文件夹

```python
def test_camera_feature(self, clean_camera_only):
    # 相机文件夹已被清理
    pass
```

### 3. clean_download_only
仅清理下载文件夹

```python
def test_download_feature(self, clean_download_only):
    # 下载文件夹已被清理
    pass
```

## 便捷方法

### 清理方法
- `clean_camera_folder()` - 清理相机文件夹
- `clean_pictures_folder()` - 清理图片文件夹
- `clean_download_folder()` - 清理下载文件夹
- `clean_screenshots_folder()` - 清理截图文件夹
- `clean_all_media_folders()` - 清理所有媒体文件夹

### 状态方法
- `get_gallery_status()` - 获取相册状态
- `ensure_clean_gallery()` - 确保相册干净

## 高级用法

### 1. 按文件类型清理

```python
# 只清理图片和视频
self.clean_gallery_folders(['camera'], ['images', 'videos'])

# 只清理文档
self.clean_gallery_folders(['download'], ['documents'])
```

### 2. 清理自定义路径

```python
# 清理自定义文件夹
self.clean_custom_gallery_path('/sdcard/MyApp/Cache')
```

### 3. 获取详细状态

```python
status = self.get_gallery_status()
for folder_type, info in status.items():
    print(f"{folder_type}: {info['file_count']} 个文件")
```

## 注意事项

1. **权限要求**: 需要设备已连接并启用USB调试
2. **文件恢复**: 删除的文件无法恢复，请谨慎使用
3. **媒体库刷新**: 清理后会自动刷新媒体库，可能需要几秒钟
4. **错误处理**: 工具会优雅处理各种错误情况
5. **日志记录**: 所有操作都会记录详细日志

## 测试示例

运行示例测试：

```bash
# 运行所有相册清理测试
pytest testcases/test_gallery_cleanup_example.py -v

# 运行特定测试
pytest testcases/test_gallery_cleanup_example.py::TestGalleryCleanupExample::test_complete_cleanup_workflow -v

# 生成Allure报告
pytest testcases/test_gallery_cleanup_example.py --allure-results-dir=reports/allure-results
allure serve reports/allure-results
```

## 故障排除

### 常见问题

1. **设备未连接**
   - 检查USB连接
   - 确认USB调试已启用
   - 运行 `adb devices` 确认设备可见

2. **权限不足**
   - 某些系统文件夹可能需要root权限
   - 尝试清理用户可访问的文件夹

3. **清理失败**
   - 检查文件夹是否存在
   - 确认文件没有被其他应用占用
   - 查看详细日志了解具体错误

### 调试方法

```python
# 启用详细日志
import logging
logging.getLogger().setLevel(logging.DEBUG)

# 检查设备连接
from tools.gallery_cleaner import gallery_cleaner
gallery_cleaner.check_device_connection()

# 获取状态信息
status = gallery_cleaner.get_gallery_status()
print(status)
```
