"""
简化版相册清理工具
专门针对Windows环境和兼容性问题优化
"""
import subprocess
import time
from typing import List, Dict, Optional
from core.logger import log


class SimpleGalleryCleaner:
    """简化版相册清理工具类"""

    def __init__(self):
        """初始化相册清理工具"""
        # 常见的相册目录路径
        self.gallery_paths = {
            'camera': '/sdcard/DCIM/Camera',
            'pictures': '/sdcard/Pictures',
            'download': '/sdcard/Download',
            'screenshots': '/sdcard/Pictures/Screenshots',
            'dcim': '/sdcard/DCIM',
            'movies': '/sdcard/Movies',
            'music': '/sdcard/Music',
            'documents': '/sdcard/Documents'
        }

    def _run_adb_command(self, cmd: str) -> subprocess.CompletedProcess:
        """执行ADB命令"""
        try:
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=60
            )
            return result
        except subprocess.TimeoutExpired:
            log.error(f"ADB命令执行超时: {cmd}")
            return subprocess.CompletedProcess(cmd, 1, "", "命令执行超时")
        except Exception as e:
            log.error(f"执行ADB命令时出错: {e}")
            return subprocess.CompletedProcess(cmd, 1, "", str(e))

    def check_device_connection(self) -> bool:
        """检查设备连接状态"""
        try:
            result = self._run_adb_command("adb devices")
            if result.returncode == 0 and "device" in result.stdout:
                log.info("✅ 设备连接正常")
                return True
            else:
                log.error("❌ 设备未连接或ADB不可用")
                return False
        except Exception as e:
            log.error(f"检查设备连接失败: {e}")
            return False

    def clear_directory_simple(self, path: str) -> bool:
        """简单清理目录"""
        try:
            log.info(f"开始清理目录: {path}")

            # 检查设备连接
            if not self.check_device_connection():
                return False

            # 检查目录是否存在
            check_cmd = f"adb shell test -d {path} && echo 'exists' || echo 'not_exists'"
            result = self._run_adb_command(check_cmd)
            
            if result.returncode != 0 or 'not_exists' in result.stdout:
                log.warning(f"目录不存在，无需清理: {path}")
                return True

            # 获取清理前的文件数量
            count_before = self._count_files_simple(path)
            log.info(f"清理前文件数量: {count_before}")

            # 执行清理 - 使用多种方法
            success = self._clear_directory_multiple_methods(path)

            if success:
                # 获取清理后的文件数量
                count_after = self._count_files_simple(path)
                cleared_count = count_before - count_after
                log.info(f"✅ 清理完成: 删除了 {cleared_count} 个文件")
                
                # 刷新媒体库
                self._refresh_media_library()
                return True
            else:
                log.error("❌ 清理操作失败")
                return False

        except Exception as e:
            log.error(f"清理目录失败: {e}")
            return False

    def _count_files_simple(self, path: str) -> int:
        """简单计算文件数量"""
        try:
            # 使用ls命令计算文件数量
            cmd = f"adb shell ls {path} 2>/dev/null | wc -l || echo 0"
            result = self._run_adb_command(cmd)
            
            if result.returncode == 0:
                try:
                    count = int(result.stdout.strip())
                    return max(0, count)
                except ValueError:
                    pass
            
            # 备用方法：使用ls然后在Python中计算
            cmd = f"adb shell ls {path} 2>/dev/null || true"
            result = self._run_adb_command(cmd)
            
            if result.returncode == 0:
                files = [line.strip() for line in result.stdout.split('\n') if line.strip()]
                return len(files)
            
            return 0
        except Exception as e:
            log.debug(f"计算文件数量失败: {e}")
            return 0

    def _clear_directory_multiple_methods(self, path: str) -> bool:
        """使用多种方法清理目录"""
        try:
            success = False
            
            # 方法1: 使用rm删除所有文件
            methods = [
                f"adb shell rm {path}/* 2>/dev/null || true",
                f"adb shell rm -f {path}/* 2>/dev/null || true",
                f"adb shell rm -rf {path}/* 2>/dev/null || true"
            ]
            
            for cmd in methods:
                log.debug(f"尝试清理方法: {cmd}")
                result = self._run_adb_command(cmd)
                if result.returncode == 0:
                    success = True
                    log.debug("清理方法成功")
                    break
            
            # 方法2: 如果上面失败，尝试删除特定文件类型
            if not success:
                extensions = ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'avi', 'txt', 'pdf']
                for ext in extensions:
                    cmd = f"adb shell rm {path}/*.{ext} 2>/dev/null || true"
                    result = self._run_adb_command(cmd)
                    if result.returncode == 0:
                        success = True
                    
                    cmd = f"adb shell rm {path}/*.{ext.upper()} 2>/dev/null || true"
                    result = self._run_adb_command(cmd)
                    if result.returncode == 0:
                        success = True
            
            # 方法3: 尝试递归删除（如果支持）
            if not success:
                recursive_methods = [
                    f"adb shell find {path} -type f -delete 2>/dev/null || true",
                    f"adb shell find {path} -type f -exec rm {{}} \\; 2>/dev/null || true"
                ]
                
                for cmd in recursive_methods:
                    result = self._run_adb_command(cmd)
                    if result.returncode == 0:
                        success = True
                        break
            
            return success

        except Exception as e:
            log.error(f"多方法清理失败: {e}")
            return False

    def _refresh_media_library(self) -> bool:
        """刷新媒体库"""
        try:
            log.info("刷新媒体库...")

            # 重启媒体存储服务
            restart_cmd = "adb shell am force-stop com.android.providers.media"
            self._run_adb_command(restart_cmd)

            # 刷新媒体库
            refresh_cmd = "adb shell am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard"
            self._run_adb_command(refresh_cmd)

            # 等待媒体库更新
            time.sleep(3)

            log.info("✅ 媒体库刷新完成")
            return True

        except Exception as e:
            log.error(f"刷新媒体库失败: {e}")
            return False

    def clear_gallery_folder(self, folder_type: str) -> bool:
        """清空指定的相册文件夹"""
        try:
            if folder_type not in self.gallery_paths:
                log.error(f"不支持的文件夹类型: {folder_type}")
                log.info(f"支持的类型: {list(self.gallery_paths.keys())}")
                return False

            target_path = self.gallery_paths[folder_type]
            return self.clear_directory_simple(target_path)

        except Exception as e:
            log.error(f"清理相册文件夹失败: {e}")
            return False

    def clear_all_gallery_folders(self) -> Dict[str, bool]:
        """清空所有相册文件夹"""
        try:
            log.info("开始清理所有相册文件夹")
            results = {}

            for folder_type in self.gallery_paths.keys():
                log.info(f"清理文件夹: {folder_type}")
                results[folder_type] = self.clear_gallery_folder(folder_type)

            # 统计结果
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            log.info(f"清理完成: {success_count}/{total_count} 个文件夹成功")

            return results

        except Exception as e:
            log.error(f"清理所有相册文件夹失败: {e}")
            return {}

    def clear_custom_path(self, custom_path: str) -> bool:
        """清空自定义路径"""
        try:
            log.info(f"开始清理自定义路径: {custom_path}")
            return self.clear_directory_simple(custom_path)

        except Exception as e:
            log.error(f"清理自定义路径失败: {e}")
            return False

    def get_gallery_status(self) -> Dict[str, Dict]:
        """获取所有相册文件夹的状态"""
        try:
            log.info("获取相册文件夹状态...")
            status = {}

            for folder_type, path in self.gallery_paths.items():
                folder_status = {
                    'path': path,
                    'exists': self._check_directory_exists(path),
                    'file_count': 0
                }

                if folder_status['exists']:
                    folder_status['file_count'] = self._count_files_simple(path)

                status[folder_type] = folder_status

            return status

        except Exception as e:
            log.error(f"获取相册状态失败: {e}")
            return {}

    def _check_directory_exists(self, path: str) -> bool:
        """检查目录是否存在"""
        try:
            cmd = f"adb shell test -d {path} && echo 'exists' || echo 'not_exists'"
            result = self._run_adb_command(cmd)
            return result.returncode == 0 and 'exists' in result.stdout
        except Exception as e:
            log.error(f"检查目录存在性失败: {e}")
            return False


# 全局实例
simple_gallery_cleaner = SimpleGalleryCleaner()


def clear_camera_folder() -> bool:
    """清空相机文件夹的便捷函数"""
    return simple_gallery_cleaner.clear_gallery_folder('camera')


def clear_pictures_folder() -> bool:
    """清空图片文件夹的便捷函数"""
    return simple_gallery_cleaner.clear_gallery_folder('pictures')


def clear_download_folder() -> bool:
    """清空下载文件夹的便捷函数"""
    return simple_gallery_cleaner.clear_gallery_folder('download')


def clear_all_galleries() -> Dict[str, bool]:
    """清空所有相册文件夹的便捷函数"""
    return simple_gallery_cleaner.clear_all_gallery_folders()


if __name__ == "__main__":
    # 测试简化版相册清理功能
    cleaner = SimpleGalleryCleaner()
    
    # 检查设备连接
    if cleaner.check_device_connection():
        # 获取当前状态
        status = cleaner.get_gallery_status()
        print("当前相册状态:")
        for folder, info in status.items():
            print(f"  {folder}: {info['file_count']} 个文件")
        
        # 测试清理下载文件夹
        print("\n测试清理下载文件夹...")
        success = cleaner.clear_gallery_folder('download')
        print(f"清理结果: {'成功' if success else '失败'}")
    else:
        print("请确保设备已连接并启用USB调试")
