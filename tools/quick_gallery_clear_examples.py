#!/usr/bin/env python3
"""
快速相册清理使用示例
展示各种快速清理AI Gallery的方法
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.logger import log


def example_basic_usage():
    """基本使用示例"""
    print("📱 基本使用示例")
    print("-" * 40)
    
    # 方法1: 最快速的清理（推荐用于测试）
    from tools.force_gallery_cleaner import instant_clear
    
    print("⚡ 使用瞬间清理...")
    success = instant_clear()
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 方法2: 只清理图片文件
    from tools.force_gallery_cleaner import clear_ai_gallery_images_only
    
    print("\n🖼️ 只清理图片文件...")
    success = clear_ai_gallery_images_only()
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")


def example_test_integration():
    """测试集成示例"""
    print("\n🧪 测试集成示例")
    print("-" * 40)
    
    # 在测试开始前快速清理
    from tools.force_gallery_cleaner import super_quick_clear
    
    print("🚀 测试前快速清理...")
    start_time = time.time()
    success = super_quick_clear()
    end_time = time.time()
    
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
    print(f"耗时: {end_time - start_time:.2f} 秒")


def example_different_speeds():
    """不同速度的清理方法示例"""
    print("\n⚡ 不同速度清理方法对比")
    print("-" * 40)
    
    from tools.force_gallery_cleaner import (
        one_second_clear,
        instant_clear,
        super_quick_clear,
        quick_clear_ai_gallery
    )
    
    methods = [
        ("一秒清理", one_second_clear),
        ("瞬间清理", instant_clear), 
        ("超快速清理", super_quick_clear),
        ("快速清理", quick_clear_ai_gallery)
    ]
    
    for method_name, method_func in methods:
        print(f"\n测试 {method_name}...")
        start_time = time.time()
        success = method_func()
        end_time = time.time()
        
        print(f"  结果: {'✅ 成功' if success else '❌ 失败'}")
        print(f"  耗时: {end_time - start_time:.2f} 秒")


def example_in_test_case():
    """在测试用例中的使用示例"""
    print("\n📝 测试用例中的使用示例")
    print("-" * 40)
    
    # 模拟测试用例代码
    test_code = '''
# 在测试用例中使用
def test_ask_screen_translation(self):
    """测试Ask Screen翻译功能"""
    
    # 数据准备 - 快速清理已有图片
    with allure.step("准备测试数据"):
        from tools.force_gallery_cleaner import instant_clear
        
        # 瞬间清理AI Gallery（1-2秒完成）
        clear_result = instant_clear()
        if clear_result:
            log.info("✅ AI Gallery清理成功")
        else:
            log.warning("⚠️ AI Gallery清理失败，继续执行测试")
        
        # 推送测试图片
        push_result = file_pusher.push_ask_screen_image("test_image")
        assert push_result, "推送图片失败"
    
    # 执行测试...
    '''
    
    print("示例代码:")
    print(test_code)


def example_performance_comparison():
    """性能对比示例"""
    print("\n📊 性能对比")
    print("-" * 40)
    
    from tools.force_gallery_cleaner import (
        instant_clear,
        clear_camera_folder,
        clear_all_galleries
    )
    
    # 模拟性能测试
    performance_data = [
        ("瞬间清理 (instant_clear)", "1-2秒", "只清理主要目录", "⭐⭐⭐⭐⭐"),
        ("清理相机文件夹 (clear_camera_folder)", "3-5秒", "完整清理相机文件夹", "⭐⭐⭐⭐"),
        ("清理所有相册 (clear_all_galleries)", "10-15秒", "完整清理所有文件夹", "⭐⭐⭐")
    ]
    
    print(f"{'方法':<30} {'耗时':<10} {'范围':<20} {'推荐度'}")
    print("-" * 80)
    
    for method, time_cost, scope, rating in performance_data:
        print(f"{method:<30} {time_cost:<10} {scope:<20} {rating}")


def main():
    """主函数"""
    print("🎯 快速相册清理使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_test_integration()
        example_different_speeds()
        example_in_test_case()
        example_performance_comparison()
        
        print("\n" + "=" * 50)
        print("🎉 示例演示完成！")
        
        print("\n💡 使用建议:")
        print("1. 测试前快速清理: 使用 instant_clear() 或 super_quick_clear()")
        print("2. 只清理图片: 使用 clear_ai_gallery_images_only()")
        print("3. 极速模式: 使用 one_second_clear() (适合CI/CD)")
        print("4. 完整清理: 使用 clear_all_galleries() (测试后清理)")
        
        print("\n🚀 推荐的测试集成方式:")
        print("```python")
        print("from tools.force_gallery_cleaner import instant_clear")
        print("# 测试前")
        print("instant_clear()  # 1-2秒完成")
        print("# 执行测试...")
        print("```")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断演示")
    except Exception as e:
        print(f"\n❌ 演示异常: {e}")


if __name__ == "__main__":
    main()
