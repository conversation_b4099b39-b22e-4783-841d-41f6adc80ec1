"""
相册清理工具
用于清空Android设备的相册文件夹
"""
import os
import subprocess
import time
from pathlib import Path
from typing import List, Optional, Dict
from core.logger import log


class GalleryCleaner:
    """相册清理工具类"""

    def __init__(self):
        """初始化相册清理工具"""
        self.project_root = Path(__file__).parent.parent
        
        # 常见的相册目录路径
        self.gallery_paths = {
            'camera': '/sdcard/DCIM/Camera',
            'pictures': '/sdcard/Pictures',
            'download': '/sdcard/Download',
            'screenshots': '/sdcard/Pictures/Screenshots',
            'dcim': '/sdcard/DCIM',
            'movies': '/sdcard/Movies',
            'music': '/sdcard/Music',
            'documents': '/sdcard/Documents'
        }
        
        # 支持的文件类型
        self.media_extensions = {
            'images': ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg'],
            'videos': ['mp4', 'avi', 'mov', 'mkv', '3gp', 'wmv', 'flv', 'webm'],
            'audio': ['mp3', 'wav', 'aac', 'flac', 'ogg', 'm4a', 'wma'],
            'documents': ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt', 'xls', 'xlsx', 'ppt', 'pptx']
        }

    def _run_adb_command(self, cmd: str) -> subprocess.CompletedProcess:
        """
        执行ADB命令，统一处理编码问题

        Args:
            cmd: ADB命令字符串

        Returns:
            subprocess.CompletedProcess: 命令执行结果
        """
        try:
            # 使用UTF-8编码并忽略错误字符，避免GBK编码问题
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=60  # 清理操作可能需要更长时间
            )
            return result
        except subprocess.TimeoutExpired:
            log.error(f"ADB命令执行超时: {cmd}")
            return subprocess.CompletedProcess(cmd, 1, "", "命令执行超时")
        except Exception as e:
            log.error(f"执行ADB命令时出错: {e}")
            return subprocess.CompletedProcess(cmd, 1, "", str(e))

    def check_device_connection(self) -> bool:
        """检查设备连接状态"""
        try:
            result = self._run_adb_command("adb devices")
            if result.returncode == 0 and "device" in result.stdout:
                log.info("✅ 设备连接正常")
                return True
            else:
                log.error("❌ 设备未连接或ADB不可用")
                return False
        except Exception as e:
            log.error(f"检查设备连接失败: {e}")
            return False

    def clear_gallery_folder(self, folder_type: str = 'camera', file_types: List[str] = None) -> bool:
        """
        清空指定的相册文件夹

        Args:
            folder_type: 文件夹类型，支持: 'camera', 'pictures', 'download', 'screenshots', 'dcim', 'movies', 'music', 'documents'
            file_types: 要清理的文件类型列表，如 ['images', 'videos']，为None时清理所有媒体文件

        Returns:
            bool: 清理是否成功
        """
        try:
            if folder_type not in self.gallery_paths:
                log.error(f"不支持的文件夹类型: {folder_type}")
                log.info(f"支持的类型: {list(self.gallery_paths.keys())}")
                return False

            target_path = self.gallery_paths[folder_type]
            log.info(f"开始清理相册文件夹: {target_path}")

            # 检查设备连接
            if not self.check_device_connection():
                return False

            # 检查目录是否存在
            if not self._check_directory_exists(target_path):
                log.warning(f"目录不存在，无需清理: {target_path}")
                return True

            # 获取清理前的文件列表
            files_before = self._list_directory_files(target_path)
            log.info(f"清理前文件数量: {len(files_before)}")

            # 执行清理操作
            success = self._clear_directory_by_type(target_path, file_types)

            if success:
                # 获取清理后的文件列表
                files_after = self._list_directory_files(target_path)
                cleared_count = len(files_before) - len(files_after)
                log.info(f"✅ 清理完成: 删除了 {cleared_count} 个文件")

                # 刷新媒体库
                self._refresh_media_library()
                return True
            else:
                log.error("❌ 清理操作失败")
                return False

        except Exception as e:
            log.error(f"清理相册文件夹失败: {e}")
            return False

    def clear_all_gallery_folders(self, file_types: List[str] = None) -> Dict[str, bool]:
        """
        清空所有相册文件夹

        Args:
            file_types: 要清理的文件类型列表，如 ['images', 'videos']，为None时清理所有媒体文件

        Returns:
            Dict[str, bool]: 每个文件夹的清理结果
        """
        try:
            log.info("开始清理所有相册文件夹")
            results = {}

            for folder_type in self.gallery_paths.keys():
                log.info(f"清理文件夹: {folder_type}")
                results[folder_type] = self.clear_gallery_folder(folder_type, file_types)

            # 统计结果
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            log.info(f"清理完成: {success_count}/{total_count} 个文件夹成功")

            return results

        except Exception as e:
            log.error(f"清理所有相册文件夹失败: {e}")
            return {}

    def clear_custom_path(self, custom_path: str, file_types: List[str] = None) -> bool:
        """
        清空自定义路径

        Args:
            custom_path: 自定义路径
            file_types: 要清理的文件类型列表

        Returns:
            bool: 清理是否成功
        """
        try:
            log.info(f"开始清理自定义路径: {custom_path}")

            # 检查设备连接
            if not self.check_device_connection():
                return False

            # 检查目录是否存在
            if not self._check_directory_exists(custom_path):
                log.warning(f"目录不存在，无需清理: {custom_path}")
                return True

            # 获取清理前的文件列表
            files_before = self._list_directory_files(custom_path)
            log.info(f"清理前文件数量: {len(files_before)}")

            # 执行清理操作
            success = self._clear_directory_by_type(custom_path, file_types)

            if success:
                # 获取清理后的文件列表
                files_after = self._list_directory_files(custom_path)
                cleared_count = len(files_before) - len(files_after)
                log.info(f"✅ 清理完成: 删除了 {cleared_count} 个文件")

                # 刷新媒体库
                self._refresh_media_library()
                return True
            else:
                log.error("❌ 清理操作失败")
                return False

        except Exception as e:
            log.error(f"清理自定义路径失败: {e}")
            return False

    def _check_directory_exists(self, path: str) -> bool:
        """检查目录是否存在"""
        try:
            cmd = f"adb shell test -d {path} && echo 'exists'"
            result = self._run_adb_command(cmd)
            return result.returncode == 0 and 'exists' in result.stdout
        except Exception as e:
            log.error(f"检查目录存在性失败: {e}")
            return False

    def _list_directory_files(self, path: str) -> List[str]:
        """列出目录中的文件"""
        try:
            cmd = f"adb shell find {path} -type f 2>/dev/null"
            result = self._run_adb_command(cmd)
            
            if result.returncode == 0:
                files = [f.strip() for f in result.stdout.split('\n') if f.strip()]
                return files
            else:
                return []
        except Exception as e:
            log.error(f"列出目录文件失败: {e}")
            return []

    def _clear_directory_by_type(self, path: str, file_types: List[str] = None) -> bool:
        """根据文件类型清理目录"""
        try:
            if file_types is None:
                # 清理所有媒体文件
                file_types = ['images', 'videos', 'audio', 'documents']

            success = True
            for file_type in file_types:
                if file_type not in self.media_extensions:
                    log.warning(f"不支持的文件类型: {file_type}")
                    continue

                extensions = self.media_extensions[file_type]
                type_success = self._clear_files_by_extensions(path, extensions)
                success = success and type_success

            return success

        except Exception as e:
            log.error(f"按类型清理目录失败: {e}")
            return False

    def _clear_files_by_extensions(self, path: str, extensions: List[str]) -> bool:
        """根据扩展名清理文件"""
        try:
            success = True
            
            for ext in extensions:
                # 清理小写扩展名
                cmd_lower = f"adb shell find {path} -name '*.{ext}' -type f -delete 2>/dev/null"
                result_lower = self._run_adb_command(cmd_lower)
                
                # 清理大写扩展名
                cmd_upper = f"adb shell find {path} -name '*.{ext.upper()}' -type f -delete 2>/dev/null"
                result_upper = self._run_adb_command(cmd_upper)
                
                if result_lower.returncode != 0 and result_upper.returncode != 0:
                    log.warning(f"清理 .{ext} 文件可能失败")
                    success = False

            return success

        except Exception as e:
            log.error(f"按扩展名清理文件失败: {e}")
            return False

    def _refresh_media_library(self) -> bool:
        """刷新媒体库"""
        try:
            log.info("刷新媒体库...")

            # 方法1: 重启媒体存储服务
            restart_cmd = "adb shell am force-stop com.android.providers.media"
            result1 = self._run_adb_command(restart_cmd)

            # 方法2: 刷新整个媒体库
            refresh_cmd = "adb shell am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard"
            result2 = self._run_adb_command(refresh_cmd)

            # 等待媒体库更新
            time.sleep(5)

            log.info("✅ 媒体库刷新完成")
            return True

        except Exception as e:
            log.error(f"刷新媒体库失败: {e}")
            return False

    def get_gallery_status(self) -> Dict[str, Dict]:
        """获取所有相册文件夹的状态"""
        try:
            log.info("获取相册文件夹状态...")
            status = {}

            for folder_type, path in self.gallery_paths.items():
                folder_status = {
                    'path': path,
                    'exists': self._check_directory_exists(path),
                    'file_count': 0,
                    'files': []
                }

                if folder_status['exists']:
                    files = self._list_directory_files(path)
                    folder_status['file_count'] = len(files)
                    folder_status['files'] = files[:10]  # 只显示前10个文件

                status[folder_type] = folder_status

            return status

        except Exception as e:
            log.error(f"获取相册状态失败: {e}")
            return {}


# 全局实例
gallery_cleaner = GalleryCleaner()


def clear_camera_folder() -> bool:
    """清空相机文件夹的便捷函数"""
    return gallery_cleaner.clear_gallery_folder('camera')


def clear_pictures_folder() -> bool:
    """清空图片文件夹的便捷函数"""
    return gallery_cleaner.clear_gallery_folder('pictures')


def clear_download_folder() -> bool:
    """清空下载文件夹的便捷函数"""
    return gallery_cleaner.clear_gallery_folder('download')


def clear_all_galleries() -> Dict[str, bool]:
    """清空所有相册文件夹的便捷函数"""
    return gallery_cleaner.clear_all_gallery_folders()


if __name__ == "__main__":
    # 测试相册清理功能
    cleaner = GalleryCleaner()
    
    # 检查设备连接
    if cleaner.check_device_connection():
        # 获取当前状态
        status = cleaner.get_gallery_status()
        print("当前相册状态:")
        for folder, info in status.items():
            print(f"  {folder}: {info['file_count']} 个文件")
        
        # 清理相机文件夹
        # cleaner.clear_gallery_folder('camera')
        
        # 清理所有文件夹
        # cleaner.clear_all_gallery_folders()
    else:
        print("请确保设备已连接并启用USB调试")
