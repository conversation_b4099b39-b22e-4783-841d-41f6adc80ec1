#!/usr/bin/env python3
"""
测试发送按钮的多种定位方法
用于验证改进后的发送功能是否正常工作
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from core.logger import log
from core.base_driver import driver_manager
from pages.apps.ella.dialogue_page import EllaDialoguePage


def test_send_button_detection():
    """测试发送按钮检测"""
    log.info("🔍 测试发送按钮检测...")
    
    try:
        # 初始化Ella对话页面
        ella_page = EllaDialoguePage()
        
        # 启动应用
        if not ella_page.start_app():
            log.error("❌ 启动Ella应用失败")
            return False
        
        time.sleep(3)  # 等待应用完全加载
        
        # 测试各种发送按钮定位器
        send_buttons = {
            'send_button': ella_page.send_button,
            'send_button_alt1': ella_page.send_button_alt1,
            'send_button_alt2': ella_page.send_button_alt2,
            'send_button_alt3': ella_page.send_button_alt3,
            'send_button_alt4': ella_page.send_button_alt4
        }
        
        found_buttons = []
        
        for button_name, button_element in send_buttons.items():
            try:
                if button_element.is_exists():
                    log.info(f"✅ 找到发送按钮: {button_name}")
                    found_buttons.append(button_name)
                    
                    # 获取按钮信息
                    button_info = button_element.get_attribute('text') or button_element.get_attribute('contentDescription')
                    log.info(f"   按钮信息: {button_info}")
                else:
                    log.info(f"❌ 未找到发送按钮: {button_name}")
            except Exception as e:
                log.error(f"❌ 检测按钮'{button_name}'时出错: {e}")
        
        if found_buttons:
            log.info(f"🎉 总共找到 {len(found_buttons)} 个可用的发送按钮")
            return True
        else:
            log.warning("⚠️ 未找到任何可用的发送按钮")
            return False
            
    except Exception as e:
        log.error(f"❌ 测试发送按钮检测失败: {e}")
        return False


def test_alternative_send_methods():
    """测试替代发送方法"""
    log.info("🧪 测试替代发送方法...")
    
    try:
        driver = driver_manager.driver
        
        # 测试文本搜索
        log.info("测试文本搜索方法...")
        send_texts = ["发送", "Send", "➤", "→", "▶"]
        
        for text in send_texts:
            try:
                element = driver(text=text)
                if element.exists():
                    log.info(f"✅ 通过文本'{text}'找到发送元素")
                else:
                    log.info(f"❌ 未通过文本'{text}'找到发送元素")
            except Exception as e:
                log.debug(f"文本'{text}'搜索失败: {e}")
        
        # 测试类名搜索
        log.info("测试类名搜索方法...")
        button_classes = [
            "android.widget.Button",
            "android.widget.ImageButton", 
            "android.widget.ImageView"
        ]
        
        for class_name in button_classes:
            try:
                elements = driver(className=class_name)
                if elements.exists():
                    count = len(elements)
                    log.info(f"✅ 通过类名'{class_name}'找到 {count} 个元素")
                else:
                    log.info(f"❌ 未通过类名'{class_name}'找到元素")
            except Exception as e:
                log.debug(f"类名'{class_name}'搜索失败: {e}")
        
        return True
        
    except Exception as e:
        log.error(f"❌ 测试替代发送方法失败: {e}")
        return False


def test_send_command_execution():
    """测试发送命令执行"""
    log.info("🚀 测试发送命令执行...")
    
    try:
        # 初始化Ella对话页面
        ella_page = EllaDialoguePage()
        
        # 启动应用
        if not ella_page.start_app():
            log.error("❌ 启动Ella应用失败")
            return False
        
        time.sleep(3)
        
        # 执行一个简单的测试命令
        test_command = "hello"
        
        log.info(f"执行测试命令: {test_command}")
        success = ella_page.command_executor.execute_text_command(test_command)
        
        if success:
            log.info("✅ 发送命令执行成功")
            return True
        else:
            log.error("❌ 发送命令执行失败")
            return False
            
    except Exception as e:
        log.error(f"❌ 测试发送命令执行失败: {e}")
        return False


def main():
    """主函数"""
    log.info("🎯 开始测试发送按钮的多种定位方法")
    
    try:
        # 测试1: 发送按钮检测
        log.info("\n" + "="*50)
        log.info("测试1: 发送按钮检测")
        log.info("="*50)
        test_send_button_detection()
        
        # 测试2: 替代发送方法
        log.info("\n" + "="*50)
        log.info("测试2: 替代发送方法")
        log.info("="*50)
        test_alternative_send_methods()
        
        # 测试3: 发送命令执行
        log.info("\n" + "="*50)
        log.info("测试3: 发送命令执行")
        log.info("="*50)
        test_send_command_execution()
        
        log.info("\n🎉 所有测试完成！")
        log.info("💡 建议:")
        log.info("1. 查看日志了解哪些定位方法有效")
        log.info("2. 根据测试结果调整发送按钮定位策略")
        log.info("3. 如果所有方法都失败，可能需要检查应用界面变化")
        
    except Exception as e:
        log.error(f"❌ 测试过程中出现错误: {e}")
    
    finally:
        # 清理资源
        try:
            driver_manager.quit()
        except:
            pass


if __name__ == "__main__":
    main()
