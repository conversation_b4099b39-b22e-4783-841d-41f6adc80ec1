#!/usr/bin/env python3
"""
相册清理工具诊断脚本
用于分析相册清理失败的原因
"""

import sys
import os
import subprocess
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from core.logger import log


def run_adb_command(cmd: str, timeout: int = 30):
    """执行ADB命令并返回结果"""
    try:
        print(f"执行命令: {cmd}")
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='ignore',
            timeout=timeout
        )
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        return result
    except subprocess.TimeoutExpired:
        print(f"命令超时: {cmd}")
        return None
    except Exception as e:
        print(f"命令执行异常: {e}")
        return None


def check_device_connection():
    """检查设备连接"""
    print("🔍 检查设备连接...")
    
    result = run_adb_command("adb devices")
    if result and result.returncode == 0:
        lines = result.stdout.strip().split('\n')
        devices = [line for line in lines[1:] if line.strip() and 'device' in line]
        
        if devices:
            print(f"✅ 找到 {len(devices)} 个设备:")
            for device in devices:
                print(f"  {device}")
            return True
        else:
            print("❌ 未找到连接的设备")
            return False
    else:
        print("❌ ADB命令执行失败")
        return False


def check_directory_permissions():
    """检查目录权限"""
    print("\n🔍 检查目录权限...")
    
    test_dirs = [
        '/sdcard/DCIM/Camera',
        '/sdcard/Pictures',
        '/sdcard/Download',
        '/sdcard/DCIM',
        '/sdcard'
    ]
    
    for test_dir in test_dirs:
        print(f"\n检查目录: {test_dir}")
        
        # 检查目录是否存在
        result = run_adb_command(f"adb shell test -d {test_dir} && echo 'exists' || echo 'not_exists'")
        if result and 'exists' in result.stdout:
            print(f"  ✅ 目录存在")
            
            # 检查读权限
            result = run_adb_command(f"adb shell test -r {test_dir} && echo 'readable' || echo 'not_readable'")
            if result and 'readable' in result.stdout:
                print(f"  ✅ 可读")
            else:
                print(f"  ❌ 不可读")
            
            # 检查写权限
            result = run_adb_command(f"adb shell test -w {test_dir} && echo 'writable' || echo 'not_writable'")
            if result and 'writable' in result.stdout:
                print(f"  ✅ 可写")
            else:
                print(f"  ❌ 不可写")
            
            # 列出文件
            result = run_adb_command(f"adb shell ls -la {test_dir} | head -5")
            if result and result.returncode == 0:
                print(f"  📁 目录内容预览:")
                for line in result.stdout.strip().split('\n')[:3]:
                    if line.strip():
                        print(f"    {line}")
        else:
            print(f"  ❌ 目录不存在")


def test_file_operations():
    """测试文件操作"""
    print("\n🧪 测试文件操作...")
    
    test_dir = "/sdcard/Download"
    test_file = f"{test_dir}/test_cleanup.txt"
    
    print(f"测试目录: {test_dir}")
    
    # 创建测试文件
    print("1. 创建测试文件...")
    result = run_adb_command(f"adb shell echo 'test content' > {test_file}")
    if result and result.returncode == 0:
        print("  ✅ 测试文件创建成功")
    else:
        print("  ❌ 测试文件创建失败")
        return False
    
    # 验证文件存在
    print("2. 验证文件存在...")
    result = run_adb_command(f"adb shell test -f {test_file} && echo 'exists' || echo 'not_exists'")
    if result and 'exists' in result.stdout:
        print("  ✅ 测试文件存在")
    else:
        print("  ❌ 测试文件不存在")
        return False
    
    # 删除测试文件
    print("3. 删除测试文件...")
    result = run_adb_command(f"adb shell rm -f {test_file}")
    if result and result.returncode == 0:
        print("  ✅ 测试文件删除成功")
    else:
        print("  ❌ 测试文件删除失败")
        return False
    
    # 验证文件已删除
    print("4. 验证文件已删除...")
    result = run_adb_command(f"adb shell test -f {test_file} && echo 'exists' || echo 'not_exists'")
    if result and 'not_exists' in result.stdout:
        print("  ✅ 测试文件已删除")
        return True
    else:
        print("  ❌ 测试文件仍然存在")
        return False


def test_find_command():
    """测试find命令"""
    print("\n🔍 测试find命令...")
    
    test_dir = "/sdcard/Download"
    
    # 测试基本find命令
    print("1. 测试基本find命令...")
    result = run_adb_command(f"adb shell find {test_dir} -type f 2>/dev/null | head -5")
    if result and result.returncode == 0:
        print("  ✅ find命令可用")
        files = [f.strip() for f in result.stdout.split('\n') if f.strip()]
        print(f"  找到 {len(files)} 个文件")
    else:
        print("  ❌ find命令不可用")
        return False
    
    # 测试按扩展名查找
    print("2. 测试按扩展名查找...")
    result = run_adb_command(f"adb shell find {test_dir} -name '*.jpg' -type f 2>/dev/null | head -3")
    if result and result.returncode == 0:
        print("  ✅ 按扩展名查找可用")
        jpg_files = [f.strip() for f in result.stdout.split('\n') if f.strip()]
        print(f"  找到 {len(jpg_files)} 个jpg文件")
    else:
        print("  ❌ 按扩展名查找失败")
    
    # 测试删除命令
    print("3. 测试find删除命令...")
    # 先创建一个测试文件
    test_file = f"{test_dir}/test_find_delete.txt"
    run_adb_command(f"adb shell echo 'test' > {test_file}")
    
    # 使用find删除
    result = run_adb_command(f"adb shell find {test_dir} -name 'test_find_delete.txt' -type f -delete 2>/dev/null")
    if result and result.returncode == 0:
        print("  ✅ find删除命令可用")
        return True
    else:
        print("  ❌ find删除命令失败")
        # 清理测试文件
        run_adb_command(f"adb shell rm -f {test_file}")
        return False


def test_gallery_cleaner():
    """测试相册清理工具"""
    print("\n🧹 测试相册清理工具...")
    
    try:
        from tools.gallery_cleaner import gallery_cleaner
        
        # 检查设备连接
        print("1. 检查设备连接...")
        if gallery_cleaner.check_device_connection():
            print("  ✅ 设备连接正常")
        else:
            print("  ❌ 设备连接失败")
            return False
        
        # 获取相册状态
        print("2. 获取相册状态...")
        status = gallery_cleaner.get_gallery_status()
        if status:
            print("  ✅ 获取状态成功")
            for folder_type, info in status.items():
                exists = "存在" if info.get('exists', False) else "不存在"
                file_count = info.get('file_count', 0)
                print(f"    {folder_type}: {exists}, {file_count} 个文件")
        else:
            print("  ❌ 获取状态失败")
            return False
        
        # 测试清理下载文件夹
        print("3. 测试清理下载文件夹...")
        success = gallery_cleaner.clear_gallery_folder('download', ['documents'])
        if success:
            print("  ✅ 清理成功")
        else:
            print("  ❌ 清理失败")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 测试异常: {e}")
        return False


def check_android_version():
    """检查Android版本"""
    print("\n📱 检查Android版本...")
    
    result = run_adb_command("adb shell getprop ro.build.version.release")
    if result and result.returncode == 0:
        version = result.stdout.strip()
        print(f"  Android版本: {version}")
        
        # 检查API级别
        result = run_adb_command("adb shell getprop ro.build.version.sdk")
        if result and result.returncode == 0:
            api_level = result.stdout.strip()
            print(f"  API级别: {api_level}")
            
            # Android 6.0+ (API 23+) 有运行时权限
            if int(api_level) >= 23:
                print("  ⚠️ Android 6.0+，可能需要运行时权限")
        
        return True
    else:
        print("  ❌ 无法获取Android版本")
        return False


def check_storage_permissions():
    """检查存储权限"""
    print("\n🔐 检查存储权限...")
    
    # 检查外部存储权限
    result = run_adb_command("adb shell ls -la /sdcard/ | head -5")
    if result and result.returncode == 0:
        print("  ✅ 可以访问外部存储")
    else:
        print("  ❌ 无法访问外部存储")
        return False
    
    # 检查是否有scoped storage限制
    result = run_adb_command("adb shell getprop ro.build.version.sdk")
    if result and result.returncode == 0:
        api_level = int(result.stdout.strip())
        if api_level >= 30:  # Android 11+
            print("  ⚠️ Android 11+，可能有Scoped Storage限制")
            
            # 检查是否可以访问其他应用的文件
            result = run_adb_command("adb shell ls /sdcard/Android/data/ 2>/dev/null | head -3")
            if result and result.stdout.strip():
                print("  ✅ 可以访问其他应用数据")
            else:
                print("  ⚠️ 可能无法访问其他应用数据")
    
    return True


def main():
    """主函数"""
    print("🔧 相册清理工具诊断")
    print("=" * 50)
    
    # 检查项目列表
    checks = [
        ("设备连接", check_device_connection),
        ("Android版本", check_android_version),
        ("存储权限", check_storage_permissions),
        ("目录权限", check_directory_permissions),
        ("文件操作", test_file_operations),
        ("find命令", test_find_command),
        ("相册清理工具", test_gallery_cleaner)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {e}")
            results[check_name] = False
    
    # 总结
    print(f"\n{'='*20} 诊断总结 {'='*20}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    print(f"检查项目: {passed}/{total} 通过")
    print("\n详细结果:")
    
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
    
    # 建议
    print(f"\n💡 建议:")
    
    if not results.get("设备连接", False):
        print("1. 检查USB连接和ADB驱动")
        print("2. 确保启用了USB调试")
        print("3. 运行 'adb devices' 确认设备可见")
    
    if not results.get("存储权限", False):
        print("1. 检查应用是否有存储权限")
        print("2. 对于Android 11+，可能需要特殊权限")
    
    if not results.get("文件操作", False):
        print("1. 检查目录写权限")
        print("2. 尝试手动创建和删除文件")
    
    if not results.get("find命令", False):
        print("1. 设备可能不支持find命令")
        print("2. 尝试使用rm命令替代")
    
    print(f"\n🏁 诊断完成")


if __name__ == "__main__":
    main()
