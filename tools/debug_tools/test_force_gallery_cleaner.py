#!/usr/bin/env python3
"""
测试强力相册清理工具
"""

import sys
import os
import subprocess
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from tools.force_gallery_cleaner import force_gallery_cleaner
from core.logger import log


def create_test_files():
    """创建测试文件"""
    print("📁 创建测试文件...")
    
    test_files = [
        "/sdcard/DCIM/Camera/test_image1.jpg",
        "/sdcard/DCIM/Camera/test_image2.png", 
        "/sdcard/Pictures/test_pic1.jpg",
        "/sdcard/Pictures/test_pic2.gif",
        "/sdcard/Download/test_doc.txt"
    ]
    
    created_files = []
    
    for file_path in test_files:
        try:
            # 确保目录存在
            dir_path = os.path.dirname(file_path)
            subprocess.run(f"adb shell mkdir -p {dir_path}", shell=True, capture_output=True)
            
            # 创建测试文件
            cmd = f'adb shell "echo test_content_$(date +%s) > {file_path}"'
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                print(f"  ✅ 创建成功: {file_path}")
                created_files.append(file_path)
            else:
                print(f"  ❌ 创建失败: {file_path}")
                print(f"    错误: {result.stderr}")
        except Exception as e:
            print(f"  ❌ 创建异常: {file_path}, {e}")
    
    return created_files


def verify_files_deleted(file_paths):
    """验证文件是否被删除"""
    print("🔍 验证文件删除情况...")
    
    remaining_files = []
    
    for file_path in file_paths:
        try:
            cmd = f"adb shell test -f {file_path} && echo 'exists' || echo 'deleted'"
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0 and 'exists' in result.stdout:
                print(f"  ❌ 文件仍存在: {file_path}")
                remaining_files.append(file_path)
            else:
                print(f"  ✅ 文件已删除: {file_path}")
        except Exception as e:
            print(f"  ❌ 验证异常: {file_path}, {e}")
    
    return remaining_files


def test_force_gallery_cleaner():
    """测试强力相册清理工具"""
    print("🔥 测试强力相册清理工具")
    print("=" * 60)
    
    try:
        # 1. 检查设备连接
        print("\n1. 检查设备连接...")
        if not force_gallery_cleaner.check_device_connection():
            print("❌ 设备连接失败，无法继续测试")
            return False
        
        # 2. 获取初始状态
        print("\n2. 获取初始相册状态...")
        initial_status = force_gallery_cleaner.get_gallery_status()
        
        print("初始状态:")
        total_files_before = 0
        for folder_type, info in initial_status.items():
            exists = "存在" if info.get('exists', False) else "不存在"
            file_count = info.get('file_count', 0)
            total_files_before += file_count
            print(f"  {folder_type}: {exists}, {file_count} 个文件")
        
        print(f"总文件数: {total_files_before}")
        
        # 3. 创建测试文件
        print("\n3. 创建测试文件...")
        created_files = create_test_files()
        
        if not created_files:
            print("⚠️ 未能创建测试文件，但继续测试现有文件的清理")
        else:
            print(f"成功创建 {len(created_files)} 个测试文件")
        
        # 4. 获取创建文件后的状态
        print("\n4. 获取创建文件后的状态...")
        after_create_status = force_gallery_cleaner.get_gallery_status()
        
        print("创建文件后状态:")
        total_files_after_create = 0
        for folder_type, info in after_create_status.items():
            exists = "存在" if info.get('exists', False) else "不存在"
            file_count = info.get('file_count', 0)
            total_files_after_create += file_count
            initial_count = initial_status.get(folder_type, {}).get('file_count', 0)
            change = file_count - initial_count
            change_str = f"(+{change})" if change > 0 else f"({change})" if change < 0 else ""
            print(f"  {folder_type}: {exists}, {file_count} 个文件 {change_str}")
        
        print(f"总文件数: {total_files_after_create}")
        
        # 5. 强力清理相机文件夹
        print("\n5. 强力清理相机文件夹...")
        camera_success = force_gallery_cleaner.force_clear_gallery_folder('camera')
        print(f"相机文件夹强力清理结果: {'✅ 成功' if camera_success else '❌ 失败'}")
        
        # 6. 强力清理图片文件夹
        print("\n6. 强力清理图片文件夹...")
        pictures_success = force_gallery_cleaner.force_clear_gallery_folder('pictures')
        print(f"图片文件夹强力清理结果: {'✅ 成功' if pictures_success else '❌ 失败'}")
        
        # 7. 强力清理下载文件夹
        print("\n7. 强力清理下载文件夹...")
        download_success = force_gallery_cleaner.force_clear_gallery_folder('download')
        print(f"下载文件夹强力清理结果: {'✅ 成功' if download_success else '❌ 失败'}")
        
        # 8. 获取清理后的状态
        print("\n8. 获取清理后的状态...")
        final_status = force_gallery_cleaner.get_gallery_status()
        
        print("强力清理后状态:")
        total_files_final = 0
        for folder_type, info in final_status.items():
            exists = "存在" if info.get('exists', False) else "不存在"
            file_count = info.get('file_count', 0)
            total_files_final += file_count
            before_count = after_create_status.get(folder_type, {}).get('file_count', 0)
            change = file_count - before_count
            change_str = f"({change})" if change != 0 else ""
            print(f"  {folder_type}: {exists}, {file_count} 个文件 {change_str}")
            
            # 显示剩余文件
            if file_count > 0 and info.get('files'):
                print(f"    剩余文件示例:")
                for remaining_file in info['files'][:3]:
                    print(f"      {remaining_file}")
        
        print(f"总文件数: {total_files_final}")
        
        # 9. 验证测试文件是否被删除
        if created_files:
            print("\n9. 验证测试文件是否被删除...")
            remaining_files = verify_files_deleted(created_files)
            deleted_count = len(created_files) - len(remaining_files)
            print(f"删除了 {deleted_count}/{len(created_files)} 个测试文件")
            
            if remaining_files:
                print("仍然存在的测试文件:")
                for file_path in remaining_files:
                    print(f"  {file_path}")
        
        # 10. 测试强力清理所有文件夹
        if total_files_final > 0:
            print("\n10. 测试强力清理所有文件夹...")
            all_results = force_gallery_cleaner.force_clear_all_gallery_folders()
            
            print("所有文件夹强力清理结果:")
            for folder_type, success in all_results.items():
                status = "✅ 成功" if success else "❌ 失败"
                print(f"  {folder_type}: {status}")
            
            # 最终状态检查
            print("\n最终状态检查...")
            ultimate_status = force_gallery_cleaner.get_gallery_status()
            
            total_files_ultimate = 0
            for folder_type, info in ultimate_status.items():
                file_count = info.get('file_count', 0)
                total_files_ultimate += file_count
                if file_count > 0:
                    print(f"  {folder_type}: 仍有 {file_count} 个文件")
            
            print(f"最终总文件数: {total_files_ultimate}")
        
        # 11. 总结
        print("\n" + "=" * 60)
        print("🎉 强力清理测试完成！")
        
        results = {
            '设备连接': True,
            '获取状态': bool(initial_status),
            '相机文件夹清理': camera_success,
            '图片文件夹清理': pictures_success,
            '下载文件夹清理': download_success
        }
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        print(f"测试结果: {success_count}/{total_count} 项通过")
        print("\n详细结果:")
        for test_name, success in results.items():
            status = "✅ 通过" if success else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        # 清理效果评估
        files_cleared = total_files_after_create - total_files_final
        if total_files_after_create > 0:
            clear_rate = (files_cleared / total_files_after_create) * 100
            print(f"\n清理效果: 删除了 {files_cleared} 个文件 ({clear_rate:.1f}%)")
        
        return success_count == total_count
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return False


def main():
    """主函数"""
    try:
        success = test_force_gallery_cleaner()
        
        if success:
            print("\n🎉 强力清理工具测试通过！")
        else:
            print("\n⚠️ 强力清理工具测试失败，请检查上面的详细信息。")
        
        print("\n💡 使用建议:")
        print("1. 如果强力清理仍然失败，可能需要root权限")
        print("2. 某些系统保护的文件可能无法删除")
        print("3. 建议在测试用例中使用强力清理作为备选方案")
        print("4. 可以结合普通清理和强力清理使用")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")


if __name__ == "__main__":
    main()
