#!/usr/bin/env python3
"""
调试ADB输出处理问题
"""

import subprocess
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)


def test_adb_output():
    """测试ADB输出处理"""
    print("🔍 测试ADB输出处理...")
    
    test_path = "/sdcard/DCIM/Camera"
    
    # 测试1: 基本ls命令
    print(f"\n1. 测试基本ls命令: adb shell ls {test_path}")
    try:
        result = subprocess.run(
            f"adb shell ls {test_path}",
            shell=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='ignore',
            timeout=10
        )
        
        print(f"返回码: {result.returncode}")
        print(f"输出长度: {len(result.stdout) if result.stdout else 0}")
        print(f"错误长度: {len(result.stderr) if result.stderr else 0}")
        print(f"原始输出: {repr(result.stdout)}")
        print(f"原始错误: {repr(result.stderr)}")
        
        if result.stdout:
            lines = result.stdout.split('\n')
            print(f"分割后行数: {len(lines)}")
            for i, line in enumerate(lines):
                print(f"  行{i}: {repr(line)}")
            
            # 处理文件名
            files = []
            for line in lines:
                filename = line.strip()
                if filename and not filename.startswith('ls:') and filename not in ['.', '..']:
                    full_path = f"{test_path}/{filename}"
                    files.append(full_path)
                    print(f"  找到文件: {full_path}")
            
            print(f"最终文件列表: {files}")
        
    except Exception as e:
        print(f"异常: {e}")
    
    # 测试2: 带2>/dev/null的命令
    print(f"\n2. 测试带错误重定向的命令: adb shell ls {test_path} 2>/dev/null")
    try:
        result = subprocess.run(
            f"adb shell ls {test_path} 2>/dev/null",
            shell=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='ignore',
            timeout=10
        )
        
        print(f"返回码: {result.returncode}")
        print(f"输出: {repr(result.stdout)}")
        print(f"错误: {repr(result.stderr)}")
        
    except Exception as e:
        print(f"异常: {e}")
    
    # 测试3: 带|| true的命令
    print(f"\n3. 测试带|| true的命令: adb shell ls {test_path} 2>/dev/null || true")
    try:
        result = subprocess.run(
            f"adb shell ls {test_path} 2>/dev/null || true",
            shell=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='ignore',
            timeout=10
        )
        
        print(f"返回码: {result.returncode}")
        print(f"输出: {repr(result.stdout)}")
        print(f"错误: {repr(result.stderr)}")
        
    except Exception as e:
        print(f"异常: {e}")
    
    # 测试4: 直接删除文件
    print(f"\n4. 测试删除文件...")
    test_file = f"{test_path}/test_image1.jpg"
    
    try:
        # 先验证文件存在
        verify_cmd = f"adb shell test -f {test_file} && echo 'exists' || echo 'not_exists'"
        result = subprocess.run(
            verify_cmd,
            shell=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='ignore',
            timeout=10
        )
        
        print(f"文件存在性检查: {result.stdout.strip()}")
        
        if 'exists' in result.stdout:
            # 尝试删除
            delete_cmd = f"adb shell rm -f {test_file}"
            delete_result = subprocess.run(
                delete_cmd,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=10
            )
            
            print(f"删除命令返回码: {delete_result.returncode}")
            print(f"删除命令输出: {repr(delete_result.stdout)}")
            print(f"删除命令错误: {repr(delete_result.stderr)}")
            
            # 再次验证文件是否被删除
            verify_result = subprocess.run(
                verify_cmd,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=10
            )
            
            print(f"删除后文件存在性: {verify_result.stdout.strip()}")
        
    except Exception as e:
        print(f"删除测试异常: {e}")


if __name__ == "__main__":
    test_adb_output()
