#!/usr/bin/env python3
"""
测试Download文件夹清理功能
"""

import sys
import os
import subprocess
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.logger import log


def create_test_files_in_download():
    """在Download文件夹中创建测试文件"""
    print("📁 在Download文件夹中创建测试文件...")
    
    test_files = [
        "/sdcard/Download/test_image1.jpg",
        "/sdcard/Download/test_image2.png",
        "/sdcard/Download/test_image3.gif",
        "/sdcard/Download/IMG_20231201_123456.jpg",
        "/sdcard/Download/Screenshot_20231201_123456.png",
        "/sdcard/Download/test_document.txt"
    ]
    
    created_files = []
    
    for file_path in test_files:
        try:
            # 创建测试文件
            cmd = f'adb shell "echo test_content_$(date +%s) > {file_path}"'
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                print(f"  ✅ 创建成功: {file_path}")
                created_files.append(file_path)
            else:
                print(f"  ❌ 创建失败: {file_path}")
        except Exception as e:
            print(f"  ❌ 创建异常: {file_path}, {e}")
    
    return created_files


def check_files_exist(file_paths):
    """检查文件是否存在"""
    print("🔍 检查文件存在性...")
    
    existing_files = []
    
    for file_path in file_paths:
        try:
            cmd = f"adb shell test -f {file_path} && echo 'exists' || echo 'not_exists'"
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0 and 'exists' in result.stdout:
                print(f"  ✅ 文件存在: {file_path}")
                existing_files.append(file_path)
            else:
                print(f"  ❌ 文件不存在: {file_path}")
        except Exception as e:
            print(f"  ❌ 检查异常: {file_path}, {e}")
    
    return existing_files


def test_download_clearing():
    """测试Download文件夹清理功能"""
    print("🧪 测试Download文件夹清理功能")
    print("=" * 50)
    
    try:
        from tools.force_gallery_cleaner import (
            clear_download_images,
            clear_all_download_files,
            one_second_clear,
            instant_clear
        )
        
        # 1. 创建测试文件
        print("\n1. 创建测试文件...")
        created_files = create_test_files_in_download()
        
        if not created_files:
            print("❌ 未能创建测试文件，无法继续测试")
            return False
        
        print(f"成功创建 {len(created_files)} 个测试文件")
        
        # 2. 验证文件存在
        print("\n2. 验证文件存在...")
        existing_files = check_files_exist(created_files)
        print(f"确认存在 {len(existing_files)} 个文件")
        
        # 3. 测试只清理图片
        print("\n3. 测试只清理Download文件夹中的图片...")
        start_time = time.time()
        result = clear_download_images()
        end_time = time.time()
        
        print(f"清理结果: {'✅ 成功' if result else '❌ 失败'}")
        print(f"耗时: {end_time - start_time:.2f} 秒")
        
        # 4. 验证图片清理结果
        print("\n4. 验证图片清理结果...")
        remaining_files = check_files_exist(created_files)
        
        image_files = [f for f in created_files if any(f.endswith(ext) for ext in ['.jpg', '.png', '.gif'])]
        remaining_images = [f for f in remaining_files if any(f.endswith(ext) for ext in ['.jpg', '.png', '.gif'])]
        
        print(f"原有图片文件: {len(image_files)} 个")
        print(f"剩余图片文件: {len(remaining_images)} 个")
        print(f"删除图片文件: {len(image_files) - len(remaining_images)} 个")
        
        # 5. 测试清理所有文件
        print("\n5. 测试清理Download文件夹中的所有文件...")
        start_time = time.time()
        result = clear_all_download_files()
        end_time = time.time()
        
        print(f"清理结果: {'✅ 成功' if result else '❌ 失败'}")
        print(f"耗时: {end_time - start_time:.2f} 秒")
        
        # 6. 验证完全清理结果
        print("\n6. 验证完全清理结果...")
        final_remaining = check_files_exist(created_files)
        
        print(f"最终剩余文件: {len(final_remaining)} 个")
        
        if len(final_remaining) == 0:
            print("🎉 所有文件都被成功清理！")
        else:
            print("⚠️ 仍有文件未被清理:")
            for file_path in final_remaining:
                print(f"  {file_path}")
        
        # 7. 测试快速清理方法
        print("\n7. 测试快速清理方法是否包含Download文件夹...")
        
        # 重新创建一些测试文件
        test_files_quick = [
            "/sdcard/Download/quick_test1.jpg",
            "/sdcard/Download/quick_test2.png"
        ]
        
        for file_path in test_files_quick:
            cmd = f'adb shell "echo quick_test > {file_path}"'
            subprocess.run(cmd, shell=True, capture_output=True)
        
        # 使用一秒清理
        print("使用一秒清理...")
        start_time = time.time()
        result = one_second_clear()
        end_time = time.time()
        
        print(f"清理结果: {'✅ 成功' if result else '❌ 失败'}")
        print(f"耗时: {end_time - start_time:.2f} 秒")
        
        # 验证快速清理结果
        quick_remaining = check_files_exist(test_files_quick)
        print(f"快速清理后剩余文件: {len(quick_remaining)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return False


def main():
    """主函数"""
    try:
        success = test_download_clearing()
        
        if success:
            print("\n🎉 Download文件夹清理功能测试完成！")
        else:
            print("\n⚠️ Download文件夹清理功能测试失败！")
        
        print("\n💡 使用建议:")
        print("1. 只清理Download中的图片: clear_download_images()")
        print("2. 清理Download中的所有文件: clear_all_download_files()")
        print("3. 快速清理所有文件夹(含Download): one_second_clear()")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")


if __name__ == "__main__":
    main()
