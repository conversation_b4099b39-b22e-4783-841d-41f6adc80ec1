"""
强力相册清理工具
专门解决图片删除不彻底的问题
"""
import subprocess
import time
from typing import List, Dict, Optional
from core.logger import log


class ForceGalleryCleaner:
    """强力相册清理工具类"""

    def __init__(self):
        """初始化强力相册清理工具"""
        # 常见的相册目录路径
        self.gallery_paths = {
            'camera': '/sdcard/DCIM/Camera',
            'pictures': '/sdcard/Pictures',
            'download': '/sdcard/Download',
            'screenshots': '/sdcard/Pictures/Screenshots',
            'dcim': '/sdcard/DCIM'
        }
        
        # 常见的图片和视频扩展名
        self.media_extensions = [
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff',
            'mp4', 'avi', 'mov', 'mkv', '3gp', 'wmv', 'flv'
        ]

    def _run_adb_command(self, cmd: str, timeout: int = 30) -> subprocess.CompletedProcess:
        """执行ADB命令"""
        try:
            log.debug(f"执行命令: {cmd}")
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=timeout
            )
            log.debug(f"命令返回码: {result.returncode}")
            if result.stdout:
                log.debug(f"命令输出: {result.stdout[:200]}...")
            if result.stderr:
                log.debug(f"命令错误: {result.stderr[:200]}...")
            return result
        except subprocess.TimeoutExpired:
            log.error(f"ADB命令执行超时: {cmd}")
            return subprocess.CompletedProcess(cmd, 1, "", "命令执行超时")
        except Exception as e:
            log.error(f"执行ADB命令时出错: {e}")
            return subprocess.CompletedProcess(cmd, 1, "", str(e))

    def check_device_connection(self) -> bool:
        """检查设备连接状态"""
        try:
            result = self._run_adb_command("adb devices")
            if result.returncode == 0 and "device" in result.stdout:
                log.info("✅ 设备连接正常")
                return True
            else:
                log.error("❌ 设备未连接或ADB不可用")
                return False
        except Exception as e:
            log.error(f"检查设备连接失败: {e}")
            return False

    def force_clear_directory(self, path: str) -> bool:
        """强力清理目录"""
        try:
            log.info(f"🔥 开始强力清理目录: {path}")

            # 检查设备连接
            if not self.check_device_connection():
                return False

            # 检查目录是否存在
            if not self._check_directory_exists(path):
                log.warning(f"目录不存在，无需清理: {path}")
                return True

            # 获取清理前的文件列表
            files_before = self._list_all_files(path)
            log.info(f"清理前找到 {len(files_before)} 个文件")

            # 执行强力清理
            success = self._execute_force_cleanup(path, files_before)

            if success:
                # 验证清理结果
                files_after = self._list_all_files(path)
                cleared_count = len(files_before) - len(files_after)
                log.info(f"✅ 强力清理完成: 删除了 {cleared_count} 个文件")
                
                if files_after:
                    log.warning(f"⚠️ 仍有 {len(files_after)} 个文件未删除")
                    for remaining_file in files_after[:5]:  # 只显示前5个
                        log.warning(f"  未删除: {remaining_file}")
                
                # 刷新媒体库
                self._force_refresh_media_library()
                return True
            else:
                log.error("❌ 强力清理失败")
                return False

        except Exception as e:
            log.error(f"强力清理目录失败: {e}")
            return False

    def _check_directory_exists(self, path: str) -> bool:
        """检查目录是否存在"""
        try:
            cmd = f"adb shell test -d {path} && echo 'exists' || echo 'not_exists'"
            result = self._run_adb_command(cmd)
            return result.returncode == 0 and 'exists' in result.stdout
        except Exception as e:
            log.error(f"检查目录存在性失败: {e}")
            return False

    def _list_all_files(self, path: str) -> List[str]:
        """列出目录中的所有文件"""
        try:
            files = []
            log.debug(f"开始列出目录文件: {path}")

            # 方法1: 使用简单的ls命令（Windows兼容）
            cmd = f"adb shell ls {path}"
            result = self._run_adb_command(cmd)
            log.debug(f"ls命令结果: 返回码={result.returncode}, 输出长度={len(result.stdout) if result.stdout else 0}")

            if result.returncode == 0 and result.stdout.strip():
                log.debug(f"ls输出内容: {result.stdout[:200]}...")
                for line in result.stdout.split('\n'):
                    filename = line.strip()
                    if filename and not filename.startswith('ls:') and filename not in ['.', '..']:
                        full_path = f"{path}/{filename}"
                        files.append(full_path)
                        log.debug(f"找到文件: {full_path}")

            # 方法2: 使用ls -1 (单列显示)
            if not files:
                cmd = f"adb shell ls -1 {path}"
                result = self._run_adb_command(cmd)
                log.debug(f"ls -1命令结果: 返回码={result.returncode}")

                if result.returncode == 0 and result.stdout.strip():
                    for line in result.stdout.split('\n'):
                        filename = line.strip()
                        if filename and filename not in ['.', '..']:
                            full_path = f"{path}/{filename}"
                            files.append(full_path)
                            log.debug(f"找到文件(方法2): {full_path}")

            # 方法3: 使用通配符列出文件
            if not files:
                cmd = f"adb shell ls {path}/*"
                result = self._run_adb_command(cmd)
                log.debug(f"通配符命令结果: 返回码={result.returncode}")

                if result.returncode == 0 and result.stdout.strip():
                    for line in result.stdout.split('\n'):
                        filepath = line.strip()
                        if filepath and filepath.startswith('/') and not filepath.endswith('/'):
                            files.append(filepath)
                            log.debug(f"找到文件(方法3): {filepath}")

            # 方法4: 使用find命令
            if not files:
                cmd = f"adb shell find {path} -maxdepth 1 -type f"
                result = self._run_adb_command(cmd)
                log.debug(f"find命令结果: 返回码={result.returncode}")

                if result.returncode == 0 and result.stdout.strip():
                    for line in result.stdout.split('\n'):
                        filepath = line.strip()
                        if filepath and filepath.startswith('/'):
                            files.append(filepath)
                            log.debug(f"找到文件(方法4): {filepath}")

            # 方法5: 手动验证每个可能的文件
            if not files:
                log.debug("尝试手动验证常见文件...")
                common_extensions = ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'txt', 'pdf']
                for ext in common_extensions:
                    verify_cmd = f"adb shell ls {path}/*.{ext}"
                    verify_result = self._run_adb_command(verify_cmd)
                    if verify_result.returncode == 0 and verify_result.stdout.strip():
                        for line in verify_result.stdout.split('\n'):
                            filepath = line.strip()
                            if filepath and filepath.startswith('/'):
                                files.append(filepath)
                                log.debug(f"找到文件(方法5): {filepath}")

            log.info(f"最终找到 {len(files)} 个文件在 {path}")
            return files

        except Exception as e:
            log.error(f"列出文件失败: {e}")
            return []

    def _execute_force_cleanup(self, path: str, files: List[str]) -> bool:
        """执行强力清理"""
        try:
            success = True
            
            # 策略1: 逐个删除文件
            log.info("策略1: 逐个删除文件...")
            deleted_count = 0
            
            for file_path in files:
                if self._delete_single_file(file_path):
                    deleted_count += 1
            
            log.info(f"逐个删除: {deleted_count}/{len(files)} 个文件")
            
            # 策略2: 批量删除常见扩展名
            log.info("策略2: 批量删除常见扩展名...")
            for ext in self.media_extensions:
                self._delete_by_extension(path, ext)
            
            # 策略3: 删除所有文件（通配符）
            log.info("策略3: 通配符删除...")
            wildcard_commands = [
                f"adb shell rm -f {path}/*",
                f"adb shell rm -rf {path}/*"
            ]

            for cmd in wildcard_commands:
                result = self._run_adb_command(cmd)
                if result.returncode == 0:
                    log.debug("通配符删除执行成功")

            # 策略4: 使用shell脚本删除
            log.info("策略4: Shell脚本删除...")
            # 简化的shell脚本，避免复杂的语法
            shell_commands = [
                f"adb shell rm -f {path}/*.*",
                f"adb shell rm -f {path}/*"
            ]

            for cmd in shell_commands:
                result = self._run_adb_command(cmd)
                if result.returncode == 0:
                    log.debug("Shell命令删除执行成功")
            
            return success
            
        except Exception as e:
            log.error(f"执行强力清理失败: {e}")
            return False

    def _delete_single_file(self, file_path: str) -> bool:
        """删除单个文件"""
        try:
            # 尝试多种删除命令（Windows兼容）
            delete_commands = [
                f"adb shell rm -f \"{file_path}\"",
                f"adb shell rm \"{file_path}\"",
                f"adb shell unlink \"{file_path}\""
            ]

            for cmd in delete_commands:
                result = self._run_adb_command(cmd)
                if result.returncode == 0:
                    # 验证文件是否真的被删除
                    verify_cmd = f"adb shell test -f \"{file_path}\" && echo 'exists' || echo 'deleted'"
                    verify_result = self._run_adb_command(verify_cmd)

                    if verify_result.returncode == 0 and 'deleted' in verify_result.stdout:
                        log.debug(f"成功删除文件: {file_path}")
                        return True

            log.warning(f"删除文件失败: {file_path}")
            return False
            
        except Exception as e:
            log.error(f"删除单个文件异常: {e}")
            return False

    def _delete_by_extension(self, path: str, ext: str) -> bool:
        """按扩展名删除文件"""
        try:
            commands = [
                f"adb shell rm -f {path}/*.{ext}",
                f"adb shell rm -f {path}/*.{ext.upper()}"
            ]

            success = False
            for cmd in commands:
                result = self._run_adb_command(cmd)
                if result.returncode == 0:
                    success = True

            return success
            
        except Exception as e:
            log.error(f"按扩展名删除失败: {e}")
            return False

    def _force_refresh_media_library(self) -> bool:
        """强力刷新媒体库"""
        try:
            log.info("🔄 强力刷新媒体库...")

            # 1. 停止媒体相关服务
            services_to_stop = [
                "com.android.providers.media",
                "com.google.android.providers.media",
                "com.android.gallery3d",
                "com.google.android.apps.photos"
            ]
            
            for service in services_to_stop:
                cmd = f"adb shell am force-stop {service} 2>/dev/null || true"
                self._run_adb_command(cmd)
            
            # 2. 清除媒体数据库缓存
            cache_commands = [
                "adb shell pm clear com.android.providers.media 2>/dev/null || true",
                "adb shell rm -rf /data/data/com.android.providers.media/databases/* 2>/dev/null || true"
            ]
            
            for cmd in cache_commands:
                self._run_adb_command(cmd)
            
            # 3. 重新扫描媒体库
            scan_commands = [
                "adb shell am broadcast -a android.intent.action.MEDIA_SCANNER_SCAN_FILE -d file:///sdcard/",
                "adb shell am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard/",
                "adb shell am broadcast -a android.intent.action.MEDIA_SCANNER_STARTED -d file:///sdcard/"
            ]
            
            for cmd in scan_commands:
                self._run_adb_command(cmd)
            
            # 4. 等待媒体库更新
            time.sleep(5)
            
            log.info("✅ 媒体库强力刷新完成")
            return True

        except Exception as e:
            log.error(f"强力刷新媒体库失败: {e}")
            return False

    def force_clear_gallery_folder(self, folder_type: str) -> bool:
        """强力清理指定的相册文件夹"""
        try:
            if folder_type not in self.gallery_paths:
                log.error(f"不支持的文件夹类型: {folder_type}")
                log.info(f"支持的类型: {list(self.gallery_paths.keys())}")
                return False

            target_path = self.gallery_paths[folder_type]
            return self.force_clear_directory(target_path)

        except Exception as e:
            log.error(f"强力清理相册文件夹失败: {e}")
            return False

    def force_clear_all_gallery_folders(self) -> Dict[str, bool]:
        """强力清理所有相册文件夹"""
        try:
            log.info("🔥 开始强力清理所有相册文件夹")
            results = {}

            for folder_type in self.gallery_paths.keys():
                log.info(f"强力清理文件夹: {folder_type}")
                results[folder_type] = self.force_clear_gallery_folder(folder_type)

            # 统计结果
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            log.info(f"强力清理完成: {success_count}/{total_count} 个文件夹成功")

            return results

        except Exception as e:
            log.error(f"强力清理所有相册文件夹失败: {e}")
            return {}

    def get_gallery_status(self) -> Dict[str, Dict]:
        """获取相册状态"""
        try:
            log.info("📊 获取相册状态...")
            status = {}

            for folder_type, path in self.gallery_paths.items():
                files = self._list_all_files(path)
                folder_status = {
                    'path': path,
                    'exists': self._check_directory_exists(path),
                    'file_count': len(files),
                    'files': files[:5]  # 只显示前5个文件
                }
                status[folder_type] = folder_status

            return status

        except Exception as e:
            log.error(f"获取相册状态失败: {e}")
            return {}


# 全局实例
force_gallery_cleaner = ForceGalleryCleaner()


def force_clear_camera_folder() -> bool:
    """强力清空相机文件夹"""
    return force_gallery_cleaner.force_clear_gallery_folder('camera')


def force_clear_all_galleries() -> Dict[str, bool]:
    """强力清空所有相册文件夹"""
    return force_gallery_cleaner.force_clear_all_gallery_folders()


if __name__ == "__main__":
    # 测试强力相册清理功能
    cleaner = ForceGalleryCleaner()
    
    if cleaner.check_device_connection():
        # 获取当前状态
        status = cleaner.get_gallery_status()
        print("当前相册状态:")
        for folder, info in status.items():
            print(f"  {folder}: {info['file_count']} 个文件")
        
        # 强力清理相机文件夹
        print("\n强力清理相机文件夹...")
        success = cleaner.force_clear_gallery_folder('camera')
        print(f"清理结果: {'成功' if success else '失败'}")
        
        # 再次检查状态
        status_after = cleaner.get_gallery_status()
        print("\n清理后状态:")
        for folder, info in status_after.items():
            print(f"  {folder}: {info['file_count']} 个文件")
    else:
        print("请确保设备已连接并启用USB调试")
