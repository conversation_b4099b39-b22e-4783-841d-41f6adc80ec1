"""
强力相册清理工具
专门解决图片删除不彻底的问题，支持多种删除策略
"""
import subprocess
import time
from typing import List, Dict, Optional
from core.logger import log


class ForceGalleryCleaner:
    """强力相册清理工具类"""

    def __init__(self):
        """初始化强力相册清理工具"""
        # 常见的相册目录路径
        self.gallery_paths = {
            'camera': '/sdcard/DCIM/Camera',
            'pictures': '/sdcard/Pictures', 
            'download': '/sdcard/Download',
            'screenshots': '/sdcard/Pictures/Screenshots',
            'dcim': '/sdcard/DCIM',
            'movies': '/sdcard/Movies',
            'music': '/sdcard/Music'
        }
        
        # 常见的媒体文件扩展名
        self.media_extensions = [
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg',
            'mp4', 'avi', 'mov', 'mkv', '3gp', 'wmv', 'flv', 'webm',
            'mp3', 'wav', 'aac', 'flac', 'ogg', 'm4a',
            'txt', 'pdf', 'doc', 'docx'
        ]

    def _run_adb_command(self, cmd: str, timeout: int = 30) -> subprocess.CompletedProcess:
        """执行ADB命令"""
        try:
            log.debug(f"执行命令: {cmd}")
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=timeout
            )
            log.debug(f"命令返回码: {result.returncode}")
            return result
        except subprocess.TimeoutExpired:
            log.error(f"ADB命令执行超时: {cmd}")
            return subprocess.CompletedProcess(cmd, 1, "", "命令执行超时")
        except Exception as e:
            log.error(f"执行ADB命令时出错: {e}")
            return subprocess.CompletedProcess(cmd, 1, "", str(e))

    def check_device_connection(self) -> bool:
        """检查设备连接状态"""
        try:
            result = self._run_adb_command("adb devices")
            if result.returncode == 0 and "device" in result.stdout:
                log.info("✅ 设备连接正常")
                return True
            else:
                log.error("❌ 设备未连接或ADB不可用")
                return False
        except Exception as e:
            log.error(f"检查设备连接失败: {e}")
            return False

    def clear_directory_completely(self, path: str) -> bool:
        """完全清理目录中的所有文件"""
        try:
            log.info(f"🔥 开始完全清理目录: {path}")

            # 检查设备连接
            if not self.check_device_connection():
                return False

            # 检查目录是否存在
            if not self._check_directory_exists(path):
                log.warning(f"目录不存在，无需清理: {path}")
                return True

            # 获取清理前的文件数量
            files_before = self._count_files_in_directory(path)
            log.info(f"清理前文件数量: {files_before}")

            # 执行多种清理策略
            success = self._execute_multiple_cleanup_strategies(path)

            if success:
                # 验证清理结果
                files_after = self._count_files_in_directory(path)
                cleared_count = files_before - files_after
                log.info(f"✅ 清理完成: 删除了 {cleared_count} 个文件")
                
                # 刷新媒体库
                self._refresh_media_library()
                return True
            else:
                log.error("❌ 清理操作失败")
                return False

        except Exception as e:
            log.error(f"完全清理目录失败: {e}")
            return False

    def clear_images_only(self, path: str) -> bool:
        """只清理图片文件"""
        try:
            log.info(f"🖼️ 开始清理图片文件: {path}")

            # 检查设备连接
            if not self.check_device_connection():
                return False

            # 图片扩展名
            image_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg']
            
            success = True
            for ext in image_extensions:
                if not self._delete_files_by_extension(path, ext):
                    success = False

            if success:
                log.info("✅ 图片文件清理完成")
                self._refresh_media_library()
            else:
                log.warning("⚠️ 部分图片文件清理失败")

            return success

        except Exception as e:
            log.error(f"清理图片文件失败: {e}")
            return False

    def _check_directory_exists(self, path: str) -> bool:
        """检查目录是否存在"""
        try:
            cmd = f"adb shell test -d {path} && echo 'exists' || echo 'not_exists'"
            result = self._run_adb_command(cmd)
            return result.returncode == 0 and 'exists' in result.stdout
        except Exception as e:
            log.error(f"检查目录存在性失败: {e}")
            return False

    def _count_files_in_directory(self, path: str) -> int:
        """计算目录中的文件数量"""
        try:
            # 使用ls命令计算文件数量
            cmd = f"adb shell ls {path}"
            result = self._run_adb_command(cmd)
            
            if result.returncode == 0 and result.stdout.strip():
                files = [f.strip() for f in result.stdout.split('\n') if f.strip() and not f.startswith('ls:')]
                return len(files)
            
            return 0
        except Exception as e:
            log.debug(f"计算文件数量失败: {e}")
            return 0

    def _execute_multiple_cleanup_strategies(self, path: str) -> bool:
        """执行多种清理策略"""
        try:
            success = False
            
            # 策略1: 删除所有文件（通配符）
            log.info("策略1: 通配符删除所有文件...")
            wildcard_commands = [
                f"adb shell rm -f {path}/*",
                f"adb shell rm -rf {path}/*"
            ]
            
            for cmd in wildcard_commands:
                result = self._run_adb_command(cmd)
                if result.returncode == 0:
                    success = True
                    log.debug("通配符删除执行成功")
            
            # 策略2: 按扩展名删除
            log.info("策略2: 按扩展名删除...")
            for ext in self.media_extensions:
                self._delete_files_by_extension(path, ext)
            
            # 策略3: 使用find命令删除
            log.info("策略3: find命令删除...")
            find_commands = [
                f"adb shell find {path} -type f -delete",
                f"adb shell find {path} -maxdepth 1 -type f -delete"
            ]
            
            for cmd in find_commands:
                result = self._run_adb_command(cmd)
                if result.returncode == 0:
                    success = True
                    log.debug("find命令删除执行成功")
            
            return success
            
        except Exception as e:
            log.error(f"执行多种清理策略失败: {e}")
            return False

    def _delete_files_by_extension(self, path: str, ext: str) -> bool:
        """按扩展名删除文件"""
        try:
            commands = [
                f"adb shell rm -f {path}/*.{ext}",
                f"adb shell rm -f {path}/*.{ext.upper()}"
            ]
            
            success = False
            for cmd in commands:
                result = self._run_adb_command(cmd)
                if result.returncode == 0:
                    success = True
            
            return success
            
        except Exception as e:
            log.error(f"按扩展名删除文件失败: {e}")
            return False

    def _refresh_media_library(self) -> bool:
        """刷新媒体库"""
        try:
            log.info("🔄 刷新媒体库...")

            # 停止媒体相关服务
            services_to_stop = [
                "com.android.providers.media",
                "com.google.android.providers.media"
            ]
            
            for service in services_to_stop:
                cmd = f"adb shell am force-stop {service}"
                self._run_adb_command(cmd)
            
            # 重新扫描媒体库
            scan_commands = [
                "adb shell am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard/",
                "adb shell am broadcast -a android.intent.action.MEDIA_SCANNER_STARTED -d file:///sdcard/"
            ]
            
            for cmd in scan_commands:
                self._run_adb_command(cmd)
            
            # 等待媒体库更新
            time.sleep(3)
            
            log.info("✅ 媒体库刷新完成")
            return True

        except Exception as e:
            log.error(f"刷新媒体库失败: {e}")
            return False

    def clear_camera_folder(self) -> bool:
        """清理相机文件夹"""
        return self.clear_directory_completely(self.gallery_paths['camera'])

    def clear_pictures_folder(self) -> bool:
        """清理图片文件夹"""
        return self.clear_directory_completely(self.gallery_paths['pictures'])

    def clear_download_folder(self) -> bool:
        """清理下载文件夹"""
        return self.clear_directory_completely(self.gallery_paths['download'])

    def clear_all_gallery_folders(self) -> Dict[str, bool]:
        """清理所有相册文件夹"""
        try:
            log.info("🔥 开始清理所有相册文件夹")
            results = {}

            for folder_type, path in self.gallery_paths.items():
                log.info(f"清理文件夹: {folder_type}")
                results[folder_type] = self.clear_directory_completely(path)

            # 统计结果
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            log.info(f"清理完成: {success_count}/{total_count} 个文件夹成功")

            return results

        except Exception as e:
            log.error(f"清理所有相册文件夹失败: {e}")
            return {}

    def quick_clear_ai_gallery(self) -> bool:
        """快速清理AI Gallery中的所有图片（最简洁的方法）"""
        try:
            log.info("⚡ 快速清理AI Gallery图片...")

            # 检查设备连接
            if not self.check_device_connection():
                return False

            # 主要的图片目录（包含Download文件夹）
            main_paths = [
                '/sdcard/DCIM/Camera',
                '/sdcard/Pictures',
                '/sdcard/Download'
            ]

            success = True
            total_cleared = 0

            for path in main_paths:
                try:
                    # 直接使用最简单的删除命令
                    cmd = f"adb shell rm -rf {path}/*"
                    result = self._run_adb_command(cmd, timeout=10)

                    if result.returncode == 0:
                        log.info(f"✅ 快速清理成功: {path}")
                        total_cleared += 1
                    else:
                        log.warning(f"⚠️ 快速清理失败: {path}")
                        success = False

                except Exception as e:
                    log.error(f"清理路径异常 {path}: {e}")
                    success = False

            # 快速刷新媒体库
            self._quick_refresh_media()

            log.info(f"⚡ 快速清理完成: {total_cleared}/{len(main_paths)} 个目录成功")
            return success

        except Exception as e:
            log.error(f"快速清理AI Gallery失败: {e}")
            return False

    def _quick_refresh_media(self) -> bool:
        """快速刷新媒体库（简化版本）"""
        try:
            # 只执行最关键的刷新命令
            refresh_cmd = "adb shell am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard/"
            self._run_adb_command(refresh_cmd, timeout=5)

            # 短暂等待
            time.sleep(1)

            return True
        except Exception as e:
            log.debug(f"快速刷新媒体库失败: {e}")
            return False

    def get_gallery_status(self) -> Dict[str, Dict]:
        """获取相册状态"""
        try:
            log.info("📊 获取相册状态...")
            status = {}

            for folder_type, path in self.gallery_paths.items():
                folder_status = {
                    'path': path,
                    'exists': self._check_directory_exists(path),
                    'file_count': self._count_files_in_directory(path)
                }
                status[folder_type] = folder_status

            return status

        except Exception as e:
            log.error(f"获取相册状态失败: {e}")
            return {}


class QuickAIGalleryCleaner:
    """专门用于快速清理AI Gallery的轻量级类"""

    def __init__(self):
        self.main_cleaner = ForceGalleryCleaner()

    def clear_now(self) -> bool:
        """立即清理（最快速的方法）"""
        try:
            log.info("⚡ 立即清理AI Gallery...")

            if not self.main_cleaner.check_device_connection():
                return False

            # 使用最直接的命令（包含Download文件夹）
            commands = [
                "adb shell rm -rf /sdcard/DCIM/Camera/*",
                "adb shell rm -rf /sdcard/Pictures/*",
                "adb shell rm -rf /sdcard/Download/*"
            ]

            success = True
            for cmd in commands:
                result = self.main_cleaner._run_adb_command(cmd, timeout=3)
                if result.returncode != 0:
                    success = False

            # 快速刷新
            self.main_cleaner._quick_refresh_media()

            log.info("⚡ 立即清理完成")
            return success

        except Exception as e:
            log.error(f"立即清理失败: {e}")
            return False

    def clear_images_only(self) -> bool:
        """只清理图片文件（保留其他文件）"""
        try:
            log.info("🖼️ 只清理图片文件...")

            if not self.main_cleaner.check_device_connection():
                return False

            # 图片扩展名
            image_exts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
            paths = ['/sdcard/DCIM/Camera', '/sdcard/Pictures', '/sdcard/Download']

            success = True
            for path in paths:
                for ext in image_exts:
                    cmd = f"adb shell rm -f {path}/*.{ext} {path}/*.{ext.upper()}"
                    result = self.main_cleaner._run_adb_command(cmd, timeout=2)
                    if result.returncode != 0:
                        success = False

            self.main_cleaner._quick_refresh_media()
            log.info("🖼️ 图片清理完成")
            return success

        except Exception as e:
            log.error(f"图片清理失败: {e}")
            return False


# 全局实例
force_gallery_cleaner = ForceGalleryCleaner()
quick_ai_gallery_cleaner = QuickAIGalleryCleaner()


# 便捷函数
def clear_camera_images() -> bool:
    """清理相机文件夹中的所有图片"""
    return force_gallery_cleaner.clear_images_only('/sdcard/DCIM/Camera')


def clear_camera_folder() -> bool:
    """清理相机文件夹中的所有文件"""
    return force_gallery_cleaner.clear_camera_folder()


def clear_all_images() -> bool:
    """清理所有相册文件夹中的图片"""
    success = True
    for folder_type, path in force_gallery_cleaner.gallery_paths.items():
        if not force_gallery_cleaner.clear_images_only(path):
            success = False
    return success


def clear_all_galleries() -> Dict[str, bool]:
    """清理所有相册文件夹"""
    return force_gallery_cleaner.clear_all_gallery_folders()


def quick_clear_ai_gallery() -> bool:
    """⚡ 快速清理AI Gallery（最简洁快速的方法）"""
    return force_gallery_cleaner.quick_clear_ai_gallery()


def super_quick_clear() -> bool:
    """🚀 超快速清理（一键清理所有图片，3秒完成）"""
    return quick_ai_gallery_cleaner.clear_now()


def clear_ai_gallery_images_only() -> bool:
    """🖼️ 只清理AI Gallery中的图片文件（保留其他文件）"""
    return quick_ai_gallery_cleaner.clear_images_only()


def instant_clear() -> bool:
    """⚡ 瞬间清理（最快速度，适合测试前快速清理）"""
    return quick_ai_gallery_cleaner.clear_now()


def one_second_clear() -> bool:
    """⏱️ 一秒清理（极速模式）"""
    try:
        log.info("⏱️ 一秒清理模式...")

        if not force_gallery_cleaner.check_device_connection():
            return False

        # 最简单粗暴的清理命令（包含Download文件夹）
        cmd = "adb shell rm -rf /sdcard/DCIM/Camera/* /sdcard/Pictures/* /sdcard/Download/*"
        result = force_gallery_cleaner._run_adb_command(cmd, timeout=1)

        if result.returncode == 0:
            log.info("⏱️ 一秒清理完成！")
            return True
        else:
            return False

    except Exception as e:
        log.debug(f"一秒清理异常: {e}")
        return False


def clear_download_images() -> bool:
    """📥 专门清理Download文件夹中的图片"""
    try:
        log.info("📥 清理Download文件夹图片...")

        if not force_gallery_cleaner.check_device_connection():
            return False

        # 使用更直接的方法清理Download文件夹
        success = force_gallery_cleaner.clear_directory_completely('/sdcard/Download')

        if success:
            log.info("📥 Download文件夹清理完成")
        else:
            log.warning("⚠️ Download文件夹清理失败")

        return success

    except Exception as e:
        log.error(f"清理Download文件夹失败: {e}")
        return False


def clear_all_download_files() -> bool:
    """📥 清理Download文件夹中的所有文件"""
    try:
        log.info("📥 清理Download文件夹所有文件...")

        if not force_gallery_cleaner.check_device_connection():
            return False

        cmd = "adb shell rm -rf /sdcard/Download/*"
        result = force_gallery_cleaner._run_adb_command(cmd, timeout=3)

        if result.returncode == 0:
            log.info("📥 Download文件夹清理完成")
            return True
        else:
            log.warning("⚠️ Download文件夹清理失败")
            return False

    except Exception as e:
        log.error(f"清理Download文件夹失败: {e}")
        return False


if __name__ == "__main__":
    # 测试相册清理功能
    cleaner = ForceGalleryCleaner()
    
    if cleaner.check_device_connection():
        # 获取当前状态
        status = cleaner.get_gallery_status()
        print("当前相册状态:")
        for folder, info in status.items():
            print(f"  {folder}: {info['file_count']} 个文件")
        
        # 清理相机文件夹
        print("\n清理相机文件夹...")
        success = cleaner.clear_all_gallery_folders()
        print(f"清理结果: {'成功' if success else '失败'}")
    else:
        print("请确保设备已连接并启用USB调试")
