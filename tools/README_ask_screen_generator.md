# Ask Screen 测试用例生成器

基于 Ella 测试生成器设计，专门为 Ask Screen 浮窗功能生成标准化测试用例的工具。

## 功能特性

### 🎯 智能分类检测
自动检测命令类型并分类到对应目录：
- **contact**: 联系人相关（电话号码、联系人卡片等）
- **math_calculation**: 数学计算（算术题、方程式等）
- **translation**: 翻译功能（中英文翻译等）
- **text_recognition**: 文字识别（文档阅读等）
- **object_detection**: 物体检测（识别物品等）
- **scene_understanding**: 场景理解（描述场景等）
- **image_analysis**: 图片分析（通用图片分析）
- **general_qa**: 通用问答（其他类型）

### 🚀 多种生成方式
1. **单个测试用例生成**: 指定命令和图片生成单个测试
2. **批量生成**: 从命令列表批量生成多个测试用例
3. **自动扫描生成**: 从图片文件夹自动扫描并生成测试用例

### 📁 智能文件管理
- 自动创建分类目录结构
- 智能命名（类名、方法名、文件名）
- 避免命名冲突

### 🔧 集成优化的文件推送
- 集成优化后的 `file_pusher` 工具
- 支持智能图片匹配和推送
- 自动处理图片准备流程

## 使用方法

### 1. 导入模块

```python
from tools.ask_screen_test_generator import (
    generate_ask_screen_test,
    batch_generate_ask_screen_tests,
    auto_generate_from_images,
    AskScreenTestGenerator
)
```

### 2. 生成单个测试用例

```python
# 基础用法
file_path = generate_ask_screen_test(
    command="add this number",
    image_name="add_this_number"
)

# 完整用法
file_path = generate_ask_screen_test(
    command="add this number",
    image_name="add_this_number", 
    expected_keywords=["following number is recognized", "save it as a contact"],
    category="contact"  # 可选，会自动检测
)
```

### 3. 批量生成测试用例

```python
# 定义图片和命令对
image_commands = [
    ("contact_card", "add this contact"),
    ("math_problem", "solve this equation"),
    ("english_text", "translate this text"),
    ("scene_photo", "describe this scene")
]

# 批量生成
generated_files = batch_generate_ask_screen_tests(image_commands)
print(f"生成了 {len(generated_files)} 个测试用例")
```

### 4. 从图片文件夹自动生成

```python
# 使用默认路径 (data/static/ask_screen)
generated_files = auto_generate_from_images()

# 使用自定义路径
generated_files = auto_generate_from_images(
    folder_path="/path/to/your/images",
    command_mapping={
        "add_this_number": "add this number",
        "math_calc": "calculate this problem"
    }
)
```

### 5. 高级用法

```python
# 创建生成器实例
generator = AskScreenTestGenerator()

# 检测分类
category = generator.detect_ask_screen_category("add this number", "contact_info")
print(f"检测到分类: {category}")

# 生成名称
class_name = generator.generate_class_name("add this number", "contact_card")
method_name = generator.generate_method_name("add this number", "contact_card")
file_name = generator.generate_file_name("add this number", "contact_card")

# 建议关键词
keywords = generator.suggest_expected_keywords("add this number", "contact")
```

## 生成的测试用例结构

生成的测试用例包含以下特性：

### 📋 标准化结构
- 继承自 `SimpleAskScreenTest`
- 包含完整的 Allure 报告注解
- 标准化的测试步骤和断言

### 🔄 完整的测试流程
1. **数据准备**: 自动推送图片到设备
2. **应用启动**: 打开图库并选择图片
3. **命令执行**: 在浮窗中执行 Ask Screen 命令
4. **结果验证**: 验证响应包含期望关键词
5. **测试记录**: 记录测试结果和截图

### 🎯 智能断言
- 支持关键词匹配验证
- 可配置匹配策略（全部匹配/任意匹配）
- 详细的错误信息和日志

## 目录结构

```
testcases/test_ask_screen/
├── base_ask_screen_test.py          # 基础测试类
├── contact/                         # 联系人相关测试
│   ├── add_this_number.py
│   ├── contact_card.py
│   └── phone_number.py
├── math_calculation/                # 数学计算测试
│   ├── math_equation.py
│   └── math_calculation.py
├── translation/                     # 翻译功能测试
│   └── english_text.py
├── text_recognition/                # 文字识别测试
│   └── text_document.py
├── scene_understanding/             # 场景理解测试
│   └── scene_photo.py
└── general_qa/                      # 通用问答测试
    └── business_card.py
```

## 命令行使用

```bash
# 运行交互式生成器
python tools/ask_screen_test_generator.py

# 运行测试验证
python test_ask_screen_generator.py
```

## 配置说明

### 分类检测规则
生成器会根据命令中的关键词自动检测分类：
- 包含 "contact", "number", "phone" → contact
- 包含 "calculate", "math", "add" → math_calculation  
- 包含 "translate", "翻译" → translation
- 包含 "text", "read", "文字" → text_recognition
- 包含 "what", "identify" → object_detection
- 包含 "scene", "describe" → scene_understanding
- 其他 → general_qa

### 期望关键词建议
根据不同分类自动建议合适的期望关键词：
- contact: ["number", "contact", "save", "recognized"]
- math_calculation: ["result", "answer", "calculate", "equals"]
- translation: ["translation", "translate", "means"]
- 等等...

## 注意事项

1. **图片文件**: 确保测试图片存在于 `data/static/ask_screen/` 目录
2. **命名规范**: 图片名称建议使用下划线分隔，如 `add_this_number.png`
3. **依赖关系**: 需要先配置好 `file_pusher` 和相关页面对象
4. **测试环境**: 确保设备已连接且 Ask Screen 功能可用

## 示例输出

生成的测试用例示例：
```python
@allure.epic("Ella浮窗测试")
@allure.feature("Ask Screen功能") 
@allure.story("联系人相关")
class TestAskScreenAddNumber(SimpleAskScreenTest):
    def test_add_this_number(self, ella_floating_page):
        command = "add this number"
        expected_keywords = ['following number is recognized', 'save it as a contact']
        
        # 数据准备、命令执行、结果验证...
```

这个工具大大简化了 Ask Screen 测试用例的创建过程，提供了标准化、可维护的测试代码结构。
