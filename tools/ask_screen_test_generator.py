#!/usr/bin/env python3
"""
Ask Screen测试用例生成工具
基于Ella测试生成器，专门为Ask Screen浮窗功能生成测试用例
"""
import os
import sys
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from core.logger import log


class AskScreenTestGenerator:
    """Ask Screen测试用例生成器"""
    
    def __init__(self):
        self.project_root = project_root
        self.output_dir = self.project_root / "testcases" / "test_ask_screen"
        
        # Ask Screen 特定的分类
        self.ask_screen_categories = {
            "contact": "联系人相关",
            "image_analysis": "图片分析",
            "text_recognition": "文字识别", 
            "object_detection": "物体检测",
            "scene_understanding": "场景理解",
            "math_calculation": "数学计算",
            "translation": "翻译功能",
            "general_qa": "通用问答"
        }
    
    def detect_ask_screen_category(self, command: str, image_name: str = "") -> str:
        """
        检测Ask Screen命令的分类
        
        Args:
            command: 命令文本
            image_name: 图片名称（可选）
            
        Returns:
            str: 分类名称
        """
        command_lower = command.lower()
        image_lower = image_name.lower()
        
        # 联系人相关
        if any(keyword in command_lower for keyword in ["contact", "number", "phone", "call", "联系人", "电话", "号码"]):
            return "contact"
        
        # 数学计算
        if any(keyword in command_lower for keyword in ["calculate", "math", "add", "sum", "计算", "数学", "加", "减", "乘", "除"]):
            return "math_calculation"
        
        # 翻译功能
        if any(keyword in command_lower for keyword in ["translate", "翻译", "英文", "中文", "language"]):
            return "translation"
        
        # 文字识别
        if any(keyword in command_lower for keyword in ["text", "read", "文字", "识别", "阅读", "字"]):
            return "text_recognition"
        
        # 物体检测
        if any(keyword in command_lower for keyword in ["what", "identify", "detect", "这是什么", "识别", "检测"]):
            return "object_detection"
        
        # 场景理解
        if any(keyword in command_lower for keyword in ["scene", "where", "describe", "场景", "描述", "在哪里"]):
            return "scene_understanding"
        
        # 图片分析（通用）
        if any(keyword in command_lower for keyword in ["image", "picture", "photo", "图片", "照片", "分析"]):
            return "image_analysis"
        
        # 默认为通用问答
        return "general_qa"
    
    def generate_class_name(self, command: str, image_name: str = "") -> str:
        """生成类名"""
        # 提取字母并转换为标题格式
        def extract_letters_only(text: str) -> str:
            letters_only = re.sub(r'[^a-zA-Z\s]', ' ', text)
            words = letters_only.split()
            clean_words = [word.title() for word in words if word and word.lower() not in 
                          ["a", "the", "to", "and", "or", "of", "in", "on", "at", "by", "for", "with", "this", "that"]]
            return "".join(clean_words)
        
        # 优先使用图片名称生成类名
        if image_name:
            clean_image_name = extract_letters_only(image_name)
            if clean_image_name:
                return f"TestAskScreen{clean_image_name}"
        
        # 使用命令生成类名
        clean_command = extract_letters_only(command)
        return f"TestAskScreen{clean_command}" if clean_command else "TestAskScreenCommand"
    
    def generate_method_name(self, command: str, image_name: str = "") -> str:
        """生成方法名"""
        # 优先使用图片名称
        if image_name:
            method_base = re.sub(r'[^a-zA-Z\s]', ' ', image_name)
        else:
            method_base = re.sub(r'[^a-zA-Z\s]', ' ', command)
        
        method_name = method_base.lower().replace(" ", "_")
        # 移除连续的下划线
        while "__" in method_name:
            method_name = method_name.replace("__", "_")
        method_name = method_name.strip("_")
        
        return f"test_{method_name}" if method_name else "test_ask_screen_command"
    
    def generate_file_name(self, command: str, image_name: str = "") -> str:
        """生成文件名"""
        if image_name:
            # 使用图片名称作为文件名
            file_base = re.sub(r'[^a-zA-Z0-9\s]', ' ', image_name)
            file_name = file_base.lower().replace(" ", "_").strip("_")
            return f"{file_name}.py" if file_name else "ask_screen_test.py"
        else:
            # 使用命令生成文件名
            method_name = self.generate_method_name(command)
            return f"{method_name}.py"
    
    def suggest_expected_keywords(self, command: str, category: str) -> List[str]:
        """根据命令和分类建议期望关键词"""
        command_lower = command.lower()
        
        # 根据分类提供默认关键词
        category_keywords = {
            "contact": ["number", "contact", "save", "recognized"],
            "math_calculation": ["result", "answer", "calculate", "equals"],
            "translation": ["translation", "translate", "means"],
            "text_recognition": ["text", "read", "recognized", "content"],
            "object_detection": ["this is", "identify", "detect", "object"],
            "scene_understanding": ["scene", "location", "place", "environment"],
            "image_analysis": ["image", "picture", "analysis", "shows"],
            "general_qa": ["answer", "response", "information"]
        }
        
        base_keywords = category_keywords.get(category, ["response", "answer"])
        
        # 根据具体命令添加特定关键词
        if "add" in command_lower and "number" in command_lower:
            return ["following number is recognized", "save it as a contact"]
        elif "calculate" in command_lower or "math" in command_lower:
            return ["result", "answer", "calculation"]
        elif "translate" in command_lower:
            return ["translation", "means", "language"]
        elif "what" in command_lower:
            return ["this is", "identify", "shows"]
        
        return base_keywords

    def generate_ask_screen_template(self, command: str, class_name: str, method_name: str,
                                   expected_keywords: List[str], image_name: str = "",
                                   category: str = "general_qa") -> str:
        """生成Ask Screen测试模板"""

        # 生成测试描述
        category_desc = self.ask_screen_categories.get(category, "Ask Screen功能")

        template = f'''import pytest
import allure
from testcases.test_ask_screen.base_ask_screen_test import SimpleAskScreenTest
from pages.apps.ai_gallery.photos_page import AiGalleryPhotosPage


@allure.epic("Ella浮窗测试")
@allure.feature("Ask Screen功能")
@allure.story("{category_desc}")
class {class_name}(SimpleAskScreenTest):
    """
    Ask Screen测试类 - {category_desc}
    命令: {command}
    图片: {image_name or "无"}
    """

    @allure.title("测试{command}")
    @allure.description("测试Ask Screen功能: {command}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def {method_name}(self, ella_floating_page):
        """测试{command}命令"""
        command = "{command}"
        expected_keywords = {expected_keywords}

        # 数据准备
        with allure.step("准备测试数据"):'''

        # 如果有图片名称，添加图片推送逻辑
        if image_name:
            template += f'''
            from tools.file_pusher import file_pusher
            # 推送测试图片到设备
            push_result = file_pusher.push_ask_screen_image("{image_name}")
            assert push_result, f"推送图片失败: {image_name}"

            # 打开图库并选择图片
            photos_page = AiGalleryPhotosPage()
            result = photos_page.start_app()
            if result:
                photos_page.wait_for_page_load()
                photos_page.click_photo()'''
        else:
            template += '''
            # 无需特殊数据准备
            pass'''

        template += f'''

        # 执行命令并验证
        with allure.step(f"执行Ask Screen命令: {{command}}"):
            success, response_texts, verification_result = self.simple_floating_command_test(
                ella_floating_page, command, expected_keywords, verify_response=True
            )

        # 断言结果
        with allure.step("验证测试结果"):
            assert success, f"命令执行失败: {{command}}"
            assert response_texts, "未获取到响应文本"

            # 验证响应包含期望关键词（至少匹配一个）
            self.assert_floating_response_contains(response_texts, expected_keywords, match_all=False)

        with allure.step("记录测试完成"):
            log.info(f"✅ Ask Screen测试完成: {{command}}")
'''
        return template

    def generate_test_case(self, command: str, image_name: str = "",
                          expected_keywords: List[str] = None,
                          category: str = None, output_path: str = None) -> str:
        """
        生成Ask Screen测试用例

        Args:
            command: 要测试的命令
            image_name: 图片名称（可选）
            expected_keywords: 期望关键词列表（可选）
            category: 分类（可选，自动检测）
            output_path: 输出路径（可选）

        Returns:
            str: 生成的文件路径
        """
        log.info(f"🚀 开始生成Ask Screen测试用例: {command}")

        # 自动检测分类
        if category is None:
            category = self.detect_ask_screen_category(command, image_name)

        # 生成各种名称
        class_name = self.generate_class_name(command, image_name)
        method_name = self.generate_method_name(command, image_name)
        file_name = self.generate_file_name(command, image_name)

        # 处理期望关键词
        if expected_keywords is None:
            expected_keywords = self.suggest_expected_keywords(command, category)

        log.info(f"分类: {category}")
        log.info(f"类名: {class_name}")
        log.info(f"方法名: {method_name}")
        log.info(f"文件名: {file_name}")
        log.info(f"期望关键词: {expected_keywords}")

        # 生成代码
        code = self.generate_ask_screen_template(
            command, class_name, method_name, expected_keywords, image_name, category
        )

        # 确定输出路径
        if output_path:
            file_path = Path(output_path)
        else:
            # 根据分类创建目录
            category_dir = self.output_dir / category
            category_dir.mkdir(parents=True, exist_ok=True)
            file_path = category_dir / file_name

        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(code)

        log.info(f"✅ Ask Screen测试用例已生成: {file_path}")
        return str(file_path)

    def batch_generate_from_images(self, image_commands: List[Tuple[str, str]],
                                  base_category: str = None) -> List[str]:
        """
        从图片和命令列表批量生成测试用例

        Args:
            image_commands: (图片名称, 命令) 的元组列表
            base_category: 基础分类（可选，会自动检测）

        Returns:
            List[str]: 生成的文件路径列表
        """
        generated_files = []

        for image_name, command in image_commands:
            try:
                file_path = self.generate_test_case(
                    command=command,
                    image_name=image_name,
                    category=base_category
                )
                generated_files.append(file_path)
                print(f"✅ {image_name} + {command} -> {file_path}")
            except Exception as e:
                print(f"❌ 生成失败 '{image_name} + {command}': {e}")

        return generated_files

    def generate_from_ask_screen_folder(self, folder_path: str = None,
                                       command_mapping: Dict[str, str] = None) -> List[str]:
        """
        从ask_screen文件夹中的图片自动生成测试用例

        Args:
            folder_path: ask_screen文件夹路径（可选，默认使用项目路径）
            command_mapping: 图片名称到命令的映射（可选，会自动生成）

        Returns:
            List[str]: 生成的文件路径列表
        """
        if folder_path is None:
            folder_path = self.project_root / "data" / "static" / "ask_screen"
        else:
            folder_path = Path(folder_path)

        if not folder_path.exists():
            log.error(f"ask_screen文件夹不存在: {folder_path}")
            return []

        # 获取所有图片文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp']
        image_files = []

        for ext in image_extensions:
            image_files.extend(folder_path.glob(f"*{ext}"))
            image_files.extend(folder_path.glob(f"*{ext.upper()}"))

        if not image_files:
            log.warning(f"未找到图片文件: {folder_path}")
            return []

        generated_files = []

        for image_file in image_files:
            try:
                image_name = image_file.stem  # 不含扩展名的文件名

                # 生成或获取命令
                if command_mapping and image_name in command_mapping:
                    command = command_mapping[image_name]
                else:
                    # 自动生成命令（将下划线和连字符替换为空格）
                    command = image_name.replace('_', ' ').replace('-', ' ')

                file_path = self.generate_test_case(
                    command=command,
                    image_name=image_name
                )
                generated_files.append(file_path)
                print(f"✅ {image_name} -> {command} -> {file_path}")

            except Exception as e:
                print(f"❌ 处理图片失败 '{image_file.name}': {e}")

        log.info(f"🎉 从ask_screen文件夹批量生成完成，共生成 {len(generated_files)} 个测试用例")
        return generated_files


# ==================== 便捷函数 ====================

def generate_ask_screen_test(command: str, image_name: str = "",
                           expected_keywords: List[str] = None,
                           category: str = None) -> str:
    """
    便捷函数：生成单个Ask Screen测试用例

    Args:
        command: 要测试的命令
        image_name: 图片名称（可选）
        expected_keywords: 期望关键词列表（可选）
        category: 分类（可选）

    Returns:
        str: 生成的文件路径
    """
    generator = AskScreenTestGenerator()
    return generator.generate_test_case(command, image_name, expected_keywords, category)


def batch_generate_ask_screen_tests(image_commands: List[Tuple[str, str]]) -> List[str]:
    """
    便捷函数：批量生成Ask Screen测试用例

    Args:
        image_commands: (图片名称, 命令) 的元组列表

    Returns:
        List[str]: 生成的文件路径列表
    """
    generator = AskScreenTestGenerator()
    return generator.batch_generate_from_images(image_commands)


def auto_generate_from_images(folder_path: str = None,
                             command_mapping: Dict[str, str] = None) -> List[str]:
    """
    便捷函数：从图片文件夹自动生成测试用例

    Args:
        folder_path: ask_screen文件夹路径（可选）
        command_mapping: 图片名称到命令的映射（可选）

    Returns:
        List[str]: 生成的文件路径列表
    """
    generator = AskScreenTestGenerator()
    return generator.generate_from_ask_screen_folder(folder_path, command_mapping)


def main():
    """主函数 - 交互式生成器"""
    generator = AskScreenTestGenerator()

    print("🚀 Ask Screen测试用例生成工具")
    print("=" * 50)
    print("💡 专门为Ella浮窗Ask Screen功能生成测试用例")
    print(f"📁 输出目录: {generator.output_dir}")
    print(f"📂 支持的分类: {list(generator.ask_screen_categories.keys())}")

    while True:
        print("\n请选择操作:")
        print("1. 生成单个测试用例")
        print("2. 批量生成测试用例")
        print("3. 从图片文件夹自动生成")
        print("4. 退出")

        choice = input("请选择 (1-4): ").strip()

        if choice == "1":
            # 单个测试用例生成
            command = input("请输入命令: ").strip()
            if not command:
                print("❌ 命令不能为空")
                continue

            image_name = input("请输入图片名称 (可选): ").strip()

            try:
                file_path = generator.generate_test_case(command, image_name)
                print(f"✅ 测试用例已生成: {file_path}")
            except Exception as e:
                print(f"❌ 生成失败: {e}")

        elif choice == "2":
            # 批量生成
            print("请输入图片名称和命令对 (格式: 图片名称,命令)，输入空行结束:")
            image_commands = []

            while True:
                line = input("图片名称,命令: ").strip()
                if not line:
                    break

                if ',' in line:
                    parts = line.split(',', 1)
                    image_name = parts[0].strip()
                    command = parts[1].strip()
                    image_commands.append((image_name, command))
                else:
                    print("❌ 格式错误，请使用: 图片名称,命令")

            if image_commands:
                try:
                    generated_files = generator.batch_generate_from_images(image_commands)
                    print(f"🎉 批量生成完成，共生成 {len(generated_files)} 个测试用例")
                except Exception as e:
                    print(f"❌ 批量生成失败: {e}")
            else:
                print("❌ 未输入任何有效的图片命令对")

        elif choice == "3":
            # 从图片文件夹自动生成
            folder_path = input("请输入ask_screen文件夹路径 (留空使用默认): ").strip()
            folder_path = folder_path if folder_path else None

            try:
                generated_files = generator.generate_from_ask_screen_folder(folder_path)
                print(f"🎉 自动生成完成，共生成 {len(generated_files)} 个测试用例")
            except Exception as e:
                print(f"❌ 自动生成失败: {e}")

        elif choice == "4":
            print("👋 再见!")
            break

        else:
            print("❌ 无效选择，请输入 1-4")


# 使用示例
if __name__ == "__main__":
    # 示例1: 生成单个测试用例
    print("🚀 示例1: 生成单个Ask Screen测试用例")
    file_path = generate_ask_screen_test(
        command="Organize the text on this image and add it to my notes",
        image_name="organize_the_text_on_this_image_and_add_it_to_my_notes",
        expected_keywords=["Done"]
    )
    print(f"✅ 生成完成: {file_path}")

    # # 示例2: 批量生成测试用例
    # print("\n🚀 示例2: 批量生成Ask Screen测试用例")
    # image_commands = [
    #     ("math_problem", "solve this math problem"),
    #     ("text_document", "read this text"),
    #     ("contact_card", "add this contact")
    # ]
    # generated_files = batch_generate_ask_screen_tests(image_commands)
    # print(f"🎉 批量生成完成，共生成 {len(generated_files)} 个文件")
    #
    # # 示例3: 从图片文件夹自动生成
    # print("\n🚀 示例3: 从图片文件夹自动生成测试用例")
    # # auto_generated_files = auto_generate_from_images()
    # # print(f"🎉 自动生成完成，共生成 {len(auto_generated_files)} 个文件")

    # 运行交互式生成器
    # main()
